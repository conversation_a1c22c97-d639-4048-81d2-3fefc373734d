{ This file was automatically created by <PERSON>. Do not edit!
  This source is only used to compile and install the package.
 }

unit castle_engine_lcl;

{$warn 5023 off : no warning about unused units}
interface

uses
  CastleControl, CastleDialogs, CastleInternalExposeTransformsDialog, 
  CastleLCLRecentFiles, CastleLCLUtils, CastlePropEdits, 
  CastleInternalLclDesign, CastleEditorAccess, 
  CastleInternalTiledLayersDialog, CastleInternalRegionDialog, 
  LazarusPackageIntf;

implementation

procedure Register;
begin
  RegisterUnit('CastleControl', @CastleControl.Register);
  RegisterUnit('CastleDialogs', @CastleDialogs.Register);
  RegisterUnit('CastlePropEdits', @CastlePropEdits.Register);
  RegisterUnit('CastleInternalLclDesign', @CastleInternalLclDesign.Register);
end;

initialization
  RegisterPackage('castle_engine_lcl', @Register);
end.
