{ This file was automatically created by <PERSON>. Do not edit!
  This source is only used to compile and install the package.
 }

unit castle_engine_editor_components;

{$warn 5023 off : no warning about unused units}
interface

uses
  BAxisColorPicker, BColorPicker, CColorPicker, CIEAColorPicker, 
  CIEBColorPicker, CIELColorPicker, GAxisColorPicker, GColorPicker, 
  HColorPicker, HexaColorPicker, HRingPicker, HSCirclePicker, HSColorPicker, 
  HSLColorPicker, HSLRingPicker, HTMLColors, KColorPicker, LVColorPicker, 
  mbBasicPicker, mbColorConv, mbColorList, mbColorPalette, 
  mbColorPickerControl, mbColorPreview, mbColorTree, mbDeskPickerButton, 
  mbOfficeColorDialog, mbReg, mbTrackBarPicker, mbUtils, MColorPicker, 
  OfficeMoreColorsDialog, PalUtils, RAxisColorPicker, RColorPicker, 
  RGBCIEUtils, RGBCMYKUtils, RGBHSLUtils, RGBHSVUtils, Scanlines, 
  SColorPicker, ScreenWin, SelPropUtils, SLColorPicker, SLHColorPicker, 
  YColorPicker, CastleEditorPropEdits, FormCastleColorPicker, 
  FormLayerCollisionsPropertyEditor, FormPhysicsLayerNamesPropertyEditor, 
  LazarusPackageIntf;

implementation

procedure Register;
begin
  RegisterUnit('mbReg', @mbReg.Register);
  RegisterUnit('CastleEditorPropEdits', @CastleEditorPropEdits.Register);
end;

initialization
  RegisterPackage('castle_engine_editor_components', @Register);
end.
