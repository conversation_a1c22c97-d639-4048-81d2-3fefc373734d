{ This file was automatically created by <PERSON>. Do not edit!
  This source is only used to compile and install the package.
 }

unit castle_engine_base;

{$warn 5023 off : no warning about unused units}
interface

uses
  CastleInternalAbstractSoundBackend, CastleInternalSoundFile, 
  CastleInternalSoxSoundBackend, CastleSoundBase, CastleSoundEngine, 
  CastleFMODSoundBackend, CastleInternalFMOD, CastleInternalOgg, 
  CastleInternalVorbisCodec, CastleInternalVorbisDecoder, 
  CastleInternalVorbisFile, CastleInternalALUtils, CastleInternalEFX, 
  CastleInternalOpenAL, CastleOpenALSoundBackend, CastleApplicationProperties, 
  CastleClassUtils, CastleColors, CastleDynLib, CastleInternalClassUtils, 
  CastleInternalGzio, CastleInternalRttiUtils, CastleInternalZLib, 
  CastleInternalZStream, CastleLog, CastleMessaging, CastleParameters, 
  CastleProjection, CastleQuaternions, CastleRectangles, CastleRenderOptions, 
  CastleStreamUtils, CastleStringUtils, CastleSystemLanguage, CastleTimeUtils, 
  CastleUnicode, CastleUtils, CastleVectors, CastleVectorsInternalDouble, 
  CastleVectorsInternalSingle, CastleGLES, CastleGLImages, CastleGLShaders, 
  CastleGLUtils, CastleGLVersion, CastleInternalContextBase, 
  CastleInternalContextEgl, CastleInternalContextGlx, 
  CastleInternalContextWgl, CastleInternalEgl, CastleInternalGLUtils, 
  CastleRenderContext, CastleRenderPrimitives, CastleGL, CastleCurves, 
  CastleScript, CastleScriptArrays, CastleScriptCoreFunctions, 
  CastleScriptImages, CastleScriptLexer, CastleScriptParser, 
  CastleScriptVectors, CastleScriptXML, CastleInternalUseDeprecatedUnits, 
  CastleUIState, CastleComponentSerialize, CastleConfig, CastleDownload, 
  CastleFileFilters, CastleFilesUtils, CastleFindFiles, CastleInternalDataUri, 
  CastleInternalDirectoryInformation, CastleInternalFileMonitor, 
  CastleInternalUrlUtils, CastleLocalizationGetText, CastleRecentFiles, 
  CastleUriUtils, CastleXMLCfgInternal, CastleXMLConfig, CastleXmlUtils, 
  CastleZip, CastleInternalArchitectures, CastleInternalProjectLocalSettings, 
  CastleInternalTools, CastleFonts, CastleInternalFreeType, 
  CastleInternalFreeTypeH, CastleInternalRichText, 
  CastleTextureFont_Default3D_Sans, CastleTextureFont_DefaultUi, 
  CastleTextureFontData, CastleImages, CastleInternalAutoGenerated, 
  CastleInternalCompositeImage, CastleInternalDataCompression, 
  CastleInternalPng, CastleTextureImages, CastleVideos, 
  CastleInternalKraftOverrides, kraft, CastleDebugTransform, 
  CastleInternalArraysGenerator, CastleInternalBackgroundRenderer, 
  CastleInternalBatchShapes, CastleInternalGLCubeMaps, 
  CastleInternalMaterialProperties, CastleInternalNodeInterpolator, 
  CastleInternalNoise, CastleInternalNormals, CastleInternalOcclusionCulling, 
  CastleInternalRenderer, CastleInternalScreenEffects, 
  CastleInternalShadowMaps, CastleInternalShapeOctree, 
  CastleInternalShapesRenderer, CastleInternalSpriteSheet, 
  CastleInternalTransformData, CastleInternalTriangleOctree, 
  CastleInternalX3DLexer, CastleInternalX3DScript, CastleLivingBehaviors, 
  CastleRayTracer, CastleRendererInternalLights, CastleRendererInternalShader, 
  CastleRendererInternalTextureEnv, CastleScene, CastleSceneCore, 
  CastleSceneInternalBlending, CastleSceneInternalShape, CastleScreenEffects, 
  CastleShapeInternalRenderShadowVolumes, CastleShapeInternalShadowVolumes, 
  CastleShapes, CastleTerrain, CastleThirdPersonNavigation, CastleTiledMap, 
  CastleTransformManipulate, CastleViewport, CastleLoadGltf, 
  X3DLoadInternalCollada, CastleIfc, X3DLoadInternalMD3, CastlePasDblStrUtils, 
  CastlePasGLTF, CastlePasJSON, X3DLoadInternalSpine, X3DLoad, 
  X3DLoadInternal3DS, X3DLoadInternalCocos2d, X3DLoadInternalGEO, 
  X3DLoadInternalGltf, X3DLoadInternalImage, X3DLoadInternalOBJ, 
  X3DLoadInternalSTL, X3DLoadInternalTiledMap, X3DLoadInternalUtils, 
  CastleInternalNodesUnsupported, X3DFields, X3DNodes, X3DCameraUtils, 
  X3DTime, CastleActivityRecognition, CastleAds, CastleAnalytics, 
  CastleFacebook, CastleGameService, CastleHelpshift, CastleInAppPurchases, 
  CastleOpenDocument, CastlePhotoService, CastleTenjin, CastleTestFairy, 
  CastleInternalSteamApi, CastleSteam, CastleBehaviors, CastleBoxes, 
  CastleCameras, CastleFrustum, CastleInternalBaseTriangleOctree, 
  CastleInternalCubeMaps, CastleInternalGeometryArrays, 
  CastleInternalGLShadowVolumes, CastleInternalOctree, 
  CastleInternalPhysicsVisualization, CastleInternalRays, 
  CastleInternalSpaceFillingCurves, CastleInternalSphereSampling, 
  CastleInternalSphericalHarmonics, CastleSectors, CastleTransform, 
  CastleTriangles, CastleTriangulate, CastleControls, CastleDialogViews, 
  CastleFlashEffect, CastleInputs, CastleInternalCameraGestures, 
  CastleInternalControlsImages, CastleInternalInspector, 
  CastleInternalGameControllersExplicit, CastleInternalPk3DConnexion, 
  CastleInternalSettings, CastleGameControllers, CastleKeysMouse, 
  CastleNotifications, CastleUIControls, ImagingExtFileFormats, Imaging, 
  ImagingBitmap, ImagingCanvases, ImagingClasses, ImagingColors, 
  ImagingComponents, ImagingDds, ImagingFormats, ImagingGif, ImagingIO, 
  ImagingJpeg, ImagingNetworkGraphics, ImagingPortableMaps, ImagingRadiance, 
  ImagingTarga, ImagingTypes, ImagingUtility, CastleInternalPrimitiveMaterial, 
  LazarusPackageIntf;

implementation

procedure Register;
begin
end;

initialization
  RegisterPackage('castle_engine_base', @Register);
end.
