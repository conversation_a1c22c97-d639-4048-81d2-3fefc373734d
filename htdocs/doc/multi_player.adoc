# Multi-player
include::common.adoc[]
:description: TODO
:cge-social-share-image: not_quake_1.png

== Introduction

_Castle Game Engine_ does not have (right now) dedicated high-level components for multi-player games. We provide:

- Building blocks, like link:url[using URLs for all resource access] and _asynchronous HTTP requests_ using cgeref:TCastleDownload[].

- And we provide integrations (with examples) with many 3rd-party networking libraries.

cgeimg::block[
  not_quake_1.png|"Not Quake" demo - real-time network game,
  mormot_example_screenshot1.png|Collaborative editing of 3D world using Castle Game Engine and mORMot2,
  server_and_2_clients.png|TCP server and 2 clients\, on Windows
]

== Use TCastleDownload for HTTP REST communication with a backend

The engine provides a cross-platform component cgeref:TCastleDownload[] to asynchronously download any URL and to perform HTTP(S) web requests. In effect, this can be used to communicate with any backend (written using Pascal or not) that uses HTTP(S) protocol.

Features of cgeref:TCastleDownload[]:

- Supports various HTTP(S) methods (GET, POST, PUT...).

- Handles HTTP(S) redirects automatically.

- Allows to send custom HTTP headers.

- Gets MIME type from server automatically.

- Exposes HTTP response headers and code.

- It is really cross-platform and cross-compiler, covering all platforms supported by _Castle Game Engine_. E.g. it uses https://wiki.freepascal.org/fphttpclient[FpHttpClient] with FPC on most desktops, uses special https://github.com/castle-engine/castle-engine/blob/master/tools/build-tool/data/android/services/download_urls/README.adoc[Android service on Android], on Delphi uses _Indy_ or `TNetHTTPClient` (depending on what works better on given platform).

- Supports encrypted HTTPS out-of-the-box as much as possible, since HTTPS is standard nowadays. To this end, we adjust some _Indy_ and _FpHttpClient_ to make HTTPS just work.
+
FPC applications only have to use `OpenSSLSockets` unit, e.g. add
+
[source, pascal]
----
{$ifdef FPC} OpenSSLSockets, {$endif}
----
+
to the uses clause of one of your units (like `GameInitialize`).

=== Examples: asynchronous downloading, remote logging (HTTP POST), HTTP PUT, talking with OpenAI API, more

We maintain a number of examples of using cgeref:TCastleDownload[], both in main engine repository and in our repos:

- https://github.com/castle-engine/castle-engine/tree/master/examples/network/asynchronous_download[examples/network/asynchronous_download/] (asynchronously download multiple URLs),

- https://github.com/castle-engine/castle-engine/blob/master/examples/network/remote_logging/[examples/network/remote_logging/] (sends asynchronous HTTP POST message),

- https://github.com/castle-engine/castle-engine/tree/master/examples/network/put_data[examples/network/put_data/] (send HTTP PUT),

- https://github.com/castle-engine/castle-engine/blob/master/tools/castle-editor/code/castlesketchfab.pas[CastleSketchfab] is used by the link:editor[] to communicate with link:sketchfab[Sketchfab] API, to easily search and download Sketchfab models,

- https://github.com/castle-engine/castle-openai[castle-openai shows talking with an OpenAI assistant (essentially your own, customized ChatGPT)] (sends a series of HTTP POST and GET messages to talk with OpenAI server).

[#indy]
== CastleClientServer unit (using Indy underneath)

=== Overview

Our cgeref:CastleClientServer[] unit provides a cross-platform client-server message passing solution.

cgeimg::block[
  android_client.png|TCP client\, on Android,
  server_linux.png|TCP server\, on Linux,
  server_and_2_clients.png|TCP server and 2 clients\, on Windows
]

It's a good cross-platform solution when:

- The client/server architecture fits your design. That is: one player "hosts" a game, and everyone can reach the host over IP (which typically means that either 1. the host IP, and the relevant port, are accessible publicly on the Internet, 2. or that everyone is within the same local network).

- You want reliability (not maximum speed), since it uses TCP connection in a standard fashion.

- cgeref:CastleClientServer[] and Indy use standard TCP connection in a standard way, which is good for simplicity and interoperability. E.g. you could develop a server in Java or C++ if needed, to communicate with Pascal clients.

cgeref:CastleClientServer[] underneath uses https://www.indyproject.org/[Indy] (with threads) on most platforms, except on Android where we utilize dedicated asynchronous Android API for this.

=== Example: multiple clients talk to a server

The https://github.com/castle-engine/castle-engine/tree/master/examples/network/tcp_connection[examples/network/tcp_connection] directory in CGE sources demonstrates how to create and use a classic client/server solution, where multiple clients talk to a server over a TCP/IP connection.

=== Get Indy

To compile code using cgeref:CastleClientServer[] FPC/Lazarus, *be sure to install Indy* using one of the options below, and then also compile in Lazarus package `castle_engine_indy`. To install Indy:

- You can install Indy through the https://wiki.freepascal.org/Online_Package_Manager[Online Package Manager]. The OPM is a great way to install Lazarus packages in general, go ahead and try it :)

- You can install "indy" module using https://castle-engine.io/fpcupdeluxe[fpcupdeluxe]. You can install your own FPC and Lazarus using fpcupdeluxe, and add an "indy" module to it.

- Alternatively, you can download Indy from link:http://packages.lazarus-ide.org/[packages.lazarus-ide.org (same packages that OPM uses)] . Search for "indy" there, download and unpack the zip, open the package `indylaz.lpk` inside Lazarus and compile it. Here's a command-line version:

```
wget http://packages.lazarus-ide.org/Indy10.zip
unzip Indy10.zip
lazbuild Indy10/indylaz.lpk
```

In all cases, you should get an additional package `indylaz` known by Lazarus. Remember to also install `packages/lazarus/castle_engine_indy.lpk` package, and use it in your projects.

link:delphi[Delphi] users don't need to do anything in this regard, as Indy is already included in Delphi.

=== Note: There are known memory leaks when using Indy

All applications using Indy (whether with _Castle Game Engine_ or not) have memory leaks, by default. This is a design choice of Indy -- letting the memory leak was better than crashing in some edge-cases at unit finalization.

If you are sure that all the Indy threads are given sufficient time to terminate gracefully before the application exits, you can define the symbol `FREE_ON_FINAL` in Indy sources. Search for `define FREE_ON_FINAL` inside Indy sources, as of now it means you will find 5 include files doing this:

[source, pascal]
----
{.$DEFINE FREE_ON_FINAL}
{$UNDEF FREE_ON_FINAL}
----

You need to change these lines to

[source, pascal]
----
{$DEFINE FREE_ON_FINAL}
{.$UNDEF FREE_ON_FINAL}
----

cgeimg::block[
  indy_free_on_final.png|FREE_ON_FINAL in Indy sources
]

Please note that it isn't always easy to guarantee that _"threads are given sufficient time to terminate gracefully before the application exits"_. It means that disconnecting (by clients) and stopping (by server) cannot be done right when the application exits (or you have to wait for it to finish, potentially hanging your application on exit). Basically, Indy developers had a good reason to not enable this by default.

For similar reason, the internal thread inside cgeref:CastleClientServer[] actually may leak memory in case it didn't have time to terminate gracefully before the application exits. (But in our case, by default, it will not leak if exits cleanly, i.e. you disconnect client before application exit. It only leaks if the application exits too quickly, disconnecting the client.)

Note: There's a code in Indy to "register known leak" with some memory managers, but it is not active by default for FPC.

You can test for memory leaks following link:memory_leaks[our instructions about memory leaks]. We use FPC HeapTrc to detect memory leaks, and you can just set `<compiler_options detect_memory_leaks="true">` in link:project_manifest[CastleEngineManifest.xml] to enable it.

=== Note: Code completion using LSP (e.g. in VS Code) when using Indy

When you edit a project that uses cgeref:CastleClientServer[] with link:vscode[VS Code] (or other editor using our https://github.com/castle-engine/pascal-language-server[LSP]) you may see errors like: _"unit not found: IdGlobal"_.

To solve this, you need to tell our LSP where are your Indy units. To do this, create a file:

- On Unix: `$HOME/.config/pasls/castle-pasls.ini`

- On Windows: `C:/Users/<USER>/AppData/Local/pasls/castle-pasls.ini`

and inside put these lines:

```
[extra_options]
;; Specify as many extra FPC options as you want.
;; Each extra option must have a consecutive number, start from 1.
option_1=-Fu/home/<USER>/src/Indy10/Core/
option_2=-Fu/home/<USER>/src/Indy10/System/
```

Adjust the above paths (`/home/<USER>/src/Indy10`) to your system to point to the directory where you have Indy sources.

For more information, see https://github.com/castle-engine/pascal-language-server?tab=readme-ov-file#extra-configuration-in-castle-enginepascal-language-server[README about Extra configuration in castle-engine/pascal-language-server].

[#mormot]
== mORMot 2

cgeimg::block[
  mormot_example_screenshot1.png|Collaborative editing of 3D world using Castle Game Engine and mORMot2,
  mormot_example_screenshot2.png|Collaborative editing of 3D world using Castle Game Engine and mORMot2,
  mormot_example_screenshot3.png|Collaborative editing of 3D world using Castle Game Engine and mORMot2
]

https://github.com/synopse/mORMot2/[mORMot2] is a feature-rich Object Pascal framework for developing network applications. It can support traditional client-server communication (exchanging messages), ORM (where you pass around objects that can be easily synchronized over the network and can be saved/loaded from the database), and more. It has a number of high-level (like ORM) and low-level (like database access) utilities to make programming of client-server (and other!) applications easier.

=== Example: edit 3D world online and persist it to database

See our example integration of Castle Game Engine with https://github.com/synopse/mORMot2/[mORMot2] in https://github.com/castle-engine/castle-and-mormot-collaborative-editing/[collaborative editing of 3D world using Castle Game Engine and mORMot2].

[#rnl]
== RNL

cgeimg::block[
  not_quake_1.png|"Not Quake" demo - real-time network game,
  not_quake_2.png|"Not Quake" demo - real-time network game,
  not_quake_3.png|"Not Quake" demo - real-time network game,
  not_quake_4.png|"Not Quake" demo - real-time network game
]

https://github.com/BeRo1985/rnl[RNL (Realtime Network Library)] is an open-source, cross-platform, reliable UDP network library, for FPC and Delphi.
//, , developed by https://www.patreon.com/bero[Benjamin Rosseaux] .
// ^ we don't clarify authors for other libraries
If you want to make real-time communication over a network (e.g. for a fast-paced shooter games) this may be a good choice.

=== Example: real-time online shooter "Not Quake"

https://github.com/castle-engine/not-quake[Not Quake] is an example of online first-person shooter, developed using _Castle Game Engine_ and RNL. You can

- https://github.com/castle-engine/not-quake[Get source code and binary releases of client and server from GitHub]

- https://cat-astrophe-games.itch.io/not-quake[Get binary releases of client from Itch.io]

- https://castle-engine.io/wp/2022/06/24/not-quake-an-example-of-real-time-multi-player-shooter-a-bit-like-quake-using-castle-game-engine-and-rnl/[Read the news post]

== More options

There are various other networking solutions for Pascal -- and you can use any of them together with _Castle Game Engine_.

- Aforementioned https://www.indyproject.org/[Indy] is a big library providing a lot of networking options. You can use it directly in many ways, you don't need to use our cgeref:CastleClientServer[] API. See the link:http://ww2.indyproject.org/docsite/html/frames.html[online documentation].

- link:http://ararat.cz/synapse/doku.php[Synapse] is a cross-platform networking library. See also https://wiki.freepascal.org/Synapse[FPC wiki about Synapse].

- https://lnet.wordpress.com/news/[lNet] is a cross-platform lightweight networking library for FPC. It's much smaller (in terms of API and implementation) than _Synapse_ and _Indy_, which may be an advantage, depending on what you need. See https://lnet.wordpress.com/usage/faq/[lNet FAQ] and https://wiki.freepascal.org/lNet[FPC wiki about lNET].

- FPC includes various networking units in the standard installation already. They work at various levels. In particular if you just want HTTP (REST) networking, FPC has https://wiki.freepascal.org/fcl-web[fcl-web] which allows to create HTTP(S) servers and clients.

NOTE: that you can also make https://en.wikipedia.org/wiki/Hotseat_(multiplayer_mode)[hot seat] and https://en.wikipedia.org/wiki/Split_screen_(video_games)[split screen] games, in which case multiple people just play on the same computer. We fully support multiple joysticks connected to a single desktop application, and connecting / disconnecting them at runtime, which allows to handle input from multiple people in one game.

== Plans: Nakama Integration

We plan link:roadmap#_integration_with_nakama_scalable_server_for_social_and_real_time_games_and_apps[integration with Nakama] in the future. It's a great open-source solution that provides out-of-the-box common multi-player features (like user accounts, storing user data, multi-player rooms) and can be customized to each particular project.

We also want to enable easy synchronization of game state between multiple clients on top of _Nakama_.
