# Engine on a form (VCL, FMX, LCL) using TCastleControl
include::common.adoc[]
:description: Embed Castle Game Engine rendering and processing inside a Lazarus (LCL) or Delphi (VCL, FMX) form.
:cge-social-share-image: delphi_vcl_1.png

## Introduction

cgeref:TCastleControl[] is a component you can drop on a form. It allows to do all CGE rendering and processing as part of a form, and cgeref:TCastleControl[] can be surrounded on your form by any other components.

The same component is available for Lazarus (LCL) and Delphi (VCL, FMX) forms.

cgeimg::block[
  delphi_vcl_1.png|TCastleControl on Delphi VCL form,
  lazarus_control_run.png|3D model viewer using TCastleControl on LCL
]

## Delphi installation

1. Install the Delphi packages following the link:delphi_packages[Delphi packages installation instructions].
+
Make sure to use the menu item _"Tools -> Castle Game Engine -> Configure Delphi to Use Engine"_ as described on that page, to have CGE units available for all your projects.

2. Test: Open the examples in `examples/delphi`, there's one example for VCL one for FMX.
+
These are regular Delphi projects, open them in Delphi, compile and run (for Windows 32-bit or 64-bit).
+
You can also open each example in CGE editor (point the CGE editor to the `CastleEngineManifest.xml` file inside the example directory), to edit the 3D / 2D / UI designs there.

### Screenshots

cgeimg::block[
  delphi_vcl_1.png|TCastleControl on Delphi VCL form,
  delphi_vcl_2.png|TCastleControl on Delphi VCL form,
  delphi_vcl_3.png|TCastleControl on Delphi VCL form,
  delphi_fmx_1.png|TCastleControl on Delphi FMX form,
  delphi_fmx_2.png|TCastleControl on Delphi FMX form,
  delphi_fmx_3.png|TCastleControl on Delphi FMX form
]

## Lazarus installation

. Install in Lazarus the package `castle_engine_lcl.lpk`. In the package dialog, use the option to _"Install"_ (under the _"Use"_ button).
+
Note: Installing the `castle_engine_lcl` package will also automatically install the package `castle_engine_base`, as a dependency. That's good.
+
--
cgeimg::block[
  lazarus-installnew-31.png|castle_engine_lcl: Choose the file,
  lazarus-installnew-32.png|castle_engine_lcl: Install,
  lazarus-installnew-33.png|castle_engine_lcl: Confirm Lazarus rebuild
]
--
+
Once `castle_engine_lcl.lpk` is successfully installed, Lazarus restarts, and you should see the _"Castle"_ tab with our components.

. To test, open in Lazarus examples in link:https://github.com/castle-engine/castle-engine/tree/master/examples/lazarus[examples/lazarus/]. Like `examples/lazarus/model_3d_viewer`.

. To use in your own application:
+
--
. Create a normal new LCL project (using Lazarus _"New Project"_ menu item). Choose _"Application"_.

. Pick `TCastleControl` from the component palette (tab _"Castle"_) and drop it on a regular Lazarus form.

. Done. Press "Run" :)
--

### Screenshots

cgeimg::block[
  lazarus_control.png|TCastleControl designed in Lazarus,
  lazarus_control_run.png|3D model viewer using TCastleControl
]

## Opening the project in CGE editor

While you will use Delphi / Lazarus to design your forms, it is also useful to design CGE components using CGE editor. To do this, create a link:project_manifest[CastleEngineManifest.xml] file within your project.

This is an example for Delphi application:

[source,xml]
----
<?xml version="1.0" encoding="utf-8"?>
<project name="my_project"
  standalone_source="my_project.dpr"
  compiler="delphi"
>
</project>
----

NOTE: Delphi uses can just open the project in Delphi IDE, and use from Delphi the menu item _"Tools -> Castle Game Engine -> Open in CGE editor"_. This will automatically create a manifest file, just like shown above, if it is missing.

This is an example for Lazarus application:

[source,xml]
----
<?xml version="1.0" encoding="utf-8"?>
<project name="my_project"
  lazarus_project="my_project.lpi"
  build_using_lazbuild="true"
>
</project>
----

This way you can open this project in the CGE editor and:

- open and modify CGE designs, like `xxx.castle-user-interface` files,

- run the project from CGE editor (CGE editor, and link:build_tool[build tool], will just invoke Delphi compiler or `lazbuild` under the hood).

## Initializing the resources

Essentially, you can initialize your game resources at any time. Even before any cgeref:TCastleControl[] exists. For example in the `OnCreate` event of a main form. All _Castle Game Engine_ components can be created and configured regardless of the OpenGL context existence.

////
TCastleControl.OnOpen will be deprecated soon. Use stuff in container instead.

[NOTE]
====
**Lazarus specific note:**

Initializing resources inside the cgeref:TCastleControl.OnOpen[] is also a good choice, in typical cases. Since OpenGL context is available at this point, the engine will be able to initialize also GPU resources for things you instantiate at this point.

But note that cgeref:TCastleControl.OnOpen[] runs each time a form with cgeref:TCastleControl[] is opened. Depending on your LCL application organization, if you allow to open this form many times -- then cgeref:TCastleControl.OnOpen[] will happen many times. In this case, to execute something really *once*, just folow the usual LCL approaches (e.g. initialize from a main form, or even from the main program).
====
////

## Loading user interface and view

In the simple case, you can load a design (file `xxx.castle-user-interface`) using cgeref:TCastleControlContainer.DesignUrl[], and access loaded components using cgeref:TCastleControlContainer.DesignedComponent[].

cgeimg::block[control_lcl_design_loaded.png|Using TCastleControl.DesignUrl to load design in TCastleControl\, visible at Lazarus design-time]

In more involved cases, you should use multiple link:views[] within your control. In this case, use cgeref:TCastleView[] within the cgeref:TCastleControl[], and load a design (file `xxx.castle-user-interface`) inside the view using cgeref:TCastleView.DesignUrl[]. This is analogous to our recommended workflow with cgeref:TCastleWindow[]. You can add new _view_ using CGE editor (_"Code -> New Unit -> View..."_), or just define a new unit like this:

[source,pascal]
----
unit GameViewMain;

interface

uses CastleUIControls;

type
  TViewMain = class(TCastleView)
  published
    { Components designed using CGE editor.
      These fields will be automatically initialized at Start. }
    // ButtonXxx: TCastleButton;
  public
    constructor Create(AOwner: TComponent); override;
    procedure Start; override;
  end;

var
  ViewMain: TViewMain;

implementation

constructor TViewMain.Create(AOwner: TComponent);
begin
  inherited;
  DesignUrl := 'castle-data:/gameviewmain.castle-user-interface';
end;

procedure TViewMain.Start;
begin
  inherited;
end;

end.
----

That's enough to load `gameviewmain.castle-user-interface` design from your `data` subdirectory.

To initialize this view in your application, you can use this code e.g. in `OnCreate` form event:

[source,pascal]
----
ViewMain := TViewMain.Create(Application);
MyCastleControl.Container.View := ViewMain;
----

A simple example of using views with `TCastleControl` is in https://github.com/castle-engine/castle-engine/tree/master/examples/lazarus/multiple_views[examples/lazarus/multiple_views]. The CGE API used there is the same for Lazarus (LCL) and Delphi (VCL, FMX) versions of `TCastleControl`.

## Focus (receiving key input)

Like every LCL / VCL / FMX control, our cgeref:TCastleControl[] receives the keys only when it has _focus_. The control _does not_ capture all the keys pressed over the form (this would be bad, as other controls, maybe even other cgeref:TCastleControl[] on the same form, may want to handle them). To make sure that controlling the camera by keys (like AWSD or arrows) works, make sure that your control has a focus.

NOTE: The advises below were tested with LCL, though they should apply to VCL and FMX too. Report if you encounter difficulties applying them.

To make the control have focus:

- You can call `MyCastleControl.SetFocus;` at any time to explicitly make the `MyCastleControl` focused. You can assign this to some menu item, or key shortcut handled by form, to allow user to easily switch focus to the cgeref:TCastleControl[] instance.

- You can also use cgeref:TCastleControl.AutoFocus[] to make the control automatically focused when user presses mouse over it.

////
Alternative approach is to make cgeref:TCastleControl[] the only control on the form that can receive focus. This makes things simple, cgeref:TCastleControl[] will always have focus. But it means limiting yourself what you use on a form. E.g. all buttons should be `TSpeedButton` (unfocusable button in LCL).
////

## Comparison between TCastleWindow and TCastleControl

Most of this manual describes the process of using the engine with the cgeref:TCastleWindow[] instead of cgeref:TCastleControl[].  All new projects created from editor templates use cgeref:TCastleWindow[]. We generally advise cgeref:TCastleWindow[], as:

- cgeref:TCastleWindow[] works on all CGE platforms (desktop, mobile - link:android[Android], link:ios[iOS], consoles - link:nintendo_switch[Nintendo Switch], upcoming _web target_). It allows us to have true cross-platform projects, that can be recompiled to any supported platform out-of-the-box.

- cgeref:TCastleWindow[] allows us to handle events and message loop on the CGE side, and make it efficient. For example, it makes _mouse look_ work perfectly smooth.

On the other hand, using the cgeref:TCastleControl[] has one big benefit: as you place the control inside a form, you can surround it with all the standard VCL / FMX / LCL GUI controls. So you can use numerous GUI controls, with native look on all desktop systems, together with _Castle Game Engine_.

We are committed to supporting both approaches (cgeref:TCastleControl[] and cgeref:TCastleWindow[]) in the foreseeable future.

### It is not allowed to use both `TCastleWindow` and `TCastleControl` in a single application

You have to make a choice:

- Use cgeref:TCastleWindow[]. Use the cgeref:Application[] singleton from cgeref:CastleWindow[] unit to manage these windows.

- Use cgeref:TCastleControl[]. Use the VCL / FMX / LCL forms, along with VCL / FMX / LCL `Application` singleton in the `Forms` unit, to manage your application.

You cannot mix these approaches, as neither VCL / FMX / LCL (in which our cgeref:TCastleControl[] works) nor cgeref:TCastleWindow[] are prepared to handle the situation that they only handle *some* application forms, and other library handles the other forms.

That is also why we have separate packages (both for _Delphi_ and _Lazarus_, so statement below applies both to `.dpk` and `.lpk`):

- With cgeref:TCastleWindow : `castle_engine_window` package.

- With cgeref:TCastleControl[] : `castle_engine_lcl` (Lazarus), `castle_engine_vcl` (Delphi VCL), `castle_engine_fmx` (Delphi FMX) packages.

Only one of the above packages should be used in a particular application.

Note that, if you use cgeref:TCastleControl[], it is OK to have multiple cgeref:TCastleControl[] instances visible at the same time. Similarly, if you use cgeref:TCastleWindow[], you can have several cgeref:TCastleWindow[] instances visible at the same time (but only on desktop platforms), see https://github.com/castle-engine/castle-engine/tree/master/examples/window/multi_window[examples/window/multi_window].
