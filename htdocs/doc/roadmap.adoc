# Roadmap
include::common.adoc[]

## Introduction

Don't see the feature you need? link:talk.php[Tell us on the forum or Discord].

If you would like to see some feature implemented sooner, please link:https://www.patreon.com/castleengine[support the engine development on Patreon!]

## Planned before 7.0 release

### (IN PROGRESS) New navigation components that support physics out-of-the-box

*In progress:* We have completely new navigation components in https://github.com/castle-engine/castle-engine/pull/533 . They achieve a few things:

- more universal (suitable for 1st person 3D, 3rd person 3D, 2D cases),

- more configurable inputs, also from editor (expose new "axis" paradigm),

- more flexible (functionality split into a few components, easier to pick a subset of functionality you want, easier to write extensions), and honor the physics engine.

Once above is merged, eventually cgeref:TCastleWalkNavigation[] will be deprecated. Don't worry, the deprecated cgeref:TCastleWalkNavigation[] will still be available for quite a while -- we're aware that many projects use it, since (as of today, before we merge PR 533) we actually advise using cgeref:TCastleWalkNavigation[], as it's very easy to use and flexible enough for many situations.

### (IN PROGRESS) Remove the need to care about PreciseCollisions, cleanup old collision detection using our octrees

We want 100% of collision detection (which also means ray-casting) at runtime to be done using link:physics[], and thus determined by the presence of components like cgeref:TCastleMeshCollider[].

We're almost there... except there are exceptions, see link:physics#_old_system_for_collisions_and_gravity[Old system for collisions and gravity]. This means cgeref:TCastleSceneCore.PreciseCollisions[] matters. We want to get rid of it, at least for run-time. The old collision detection using our octrees may remain:

- Useful at design-time, in editor.

- For frustum culling done by cgeref:TCastleScene[]. This is invisible to the user.

To do this:

- Deprecate `TCastleXxxNavigation` using older collisions. See above -- we have components to replace them.

- New `Viewport.PointingDeviceHit` instead of `Viewport.MouseRayHit`, `Viewport.TransformUnderMouse`, that exposes physics ray-casting by default.

This is started, things done:

- Past `Spatial` property is now both simplified (only `ssRendering,ssDynamicCollisions`, does not manage other structures) and private. Only cgeref:TCastleSceneCore.PreciseCollisions[] is public.

- At design-time (in editor) we always behave as if cgeref:TCastleSceneCore.PreciseCollisions[] is `true` and this is good.

[#steam]
### (IN PROGRESS - ALMOST DONE) Steam integration, and engine available as Steam app

Integration with Steam done and rocks, https://castle-engine.io/steam .

TODO: Put engine on Steam as an app. We already have registered the application with Steam, and we have the app ID. We need to add achievements to CGE editor and submit it to Steam.

[#behavior_move_attack]
### (IN PROGRESS - ALMOST DONE) Implement TCastleMoveAttack behavior, use for enemies in examples/fps_game and examples/creatures_behaviors

In progress.

- Initial `TCastleMoveAttack` done, used in
+
--
- examples/creature_behaviors
- examples/fps_game
- Demo at "Delphi Programmers Meeting 2024 (Zlot Programistów Delphi 2024)", see https://github.com/castle-engine/conference-zlot-delphi-2024[demo application]. See https://castle-engine.io/zlot2024[demo slides (in Polish)].
--
- Has simple logic of movement, getting close to player (or any other enemy), short-range attack.
- We also have simple enemy logic implemented in _"3D FPS Game"_ template and to show our link:behaviors[].

TODO:

- Use link:physics[] for all movement and gravity
- Finish firing missiles, `TCastleMissile` finish
- Eliminate all TODO in `src/scene/castlelivingbehaviors_moveattack.inc` and friends
- Restore + modernize our "waypoints / sectors" (known as "navmesh" in other engines), make it possible to set up in editor
- Change section title from _"Living (Early Preview - Functionality Will Change!)"_ -> _"Living"_
- Phase 2: Use behavior trees

[NOTE]
====
Old approach to this was offered by the `CreatureCreatures` / `CastleResources` / `CastleLevels` / `CastleItems` / `CastlePlayer` units. They should be replaced with a better API. These 5 units expose a high-level API, that sits on top of the existing classes (like TCastleScene and TCastleTransform). But we are not 100% happy with their API. Reasons:

- The API is too specialized at some points (3D games with creatures / items pickable),

- It is confusing how it maps to API underneath (e.g. TPlayer somewhat controls the TCastleNavigation).

We want to express their features in different ways, in ways that are more closely and obviously connected with TCastleScene / TCastleViewport / TCastleNavigation. Basically, we're very happy with current API of cgeref:TCastleScene[] and cgeref:TCastleTransform[], and we want to make it more obvious how it is related to creatures/placeholders and other concepts in the engine. The existing `CreatureCreatures` / `CastleResources` / `CastleLevels` / `CastleItems` / `CastlePlayer` also use TCastleScene and TCastleTransform inside, but the relationship is non-obvious.
====

////
Extra possible things:

- _(Possibly reject -- not really useful, you can setup enemies in CGE editor)_ - New TCastleScene.Load should be able to take a callback (or some class instance) to easily perform the "placeholders" functionality of `TLevel.Load` in a flexible manner (user can decide what to replace with what).

- _(Possibly reject -- such optimization should be hidden)_ - There will be TCastleSceneView that provides part of the functionality of T3DResource (multiple TCastleSceneView share a single TCastleScene but can show different animation frame of it), but without some often-unnecessary "baggage" from T3DResource (like refcounting of T3DResource, and it's special Prepare/Release methods).
////

[#component_list]
### Component list class, to easily expose serialized list of components in editor (prerequisite of "New material components" item)

We want to have a component list class, that

- Maps to a subtree in the "Hierarchy" in CGE editor,

- Can be used to express current (this will make some code in editor and `CastleInternalInspector` simpler):
+
--
- cgeref:TCastleTransform[] children of cgeref:TCastleTransform[],
- cgeref:TCastleBehavior[] children of cgeref:TCastleTransform[],
- cgeref:TCastleUserInterface[] children of cgeref:TCastleUserInterface[],
- cgeref:TCastleComponent[] non-visual children of cgeref:TCastleComponent[], now managed in cgeref:TCastleComponent.NonVisualComponents[].
--

- Can be used to express future things:
+
--
- Screen (post-processing) effects list, see link:#screen_effects[Screen effects - expose as nicer components],
- Materials, textures, effects, see link:#materials[New material components]
- Tiled map layers, see link:#tiled_layers[New API for Tiled layers]
--

- Is generic, so we can be safe at compile-time about what is allowed to be inserted. That's why we don't want to just use standard `TComponentList`.

- Would allow CGE editor to easily hide some lists, while not hiding others. E.g. whether to expand cgeref:TCastleBehavior[] in the hierarchy -- can be just UI choice.
+
Conceptually, when Unity has hierarchy (of `GameObject`) on left and list of components (_components_ in Unity sense, i.e. pieces of `GameObject`) on right, we instead have a tree of components (_components_ in Pascal and CGE sense, i.e. `TComponent`). It is just editor UI decision -- which levels are shown in the tree (hierarchy), which are not (e.g. behaviors could be hidden, or shown in some different way, in editor -- and it shall be unrelated to the Pascal classes organization).

Idea:

`TCastleBaseComponentList = class(TComponent)`

- Internally uses `TComponentList` for storage because we want items to auto-disappear when they are freed wherever.
- Do not inherit from `TComponentList`, to not expose everything, limit access to our methods/properties. So only use it as a private field.
- Non-generic, editor can use it as a base class for all nested lists.
- Editor operations like move up/down, remove, add, should use this class.

`TCastleComponent.GetComponentLists` method: Query all `TCastleBaseComponentList`. Maybe it should just use RTTI to query all `TCastleComponentList<T>` on given object, from them extract `TCastleBaseComponentList` and return them. This is crucial for editor to show hierarchy. One of the lists should be "default" to show it without intermediate item in hierarchy (e.g. TCastleTransform children are `Children: TCastleComponentList<TCastleTransform>` but we don't display `Children` intermediate item in hierarchy).

`TCastleComponentList<T> = class(TComponent)`

- Generic, safe to use from code.
- internally uses `InternalBaseList: TCastleBaseComponentList` to store items, as private filed. (Do not inherit, to not expose everything non-generic.)

Another phrasing of this (equivalent, mostly):

Unify the processing of both visual children of TCastleTransform, and visual children of TCastleUserInterface, and NonVisualComponents -- by new `TCastleComponentList<T>`. This will be backward compatible, and will guarantee that all these iterations really use the same mechanism (right now these iterations are ~consistent just because they have been manually made to look similar each time).

So we'll have

```pascal
TCastleComponent = class(TComponent)
  NonVisualComponents: TCastleComponentList<TCastleComponent>;
end;

TCastleUserInterface = class(TCastleComponent)
  Children: TCastleComponentList<TCastleUserInterface>;
end;

TCastleTransform =  = class(TCastleComponent)
  Children: TCastleComponentList<TCastleTransform>;
  Behaviors: TCastleComponentList<TCastleBehavior>;
end;
```

and iterating with  `for ... in ...` will be always equivalent to iterating over `Children`.

So

`for C in MyTransform do`

will be equivalent to

`for C in MyTransform.Children do`

Same

`for C in MyUserInterface do`

will be equivalent to

`for C in MyUserInterface.Children do`

And you can also do

`for C in MyTransform.Behaviors do...`

`for C in MyTransform.NonVisualComponents do...`

Right now... it's already mostly like that, but due to the fact that I declared a few things manually in the same way. The new `TCastleComponentList<T>` should force it to be consistent, and also make some code easier for editor.

[NOTE]
====
The new class will also be our recommendation to use instead of https://wiki.freepascal.org/TCollection[TCollection]. Our `TCastleComponentList<T>` should full-fill similar use-case, with some advantages:

- Based on generics, so type-safe (compiler will not allow you to add an instance of wrong class).
- Does not require making the child a descendant of any particular type (like `TCollectionItem`), so it's more flexible.
- It is expanded in CGE editor hierarchy (with future options to make it collapsed by default) and CGE inspector (F8 in debug builds).

We could achieve some of these advantages with `TGenericCollection<T>` as shown on https://wiki.freepascal.org/TCollection[TCollection wiki page], but still we would not avoid the `TCollectionItem` necessity.
====

[#shadow_maps]
### (IN PROGRESS) Make shadow maps available by a trivial property on light sources

Activating link:x3d_extensions_shadow_maps.php[shadow maps] has some disadvantages now:

- You need to setup special X3D nodes and fields. At least put `shadows TRUE` on some light source in X3D, most likely you also want to specify `defaultShadowMap` on this light source.

- The X3D field name is inconsistent. In X3D light nodes, `shadows` activates _shadow maps_, while `shadowVolumesMain` activates _shadow volumes_. This is inconsistent with cgeref:TCastlePunctualLight.Shadows[], which activates _shadow volumes_. As part of this work, we should make cgeref:TCastlePunctualLight[] API and X3D nodes more consistent (we can, as shadow map stuff is our extension over X3D).
+
We should also account for X3D 4.0 shadows additions and report to the X3D working group our experience, ev. suggestions for X3D improvements.

- The lights cast shadow maps only https://github.com/castle-engine/castle-engine/issues/284[within the same scene]. _(Work is under way to remove this limitation, https://github.com/castle-engine/castle-engine/commit/7031d20e73a38d66985e35f8e53cd298e252c519[this refactor] is the important step and it is already pushed to the master branch.)_

This is in contrast to https://castle-engine.io/shadow_volumes[shadow volumes] that are trivial to activate (you literally toggle one checkbox, `Shadows`, on our light components `TCastleXxxLight`) and work across scenes, thus are very natural to set up using CGE editor.

We want to upgrade our _shadow maps_ support to address above issues:

- cgeref:TCastlePunctualLight[] should just have a property `ShadowsMethod` (enum `smVolumes, smMaps`) that will allow to easily switch between shadow volumes and shadow maps.
+
NOTE: Alternative, rejected, API idea was to turn `Shadows` into an enum with a possible "none" value, like `Shadows: (None, Volumes, Maps)`. Separate Boolean to choose whether shadows are enabled at all, separate from choosing shadows algorithm, seems more natural. It is, conceptually, a different decision _"do I want shadows"_ vs _"what shadows algorithm do I want"_. It also means we (at the engine) have to choose one default shadow algorithm, instead of leaving the decision on users --- which is actually better for users.

- More API:
+
--
- Shadows distance?
+
Instead of current projectionFar, which has auto-calculation but it can reach too far.
+
What is Unity default, just hard code instead ot detecting?

- Shadows distance falloff? Smooth transition to no shadows at all.
+
This matches the existing logic in `shadow(` in GLSL that says _"where the shadow map doesn't reach, assume no shadow"_. So we just introduce a smooth falloff to this.

- Take a look at what Godot, Unity, Ogre3d (last one has both shadow maps and volumes) expose in API, and what are their defaults.
--

[#web]
### (IN PROGRESS) Web platform (rendering using WebGL) support

This is already in-progress, see link:web[] for all docs.

## Important, big, impactful features (but we first focus on stuff above)

[#packages]
### Package system

We want a system to define "packages" in _Castle Game Engine_.

- Package creators can define a "package" that contains *code* and/or *data*.
+
The _data_ can be naturally referred to by the _code_, e.g. a _"car behavior (Pascal code)"_ can use a _"default car crash sound (data, .wav file)"_.
+
We can have mostly-data packages (like _"collection of medieval weapons"_) or mostly-code packages (like _"support for scripting in LUA"_, _"easy TCP/IP client/server"_) or a combination of both (like _"logic and basic data for Arcade Car Racing game type"_).
+
Package can also define platform-specific code (e.g. Java on Android), thus new packages would also allow user-created link:android_services[Android services], link:ios_services[iOS services].

- Users (developers of games/applications) can define that their project uses given packages.
+
They can also "lock" versions of the packages (like NPM packages) (so co-developers can checkout the project and associated packages with _exactly_ equal versions). They can also upgrade the packages easily (by command-line build tool and GUI editor).
+
And of course they can search for packages, both from editor GUI and WWW browser. Packages include typical metadata like title, description, license, screenshot(s), version control repository (GIT for start), website, donation link. Website can feature things like ratings, comments (design of the website is _not_ part of this roadmap item).

- We want to have active ecosystem of packages. Packages are created / updated / used without making CGE developers (like Michalis) a bottleneck. CGE becomes the "core" around which we have this ecosystem. This is also our answer to _"Unity Asset Store"_ etc.

NOTE: We have a few specific requirements (which are also a reason why we cannot use existing Lazarus packages / OPM, or FpMake / FpPkg, or GetIt) and a design how to satisfy them. Read on :)

Basics how it works:

- Use lock file like NPM, boss.

- Command to install, upgrade:
+
`castle-engine packages install`:
+
--
- Installs all packages listed in the `CastleProject.json`
- Creates for them a new entry in `CastlePackageLock.json`, creating also `CastlePackageLock.json`, if it does not exist already; if `CastlePackageLock.json` exists and already mentions the package -> use version of package specified ("locked").
- Creates `castle-packages` subdirectory
--
+
`castle-engine packages upgrade`
+
--
- Updates to latest versions all packages from server.
+
Default server of each package is "CGE main packages" registry, which is just a repo with JSON pointing to other repos with packages. Package creators submit PR to link to their new package; updating package requires no PR. The server of each package can be customized.

- Updates `CastlePackageLock.json` (bumps versions)

- Updates `castle-packages` subdirectory contents.
--

- Files are JSONs serializable with `CastleComponentSerialize`:
+
--
- Upgrade project definition, following existing link:#_project_settings_editable_using_gui[project roadmap item]. So `CastleProject.json` with matching Pascal class. Add there declaration of "used packages". This makes project definition readable/editable as easy JSON, and readable/editable using GUI editor at the same time, and consistent with everything else we read/write using `CastleComponentSerialize`, like our designs.

- For package, create `CastlePackageLock.json`, serialized component `TCastlePackagesLock`. This is where package metadata lives.

- Both `CastleProject.json` and `CastlePackageLock.json` need a place for comments at top,  using JSON field "comment": "..."
--

CGE editor "Packages" dialog:

- Lists currently installed packages
- Allow to upgrade them (all, or specific ones)
- Allow to install new one from "CGE main packages" registry

Prerequisites:

- Make link:#component_list[lists serializable nicer].
- link:#_project_settings_editable_using_gui[Rework `CastleEngineManifest.xml` -> `CastleProject.json`]

Packages in `CastleProject.json` must allow to override location from which they are to be downloaded:

- for development (when package is not submitted to "CGE main packages registry" yet).

- for package examples.
+
Because examples must be able to refer to package data. There has be a way to say "I use a package that is in relative path from me, like `CastleLines2D path=".."`. And then `castle-engine packages upgrade` does nothing, and the lock file just copies this relative path, and the package is not downloaded from anywhere.

- for organization-specific package repositories. You don't need to use "CGE main packages registry", though we will of course have one and use it by default.

For each installed package:

- Clone repo using GIT

- Must contain `CastlePackage.json`.
+
(make error if also CastleProject.json present? or ignore?)
+
We define `TCastlePackage` component, with
+
```delphi
CodeSearchPaths: TCastleStringList
// Auto added to compiler paths for this project
// Also auto added to LSP paths for this project
```
+
If package contains `data/` subdir, make it available with this URL:
+
`castle-package-data:/package-name/<path-inside-package-data>`
+
`castle-package-data` protocol is resolved:
+
--
- when `castle-packages` exists (running when in development, from project source repo) then look there in `castle-packages/package-name/data/<path-inside-package-data>`.
+
This is similar to how we resolve `caste-data:/` by looking in subdirectory `data`.

- when `castle-packages` does not exist (running from packaged game): look in `castle-data:/_castle-packages/package-name/<path-inside-package-data>`. To make it work, `castle-engine package` will copy all package data (but not other package stuff, like metadata or code) into `_castle-packages` before packing.
--

High-level goals:

- Packages allow users to get extra functionality (beyond what is in main CGE repo). Of course.

- Packages allow developers to easily share extra functionality with others.

- Packages are easy to install using CGE editor or command-line build tool.

- Packages used follow the spec in `CastleProject.json`, which is just upgraded `CastleEngineManifest.xml`

- Follow the "packages lock" known from NPM, boss etc.: Allow developer to commit a file that describes each package version, exactly, using GIT hash. This file can be committed to version control, and means that all developers use the same package versions. The "upgrade" of packages must be explicit!

- Packages can be upgraded, all or specific ones, using CGE editor or build tool

- Packages can contain Pascal code. No extra action should be necessary after installing the package -- that is, extra package Pascal paths will be automatically derived from package metadata and passed to compiler.

- Packages can contain services -- Android-specific, iOS-specific services subdirectories that define any integration with Android or iOS. This is useful for e.g. integrating ad networks, analytics, etc.

- Similarly, packages can contain CGE data. And no extra action should be necessary to use this data -- both for components included in the package (e.g. internal data for some package functionality) or for project (which may use e.g. some designs).
// Introduce some split, "internal" package data that isn't supposed to be used by project? Or not, seems useless -- if needed, a package can use just subdirectory "internal" inside it's data, no drawback of this.

- Packages can contain example projects one can run. This makes sense in CGE (unlike Unity or other engines where you can "run a scene", in CGE you run a project).

Review process: Unlike PRs to CGE repo, new/updated packages don't require any big review process from CGE devs -- we want to be permissive in what packages we allow to be published with "CGE main packages registry".

- We reserve the right to make some review, but mostly with goal "doesn't this contain anything malicious" (e.g. we don't want package called "easy MMO RPG" that does something malicious on the developer and/or application user machine). But this PR is not "make a decision if this is the right approach, or if CGE devs judge this as good quality or whatever". Of course we will try to review everything and post helpful comments, but CGE core devs *cannot* be a bottleneck in this process. So:

- New package submissions are accepted "by default", the only allowed blocker is "we see something malicious".

- Everyone understands that by downloading a package, they download a piece of code/data written by the package's author. CGE core devs are not responsible for the quality of it.

- Ratings, comments, PRs to package, are a way to give feedback / judge the package before you use. We only remove packages (from "CGE main packages registry") packages that are malicious (try to do something obviously unexpected abusing developer/user machine). Packages that are "low quality" just receive the corresponding feedback (ratings, comments). Of course, comments on the CGE website follow our https://forum.castle-engine.io/t/forum-guidelines/28[forum and Discord guidelines]. Let's be helpful and constructive and together strive to create something cool.

- To repeat: Everyone, including CGE core devs, is of course welcome to make more thorough review of the package, post comments whether something could be done differently etc. But this is just optional, not a blocker. A blocker is only if we see (or someone reports) something malicious.

- Releasing new version of package doesn't require any review. You can just tag your GitHub repo, and the new release is there, CGE repo will find the new tag soon.

- New packages, and notices of upgrades, go to internal queue for "quick review": with the main goal to check if the package contains anything malicious.

### VR and AR

VR: link:wp/2022/01/16/oculus-quest-2-developer-mode-for-castle-game-engine-enabled/[Michalis has access to Oculus Quest since the beginning of 2022]. Port most likely using link:https://www.khronos.org/OpenXR/[OpenXR] (to include the widest possible range of devices using a single cross-platform API).

cgeimg::block[oculus.png|Oculus Quest 2: Developer Mode for Castle Game Engine enabled!]

AR: most likely using Android / iOS built-in frameworks, as these make AR widely available on modern phones.

[#delphi_platforms]
### More Delphi platforms

See link:delphi[about existing Delphi compatibility] - we support _Windows_ and _Linux_ now. We plan to support more platforms with Delphi: Android, iOS...

[#gltf_meshes_gpu]
### Loading glTF meshes directly to GPU

We should read meshes from glTF to GPU in a way that keeps binary layout of data in glTF, so that it is ultra-fast to load, and it is loaded almost straight from glTF to GPU.

Some modifications to CGE may be required here (CastleGeometryArrays will have to be made more flexible for this). New X3D nodes should be introduced, like `BufferGeometry` (same as X3DOM?). See https://github.com/michaliskambi/x3d-tests/wiki/Binary-meshes[Binary meshes in X3D - my plan and reasons to add binary meshes to X3D spec].

[#gltf_skinning_gpu]
### (IN PROGRESS) Efficient skinned animation on GPU (from glTF and more) using new SkinnedAnimation node

NOTE: This is almost ready now, on https://github.com/castle-engine/castle-engine/tree/skinned-animation-gpu[skinned-animation-gpu] branch, see link:skin[new Skin support].

We want to perform _skinning_ fully on GPU, avoiding the drawbacks of current way of importing glTF animations.

To explain, right now we "precalculate" skinned animation imported from glTF to `CoordinateInterpolator` nodes, so loading is slower and consumes memory, although playback is fast. We also have an independent skinned animation support in H-Anim, which is slow at runtime (CPU-based). The plan below is to reconcile these two approaches, and make skinned animation in CGE perfect, for both glTF and X3D (H-Anim) users.

We plan to introduce new X3D node for this called `SkinnedAnimation`:

- This new node would match exactly the glTF skinned animation design.
+
In particular, it would allow to give explicitly the skin's _"Inverse Bind Matrices"_, a powerful concept in glTF that allows to perform the skinned animation super-efficiently on GPU. See https://www.slideshare.net/slideshow/gltf-20-reference-guide/78149291[glTF 2.0 Reference Guide] for an overview of glTF specification -- the skinned animation is explained there on 2 pages, along with sketch of implementation on GPU.
+
Also, as the name implies, the new node only deals with _skinned_ animation (where mesh vertexes are affected by joints through weights). You don't need it at all to make "regular" animation when rigid shapes move/rotate around (e.g. like link:spine[Spine] skeletal animation).

- The new node would also enable making skinned animation in X3D using just `Transform` nodes for joints. No need for `HAnimJoint`.

- The new node deliberately avoids any naming connotation with "humanoids". Skinned animation is a standard 2D and 3D animation technique, not specific to humanoids at all. (though of course it can be used for humanoids, as a specific case.)

- We could reimplement H-Anim support (currently using CPU implementation, that calculates skin at runtime on CPU each frame -- this is slow) by internally using `SkinnedAnimation` node.

- This new node will make our "skinned animation" have all performance advantages and no disadvantages:
+
--
- It will react at runtime to changes of joints, so it will be suitable also for inverse kinematics or other usage where joints change at runtime. See https://forum.castle-engine.io/t/joint-bone-controlled-mesh-animation/546 . See https://github.com/castle-engine/castle-engine/tree/master/examples/animations/animate_bones_by_code .

- It will be fast at run-time, due to animating on GPU.

- It will be fast at loading, and not eat excessive amounts of memory, due to just loading the skin data to GPU.
--

[#gltf_material_features]
### Support new glTF material extensions

- https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_clearcoat/README.md[Clearcoat]
- https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_transmission/README.md[Transmission]
- https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_sheen/README.md[Sheen]
- https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_ior/README.md[IOR (index of refraction)]
- https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_volume/README.md[Volume]
- https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_specular/README.md[Specular]

See:

- https://www.khronos.org/news/press/khronos-releases-wave-of-new-gltf-pbr-3d-material-capabilities[Khronos Releases Wave of New glTF PBR 3D Material Capabilities]
- https://www.khronos.org/news/press/new-gltf-extensions-raise-the-bar-on-3d-asset-visual-realism[New glTF Extensions Raise the Bar on 3D Asset Visual Realism]
- https://www.khronos.org/blog/using-the-new-gltf-extensions-volume-index-of-refraction-and-specular[Using the New glTF Extensions: Volume, Index of Refraction, and Specular]

Thanks to _Holger Seelig_ and https://create3000.github.io/x_ite/[X_ITE] project, the path how to express this functionality in X3D is already known and tested:

- https://create3000.github.io/x_ite/components/[X_ITE introduces new X3D nodes] like https://create3000.github.io/x_ite/components/x-ite/sheenmaterialextension/[SheenMaterialExtension], https://create3000.github.io/x_ite/components/x-ite/clearcoatmaterialextension/[ClearcoatMaterialExtension] that express additional material functionality and are straightforward equivalents to glTF extensions above.

- X_ITE adds https://create3000.github.io/x_ite/components/shape/physicalmaterial/[new field to PhysicalMaterial] called `extensions` (`MFNode`, allows `X3DMaterialExtensionNode`) to add this functionality to a `PhysicalMaterial`.

- See https://web3d.org/pipermail/x3d-public_web3d.org/2024-June/020587.html["X_ITE implementation of glTF material extensions" thread on x3d-public mailing list for more details].

### More physics

- Older physics based on octrees should be phased out.
+
*In progress:* We're slowly already doing it. If you want to get ahead, you can already use physical colliders (link:physics[see physics manual]) and use queries like `PhysicsRayCast` instead of `WorldRay`.

- cgeref:TCastleSceneCore.ExposeTransforms[] should work both ways, to allow to attach bodies and colliders to parts of the scene. This will enable e.g. ragdolls.

- More physics backends, not only _Kraft_.
+
We're looking to add another physics engine that relies on some big, well-known and open-source engine. Our current considerations are either https://developer.nvidia.com/physx-sdk[PhysX] or https://pybullet.org/wordpress/[Bullet].
+
As as result of this, more physics engines can also be added more easily in the future.

- See more in link:physics#_future_plans_todos[manual about physics, subsection "TODO"].

[#particles]
### (IN PROGRESS) Particle systems

With a designer. Example usage: blood, snow, rain, fire, smoke... 2D, 3D.

_This is already in-progress by Trung Le (Kagamma). Use link:https://github.com/Kagamma/cge-3d-particle-emitter[cge-3d-particle-emitter] or link:https://github.com/Kagamma/cge-effekseer[Effekseer Integration]. See link:additional_components.php[CGE components]._

[#terrain]
### (IN PROGRESS) Terrain features (in particular, editing terrain in CGE editor)

_What we have now:_ The engine includes cgeref:TCastleTerrain[] component and https://github.com/castle-engine/castle-engine/tree/master/examples/terrain[examples/terrain] that shows how to generate terrains in various ways (from smoothed random noise, from a ready image) and render it. We also have the link:https://github.com/castle-engine/wyrd-forest["Wyrd Forest"] game that also uses `CastleTerrain`, and also includes a simple editor of terrain settings. See also https://castle-engine.io/wp/2022/08/21/new-components-to-generate-terrains-along-with-a-demo-including-water-and-planting-trees/[news about terrains].

_What we need:_ Visual, interactive editor for terrains, matching e.g. Unity feature set.

Enable editing heightmaps easily:

- Edit the terrain heights within CGE editor, using something like a brush to lower/raise clicked points. To make hills, valleys in a comfortable way.
+
The edited heights just land in cgeref:TCastleTerrainImage[] with internal contents.

- Use _splatmap_ and enable editing it, to edit the terrain textures mixture within CGE editor. To effectively paint the terrain with different textures like grass, dirt, rock, snow.
+
The _splatmap_ is an internal (not directly visible) RGBA texture that determines the weights of the visible textures. E.g. imagine you have 4 visible texture types: grass, dirt, rock, snow. Then
+
--
- splatmap _red_ channel determines "how much to add _grass_ at this point",
- splatmap _green_ channel determines "how much to add _dirt_ at this point",
- splatmap _blue_ channel determines "how much to add _rock_ at this point",
- splatmap _alpha_ channel determines "how much to add _snow_ at this point".
--
+
The editing code can watch over to make sure the weights are normalized, i.e. always sum to 1.0.
+
More about splatmaps: https://en.wikipedia.org/wiki/Texture_splatting , https://help.world-machine.com/topic/device-splatmap/ .

- The existing terrain implementation, in cgeref:TCastleTerrain[] and https://github.com/castle-engine/castle-engine/tree/master/examples/terrain[examples/terrain] is a starting point -- shows how we can _randomize / generate_ a reasonable terrain with texture weights. But above additions are critical to make it really useful: in practice, for real games, people want to have control over the terrains. So the existing generation will remain part of the workflow, but only as an optional starting point.
+
NOTE: The fact that you can connect a few terrains together, and our generation can generate "neighbors" effectively, is super-useful and will be preserved.

- Tools to plant big things (3D cgeref:TCastleScene[]) in the terrain, like trees, rocks. You can do it now, though it's manual: Use behavior cgeref:TCastleStickToSurface[] and put multiple instances of the thing you want. Use cgeref:TCastleTransformReference[] to make it optimal for memory when you can, so that multiple instances share the same resources.
+
Optional LODs could be nice (but maybe this is outside of scope of terrains; LODs should be just on cgeref:TCastleScene[]. Currently we have LODs on cgeref:TCastleScene[] using `TLODNode` - works nice, though complicated to set up, we want to move away from exposing X3D nodes for this.)

- Tools to plant many small things, like grass. This is a big different than "big things", and here automatic LODs that e.g. decrease density of grass with distance make sense.

- More specialized and efficient rendering algorithm. Right now terrain in the end is just a simple big mesh of triangles. It's actually quite fast to render (because modern GPUs can handle big shapes) but it's not a sustainable way to render huge terrains.

- Improve shaders for terrain to show it, like link:https://www.getlazarus.org/videos/bareterrain/#learning_resources[this BareGame example].

- More than 4 layers. Each new set of 4 layers mean a new _splatmap_.

This is started: see PR https://github.com/castle-engine/castle-engine/pull/612 .

[#vulkan]
### Vulkan renderer (maybe; alternative: use BGFX to get Vulkan)

Add a https://www.vulkan.org/[Vulkan] renderer.

* API:
+
Almost all public CGE API will remain unchanged. For a long time now, we design our API to be independent from OpenGL, and all rendering (e.g. done by cgeref:TCastleScene[], cgeref:TCastleViewport[], cgeref:TCastleUserInterface[], cgeref:TDrawableImage[]) is driven by a renderer-neutral API.
+
Shaders will continue to be available using https://en.wikipedia.org/wiki/OpenGL_Shading_Language[GLSL]. There will be possibility to precompile them to https://en.wikipedia.org/wiki/Standard_Portable_Intermediate_Representation[SPIR-V] before usage (for both Vulkan and new OpenGL, as new OpenGL also supports SPIR-V).
+
Underneath, we will create a "pluggable" architecture for renderers. In theory we could implement a lot of renderers (e.g. also Metal or Direct3D) but given the limited resources, realistically we will focus on 2 renderers: Vulkan and OpenGL ("OpenGL" here meaning really a family OpenGL + OpenGLES + WebGL).

* Approach:
+
We have a simple example code that shows how you can start a new renderer: see link:https://github.com/castle-engine/castle-engine/blob/master/examples/research_special_rendering_methods/new_renderer_skeleton/[new_renderer_skeleton example]. This is great for testing initial implementation, before we have a "pluggable" architecture.

* Header:
+
I was playing with https://github.com/BeRo1985/pasvulkan[PasVulkan] from which we could use a Vulkan API header. See https://castle-engine.io/wp/2022/03/27/web3d-webinar-this-tuesday-steam-deck-test-documentation-updates-pasvulkan-tests/[news about PasVulkan].
+
We could also get Vulkan header from https://gen.glad.sh/[GLAD 2] if only we port it to Pascal (we plan this already).

* Platforms:
+
Not all platforms support Vulkan, in particular it's uncertain whether Apple with ever support it (they want to push Apple-specific Metal API). CGE will most likely use https://moltengl.com/moltenvk/[MoltenVK] to render using Vulkan on Apple, see https://castle-engine.io/macos#_apple_says_that_opengles_is_deprecated_what_happens_if_they_remove_opengles_from_macos_and_ios[plans regarding macOS / iOS and OpenGL, Vulkan].

////
### (Optional) Make TCastleScene, TCastleTransform and friends to be special X3D nodes

This would make the whole TCastleViewport.Items a single graph of X3D nodes, allowing for more sharing in code. The TCastleTransform would be just like TTransformNode, but a little differently optimized (but it would become toggable at runtime).

It's unsure now whether this is really needed. Using cgeref:TCastleSceneCore.ExposeTransforms[] and making it better (synchronizing both ways) will fill the most important use-case of this.
////

[#bgfx]
### BGFX renderer

- https://github.com/bkaradzic/bgfx

- Docs: https://bkaradzic.github.io/bgfx/bgfx.html

- Pascal bindings: https://github.com/Akira13641/PasBGFX/ . Though reported as having issues: https://discord.com/channels/389676745957310465/468902667003887646/1173371248596762674 , we shall investigate.

Look great:

- open-source,
- packed with features,
- actively developed,
- open for bindings from any language, any game engine (it is explicitly made as renderer useful for various game engines),
- properly documented,
- very good platform and renderer support.

This *may* be even better than direct Vulkan port. We shall see. On one hand, this provides more possibilities than just Vulkan (e.g. direct Metal renderer). And it may be easier to use than Vulkan. OTOH, it will not allow to squeeze "every last bit of performance from Vulkan" (though we really don't need that in CGE in the first place; there are lots of optimizations on the table in CGE before it's worth even thinking about low-level Vulkan optimizations).

Approach:

- We will introduce a concept of "renderer backend", where a specific renderer can be switched at runtime. Each renderer implements a set of abstract classes, and all calls to rendering will happen through this API.
+
It's similar to how we already have "sound backends" (OpenAL, FMOD, Nintendo Switch-specific, SOX for testing), and "game controllers backend" (WinAPI version, Linux version, Nintendo Switch version). We also have link:castlewindow_backends[TCastleWindow backends] though these are internally realized in a different way, which I no longer like and some day will have to change :)

- This "renderer backend" concept will require some work to split the code. The existing rendering (using OpenGL/OpenGLES/WebGL) will have to be reworked into "OpenGL/OpenGLES/WebGL backend", with new alternative renderer being "BGFX backend".

- To make it easy to experiment with new renderers *before* this "renderer backend" architecture is ready, we have an example where one can experiment with rendering using a different approach without the need for new renderer: https://github.com/castle-engine/castle-engine/tree/master/examples/research_special_rendering_methods/new_renderer_skeleton
+
So in practice, I will likely start doing BGFX by playing with `new_renderer_skeleton` example, to get quick results, and learn BGFX API. Once I'll confirm this is useful route for us, and I have a demo "CGE rendering using BGFX", then I can introduce the "renderer backend" and make BGFX rendering an alternative backend. This way I can first confirm that BGFX rocks :), and learn BGFX, before committing to larger rework to integrate it with CGE properly.

[#opengles_wirefame]
### Mobile: Support TCastleRenderOptions.WireframeEffect on OpenGLES

This is started now, by having `Shape.shading = "WIREFRAME"` option, see https://castle-engine.io/x3d_implementation_shape_extensions.php#section_ext_shading[Shape.shading field]. The implementation makes pretty wireframe (quads and n-gons don't show internal diagonal edges) and it is renderer-agnostic (so it works on OpenGLES too, doesn't rely on `glPolygonMode`), it generates line primitives on our engine side.

To continue we need to:

* Allow renderer to switch between wireframe/non-wireframe rendering without any cost. This means storing the generated `TGeometryArrays` and things they imply (VBOs etc.) for wireframe independently of the non-wireframe data, so that they don't need to be recreated when switching between wireframe/non-wireframe rendering.
* Then we can implement cgeref:TCastleRenderOptions.WireframeEffect[] on OpenGLES correctly.
* Then we can also improve current cgeref:InternalForceWireframe[] (used by editor) to work on OpenGLES and possibly change it to non-internal `TCastleViewport.WireframeEffect` to use wireframe on specific viewports (so that some viewports may view wireframe, independent of others).

### Mobile: On-screen keyboard on iOS (already done on Android)

You can subscribe to https://github.com/castle-engine/castle-engine/issues/554 to be notified about the progress.

### Mobile: TGLImage.GetContents on OpenGLES

`TGLImage.GetContents` is not available on OpenGLES, as it doesn't support `glGetTexImage`. Fixing it is a matter of implementing alternative way that draws texture to FBO and grabs using SaveScreenGL (using glReadPixels internally).

One has to use TGLRenderToTexture to render quad with a texture (making sure the framebuffer size, quad size, and texture size match, and  without filtering) and then use glReadPixels to get the pixels from FBO. This will work on mobile (and also on desktops, although it will be less optimal alternative to glGetTexImage).

### macOS: Package engine as one big application bundle

This will:

- be simpler to users, as the engine will be "one single application" for macOS users.

- avoid troubles resulting from Apple translocation. It right now prevents the CGE editor from detecting CGE path.

- avoid current duplication of `castle-editor/data` in distributed package, thus making it smaller by ~60 MB.

See link:macos[macOS].

### macOS: Have signed app bundle for the editor

This will allow to run CGE editor on macOS by just double-clicking it, out-of-the-box.

See link:macos[macOS].

We should also enable developers to easily sign their own applications packaged with CGE build tool.

### Bridge TDrawableImage with TCastleScene, enable drawing UI into TCastleScene, to have UI rotated in 3D with perspective.

link:how_to_render_2d[See also here.]

### MDL and/or MaterialX integration

- MDL, https://developer.nvidia.com/rendering-technologies/mdl-sdk

- MaterialX, https://materialx.org/

We can make 3D modeling of realistically-looking stuff much easier by reusing libraries of realistic materials.

### USD – Universal Scene Description support

Explore possibility to support https://graphics.pixar.com/usd/release/index.html[USD – Universal Scene Description] format as a first-class citizen, alongside glTF and X3D, in CGE.

### Integration with Nakama (scalable server for social and real-time games and apps)

Integration with https://heroiclabs.com/docs/nakama/getting-started/index.html[Nakama]:

Nakama is an open-source server and client library/API that provide networking capabilities typically needed in games.

- The server is written in Go (see link:https://github.com/heroiclabs/nakama[source code]) and can be extended using Lua, JS, or Go modules. See https://heroiclabs.com/docs/nakama/server-framework/introduction/index.html[server framework].

- It supports out-of-the-box stuff like user accounts, matchmaking (connect people into rooms where the game happens), leaderboards, chat, etc. There's infrastructure to synchronize data between players in a multi-player game.

- Client library can be used from link:https://heroiclabs.com/docs/nakama/client-libraries/[various game engines and languages] so we're confident it can be used from CGE too.

- You can host the server yourself or pay to get ready hosting.

- As you can see in above links, it seems really well documented, and actively maintained. These are naturally critical qualities for our dependencies.

I think Nakama may give CGE networking capabilities comparable with some popular solutions known from other game engines :)

### Related: Ready components to replicate data over the Internet

Allowing to trivially get multi-player functionality in your games.

### Project settings editable using GUI

We will expose both current `CastleEngineManifest.xml` and `data/CastleSettings.xml` as something you can edit yourself with GUI.

But we *don't want* to create and maintain a custom GUI for them, instead the plan is to:

* Create Pascal components with published properties representing their data.
* Make the project settings files just "JSON design files" for the editor.

So you'll have a file like `project.castle-project` and using editor menu item like _"Project Build Settings"_ will really just open a design `project.castle-project` (JSON) in the editor, and it will be just a serialized `TCastleProject` component. Same for _"Project Runtime Settings"_ which edits `data/settings.castle-project-settings`.

Bonus: we'll associate `*.castle-project` with the system, to open when you double-click it e.g. from Windows installer.

Bonus: you can add non-visual components to `data/settings.castle-project-settings` and load them using `Container.DesignedComponent('xxx')`. This is great e.g. to add sounds or fonts or other data that you want to later load in CGE, from any view.

There are a few details that have to be ironed out here:

* how should the new equivalent of `CastleEngineManifest.xml` and `data/CastleSettings.xml` interact. We will likely keep 2 files, `project.castle-project` and `data/settings.castle-project-settings`, as
+
--
- Some information doesn't need to be packed into data, part of it could even be considered "somewhat secret" (e.g. SDK IDs for Android analytics should *not* be present anywhere in the non-Android builds).
- Some information should be packed into data automatically, by default, everything. To enable that _Bonus: you can add non-visual components_ feature.
--

* most intuitive names for all the things above (menu item names, component names, extensions etc.) :)

Moreover:

- Expose cgeref:CastleDataIgnoreCase[] in `data/settings.castle-project-settings`. This makes sense to be read also by editor when editing the project, to ignore case also at design-time.

[#anims]
### Creating animations in CGE editor, for UI and transforms

Allow designing and testing animations in CGE editor.

The features cover animating typical UI/transform things that you see in CGE editor directly: e.g. UI stuff (like `TCastleUserInterface.Translation`, `TCastleImageControl.Color`), 3D stuff (`TCastleTransform.Translation`, `TCastleTransform.Rotation` etc.).

NOTE: Animating everything in CGE is already possible right now _using code_. You can update any values you want in `Update` method of your link:views[view] and change any value you need (like `MyControl.Translation`) using routines like `Lerp` or `SmoothStep`.

The point of this feature is to allow to design and see simple animations from editor. Code will then be able to just play them (e.g. using simple method `MyAnimation.Play`) whenever needed.

Features:

- Make it possible to animate (at least!) any property of cgeref:TCastleUserInterface[] and cgeref:TCastleTransform[]. Access the property using RTTI, and at design-time in editor allow to choose `TargetObject` and `TargetProperty` with suitable type.
+
Bonus: allow to animate any property of any `TComponent`.

- It should allow to animate at least any `Single/Double`, `TVector2/3/4` properties (this includes also colors, as `TCastleColor[RGB]` is just alias for `TVector3/4`; this includes also rotations, that is just `TVector4` but can use slerp for interpolation).
+
Bonus: allow to animate other property types. E.g. animating discrete (like boolean) actually makes sense -- to hide stuff by animation, by changing `Exists` at specific point in time.

- It should use `TCastleBehaviorBase` that will allow to attach it to any cgeref:TCastleUserInterface[] and cgeref:TCastleTransform[]. See talk about it in https://github.com/castle-engine/castle-engine/issues/473 .

- Each such animation should define:
+
--
- key-frames
- key-values
- configurable target component (TComponent)
- configurable target property (string? and get/set property using RTTI)
- easing (e.g. to make "bouncing" effect at the end of animation), https://easings.net/ https://developer.mozilla.org/en-US/docs/Web/CSS/easing-function . Bonus: easing code and enums could be also used to acceleration/deceleration of `TCastleInputAxis`.
--

- Bonus: Group animations, so that from code you can fire like `MyUserInterfaceAppear.Play` and it runs a number of animations at once -- e.g. something "slides in" (translation changes), something appears (Exists changes), something fades in (Color.alpha changes)

See also:

- PR https://github.com/castle-engine/castle-engine/issues/473 and animation-player branch in this fork https://github.com/Freedomax/castle-engine/tree/animation-player
- Nice lean tweening code, independent from engine: https://github.com/glhrmfrts/wetween

[#screen_effects]
### Screen effects - expose as nicer components

Address a few shortcomings of our  (see `examples/screen_effects_demo/`, link:x3d_extensions_screen_effects.php[screen effects]):

- Change terminology. "Screen effect" is non-standard, "post-processing effects" are better.

- Expose as components some standard effects, like blur, changing colors in HSV, grayscale. They should have easy Pascal properties, serializable to JSON, and wrap the related X3D nodes underneath.

- Expose as component a customizable shader effect allowing you to provide GLSL code in a text box (TStringList editor) in CGE editor.

- This depends on making a more generic link:#component_list[TCastleComponentList] to expose `TCastleScreenEffects.ScreenEffects` list nicely.

[#gltf_save]
### Export (save) nodes graph to glTF

Currently we can export (save) nodes graph (from one cgeref:TCastleScene[] or, using cgeref:TCastleTransform.InternalBuildNode[], from whole cgeref:TCastleViewport[] contents) to X3D or STL.

We want to enable also saving to glTF. This means that _everywhere_ where we allow saving, glTF will also be an output option:

- link:castle-model-viewer[], link:castle-model-converter[] should be able to save to glTF (from any input model, like X3D, STL, or another glTF).

- The editor menu item _"Data -> Export Viewport to X3D, STL"_ (see https://castle-engine.io/wp/2024/09/18/export-3d-or-2d-world-designed-in-castle-game-engine-editor-to-x3d-or-stl/[news]) will be able to save to glTF.

- API: cgeref:TCastleSceneCore.Save[], cgeref:SaveNode[] will be able to save to glTF.

- https://castle-engine.io/convert.php[Our online converter] will be able to save to glTF.

GitHub issue: https://github.com/castle-engine/castle-engine/issues/595 .

### Allow animating selected node graph properties, without changing the graph, to be able to reuse graphs

NOTE: We've made a start toward this, specifically for link:skin[skinned animation], in internal `TCastleSceneInstance` (right now doesn't render, but maintains customized state and can calculate bbox).

Right now our X3D graph usage is similar to HTML DOM usage from typical web applications: we modify the graph at runtime, everything that changes stuff (like animations) just changes the graph. This is simple to use, it nicely allows to animate _everything_ in X3D and gives developers an easy way to modify everything at runtime.

The downside is that we waste significant memory, and loading time, in some cases. If you want to play different animations on each cgeref:TCastleScene[], then they need to have a different graph even though they are loaded from the same `URL`. Hence using e.g. cgeref:TCastleScene.Clone[] or cgeref:TCastleSceneCore.Cache[] really internally makes multiple copies of the graph. This could be more optimal in the future. E.g. assuming your model only uses glTF skinned animation or animates simple transformation position/rotation/scale, we could make the graph read-only, and only maintain small "diff" information at each instance.

[#fmod_studio]
### FMOD Studio support

Right now our link:fmod[FMOD backend] uses only _"FMOD Core API"_.

We want to enable using higher-level FMOD events that drive the _sound banks_ exported from "FMOD Studio".

[#wwise]
### Wwise backend

Allow using https://www.audiokinetic.com/[AudioKinetic's Wwise] as sound backend, and as a middleware to design sounds.

[#async]
### Asynchronous loading of assets

As described in https://castle-engine.io/threads[threads], we want to allow loading assets *asynchronously*. This means that you use CGE API from one thread, but some operations are not "blocking" and CGE can internally use threads to finish their work, while your code continues to calculate, CGE events like `Update` continue to be processed etc.

For example:

[source,pascal]
----
TCastleScene.LoadAsync(const Url: String; const OnFinish: TNotifyEvent);

TCastleScene.PrepareResourcesAsynchronously;

TCastleTerrain.UpdateDataAsync(const NewData: TCastleTerrainData; const OnFinish: TNotifyEvent);
----

We already use such approach e.g. with cgeref:TCastleDownload[]. Internally it can download in a thread, but you don't need to worry about inherent "hard" issues with threads (synchronizing communication between threads, being careful who/when touches the shared variables). This approach results in:

- Really comfortable API. You handle everything from one thread, you get simple callbacks when things are done, you can simply watch (e.g. in some `Update`) status of something, display progress bars, interrupt the operation etc.

- Flexible implementation underneath. It can use Pascal threads underneath, or it can delegate work to another asynchronous API (e.g. to download using Java classes on Android), it can even fallback to synchronous if threads are not possible on some platform.

And it is possible to load OpenGL things in a thread: by having separate OpenGL context for the background thread, that shares resources (like VBO).

The ultimate demo is to implement exploring a huge city and loading neighboring pieces of the city asynchronously.

I mentioned this in the bottom part of https://castle-engine.io/wp/2022/10/12/slides-movies-and-thoughts-from-my-gic-2022-presentation/[Slides, movies and thoughts from my GIC 2022 presentation] .

### Merge TCastleImagePersistent and TDrawableImage

One less image class (we have too many image classes, see https://castle-engine.io/using_images ).

And it will avoid current counter-intuitive fact:

```
ImageControl.DrawableImage.CustomShader := Xxx;
```

The above is ignored, because `ImageControl.CustomShader` (equal to `ImageControl.Content.CustomShader`) overrides `TDrawableImage.CustomShader`. Similarly a few other drawable image properties, as `TCastleImagePersistent.Draw` does now:

[source,pascal]
----
{ All the DrawableImage properties must be set here,
  in case multiple TCastleImagePersistent refer to the same
  DrawableImage instance through the cache.
  Fortunately, almost all TDrawableImage properties have zero cost
  of change. }
DrawableImage.SmoothScaling := FSmoothScaling;
DrawableImage.Clip := FClip;
DrawableImage.ClipLine := FClipLine;
DrawableImage.Rotation := FRotation;
DrawableImage.RotationCenter := FRotationCenter;
DrawableImage.Alpha := FAlphaChannel;
DrawableImage.Color := FColor;
DrawableImage.CustomShader := FCustomShader;
----

### Smart Tasks

Allow to define tasks that should occur regularly, but do not have to be done every frame. The engine should then automatically execute the tasks based on their need and current workload.

E.g. animation task may say "I want to update the model every frame, but actually I don't have to -- if under heavy workload it's enough to only update 30 times per second or such". This would be a better (more general, smarter -- activated automatically) way of optimizing it than current `TCastleScene.AnimationSkipTicks`.

[NOTE]
====
The API (terminology) and implementation have to play nicely with Delphi existing concept of "tasks" from Delphi _Parallel Programming Library_. Delphi already has a "task" meaning "a unit of work that can be executed in auto-created thread (using pool of threads underneath)", see

- https://docwiki.embarcadero.com/RADStudio/Sydney/en/Tutorial:_Using_Tasks_from_the_Parallel_Programming_Library[Tutorial: Using Tasks from the Parallel Programming Library]

- https://docwiki.embarcadero.com/RADStudio/Sydney/en/Using_TTask_from_the_Parallel_Programming_Library[Using TTask from the Parallel Programming Library]

- https://docwiki.embarcadero.com/Libraries/Sydney/en/System.Threading.TTask[TTask]

- https://blog.marcocantu.com/blog/2017-november-explaining-tasks-ppl-too-many.html[Explaining Tasks in Delphi Parallel Library... by Creating too Many Tasks]

So:

1. Our tasks, that are mainly to execute tasks in the main thread but with some delay ("execute this when convenient, I don't need it done right now") should be clearly something different. To avoid confusing API for people that know Delphi tasks.

2. When using threads, then we should use Delphi tasks (on Delphi), where possible. Only for FPC we'll need to duplicate subset of it (unless FPC will get the compatible implementation).
====

[#ktx2]
### KTX 2.0 support

https://www.khronos.org/api/index_2017/ktx

A great format, by Khronos. Enables managing compressed textures much better. (than our existing solution: https://castle-engine.io/creating_data_auto_generated_textures.php )

[#expose_to_code]
### Expose component name for Pascal code and make sure `uses` clause pulls all units to register components in the design

Right now to access a component set up in the design, you have to manually add it to the `published` section of the design, adding a declaration like `MyViewport: TCastleViewport`.

The goal is to make it more automated. A bit like Lazarus or Delphi IDE when exposing components to code -- but with some twist.

It would be easy to just expose *all* named components from the design in the `published` section of the view (this would be experience quite like when Delphi/Lazarus do) but, since typical game designs (we've seen it in Unity games in CAG) can contain thousands of components easily, and most of them are not supposed to be accessed from code, we want to go one step further:

- Add a button "Expose To Code" (available on single component, or selection of components).

- Once used, the component will be added to the `published` section of the view (`name: type`), and will be updated there (on rename/delete of this component, or change of it's class).

- This is also means we'll edit the `uses` clause of unit to make sure all used component classes are registered, and declared, so that declarations in `published` section work. This is also something Delphi/Lazarus do. We know the unit of each component, so it should be easy.
+
NOTE: This is actually independent from whether the component is listed in the `published` section or not. The unit defining it must be listed in the `uses` clause, to register it and be able to deserialize it.
+
NOTE: Which unit should have the `uses` clause extended? `GameInitialize`, `CastleAutoGenerated`, or particular view's unit like `GameViewXxx`? The last option (`GameViewXxx`) is best, so that, if the view is not used in the application, then also the dependencies are not pulled. This is also most similar to what Delphi/Lazarus do for forms.

It is possible we'll add markers `{$region 'Castle ...'}` to help organize this. See the current markers in `gameinitialize.pas`.

[#design_overrides]
### Allow to override design properties when instantiated using TCastleDesign, TCastleTransformDesign

Allow to customize the hierarchy instantiated through cgeref:TCastleDesign[] or cgeref:TCastleTransformDesign[].

In Unity, this is called _"prefab overrides"_. In Delphi/Lazarus, _frames_ also offer this functionality.

The idea is that, in the "outer" design, we can save only the differences from your original design to the customized version.

See link:custom_components[Custom components] documentation, and the section _"Using designs to visually set up default state of your component"_.

[#tiled_layers]
### New API for Tiled layers

- New class `TCastleTiledMap.TLayer = class(TCastleComponent)`
+
TODO: Make sure it can be serialized OK. Fallback on `TCastleTiledMapLayer` if necessary. But we prefer `TCastleTiledMap.TLayer`.

- New published property `Layers:TCastleComponentList<TLayer>` inside `TCastleTiledMap`.
+
Depends on link:#component_list[TCastleComponentList] plans. We want to serialize the properties of layers this way.

- Remove existing `Layers` set from `TCastleTiledMap`.
+
Write code in `TCastleTiledMap.CustomSerialization(const SerializationProcess: TSerializationProcess);` to upgrade existing designs to new API.
+
Pascal code using old `Layers` will just be broken and have to be upgraded, acceptable.

- `Exists` should be in `TCastleTiledMap.TLayer`, and not in `TCastleTiledMapData.TLayer`.
+
Should toggling it toggle also `TCastleTiledMapData.TLayer.Visible`? Or let `TCastleTiledMapData.TLayer.Visible` stay as "initial value"? Undecided, not really very important.

- The long-term goal is to have nice components for layers and maybe more (tiles), inside `TCastleTiledMap`, with published properties, that can be designed in CGE editor, and that are serialized using existing system to JSON.
+
It should be possible to design layers (and maybe more, like tiles) in CGE editor.
+
This way we can go, step by step, to having own Tiled-like editor in CGE, like Godot or Unity.
+
At the same time, we want to support https://www.mapeditor.org/[Tiled] (as external application for map editing) and support it fully, because Tiled is great! and it may take us long time to actually have fully useful tile editor in CGE editor. The road should be that `Data` stuff, and everything in `TCastleTiledMapData`, becomes less and less useful. It's an internal detail whether our public `TCastleTiledMap.TLayer` has a reference like `Data:TCastleTiledMapData.TLayer` or not.

[#tiles3d]
### Built-in tile editor in CGE, with support for 3D models on a grid

Built-in Tiled-like editing features in CGE would be great.

- With ability to initialize them from https://www.mapeditor.org/[Tiled] data, and using similar API as we have for Tiled, see also link:#tiled_layers[plans for improving Tiled API].

- With ability to design stuff in 3D. See:
+
--
- Unity: https://www.youtube.com/watch?v=u8zJnXnZiFE
- Unreal: https://www.youtube.com/watch?v=DwPHsLJv5Gw
- https://forum.castle-engine.io/t/request-for-a-tcastlegrid-component/839[CGE forum thread]
--

This is admittedly a long-term goal. A whole system for this, esp. with 3D, would be cool but also a significant new work.

What will likely be done sooner: "snapping" for 3D operations, that fils some subset of this functionality, e.g. allows to put 3D buildings on a grid.

[#expose]
### ExposeTransforms is a powerful idea that can cover more use-cases

See link:expose[docs] and cgeref:TCastleSceneCore.ExposeTransforms[]. It already contains TODOs showing how it *should* work like.

Moreover fix problem with names:

* cannot duplicate model with ExposeTransforms now
* cannot add another model with same bone names, before customizing bone name prefixes
* solution: remove ExposeTransformsPrefix, rather add new property `BoneName` that doesn't have to be unique and can be used for synchronization
* document this on link:expose[docs]

Moreover, maybe:

* ? Should expose the parent bones too (otherwise editing from code is hard, and applying edits back is hard -- as you see a combined transformation only)
* ? Rename to just `Expose`? `ExposeTransforms` sounds convoluted.

[#edit]
### Improve editing (TCastleEdit, planned TCastleMemo)

- Moving cursor freely in text (clicking, arrow keys, ctrl+arrow keys to move by words)
- Free selection of any text subset (clicking, shift+arrows)
- Multiline display (thus TCastleMemo, which should internally share code and be essentially TCastleEdit with > 1 line)
- Right click with contextual menu - copy, paste, cut, select all, use platform-specific input method
- Manual focus. Clicking on edit should keep it focused, no matter where the mouse moves. Tab or clicking elsewhere switches focus. Right now you can workaround it / do it manually by setting `Container.ForceCaptureInput` to the edit control.

All above will have to be synchronized with on-screen keyboard behavior on Android.

Moreover we want to support on-screen keyboard behavior on iOS as well, see https://github.com/castle-engine/castle-engine/issues/554 .

[#portable_editor]
### (IN PROGRESS) Editor on the web

This is started:

- Run it here: https://castle-engine.io/web-demos/tools/castle-editor-portable

- See source code: https://github.com/castle-engine/castle-engine/tree/master/tools/castle-editor-portable

- See announcement here: https://castle-engine.io/wp/2025/05/24/web-target-progress-editor-on-web-proof-of-concept-openurl-right-mouse-button-many-new-demos-creature-ai-3d-model-viewer-physics-shooting-auto-tests-kraft-and-vampyre-imaging-on-web-wa/

The goal is simple: turn above into a fully-functional editor, matching our link:editor[real editor] capabilities.

[#dark_mode]
### Editor Dark mode

cgeimg::block[editor_dark_mode.png|Dark Mode design by Adrianna Matejek]

I can't count how many times I've been asked _"when is the Dark Mode coming to CGE link:editor[]"_ :)

1. One answer: It is already possible and easy on non-Windows.
+
We use LCL and our editor just follows your OS (Windows, GTK, ...) theme. If you switch your system theme to something dark, then all your applications (including CGE) will become dark. This already works great e.g. on Linux or FreeBSD with GNOME, just switch your theme using GNOME settings and enjoy dark mode for everything.
+
Unfortunately, on Windows, there's no setting to change the WinAPI controls to use the dark theme.

2. On Windows you can use https://github.com/zamtmn/metadarkstyle[MetaDarkStyle] Lazarus package.
+
We have a https://github.com/castle-engine/castle-engine/pull/555/[PR with this], not merged yet. You can easily do this yourself:
+
--
- Get link:compiling_from_source.php[Castle Game Engine sources and learn how to build editor yourself].
- Get https://github.com/zamtmn/metadarkstyle[MetaDarkStyle] package.
- Add it to the CGE editor application, and apply the trivial code changes to editor source from https://github.com/castle-engine/castle-engine/pull/555/[this PR].
--

3. _Big Plan_: The ultimate solution, that will allow to switch dark mode on/off on all platforms with exactly the same results, will come from reimplementing CGE editor using CGE link:user_interface[user interface components]. We made a decision some time ago, and it will definitely happen -- but it's still a big task, and it will take some time. The major benefits of it will be:
+
--
- We will be able to use the editor on all platforms there are CGE targets. In particular, our link:web[web target] means you will be able to create and edit your games in your browser! Or even on mobile (but don't hold your breath for making games on mobile -- the form factors of mobile devices will likely make UX of CGE editor on mobile not very good).
- It will necessitate CGE UI improvements, from which all CGE applications will benefit. For example, ready tree view, object inspector and list components.
- It will allow to turn our link:https://www.youtube.com/watch?v=5jBdPdj75yk["inspector at run-time"] into full-featured "editor at run-time".
- It will allow to avoid some LCL limitations, sometimes widgetset-specific. E.g. LCL GTK 2 widgetset uses deprecated GTK 2 and has https://github.com/castle-engine/castle-engine/issues/543[known crashing issues], Qt5 widgetset needs libqt5pas library that most people don't have (or don't have in relevant version for latest LCL), macOS widgetset https://github.com/castle-engine/castle-engine/issues/404[does not have a functional frames list and thus sprite sheet editor sucks]... We will be able to ignore all of this, as they will be replaced by a cross-platform implementation.
- And, last but not least: the dark mode, on all platforms, will be trivial to implement :)
--
+
*This is started already*, see link:#portable_editor[Editor on the web].

### Environment Lighting

- Finish https://github.com/michaliskambi/x3d-tests/wiki/Image-Based-Lighting-(EnvironmentLight-node)[EnvironmentLight X3D node]
- On top of that CGE component `TCastleEnvironmentLight`

It will work for all models, it is a great and modern way to do lighting.

[#animations_optimized]
### Optimized animations (OptimizeExtensiveTransformations, InternalFastTransformUpdate) just out-of-the-box

As https://castle-engine.io/wp/2024/06/07/new-example-demonstrating-a-few-possible-animations-optimizations/[this post] discusses, we have a few options to speed up animation processing.

Not all, but *some* of them should be "on" by default and have no possible adverse effects:

- cgeref:InternalFastTransformUpdate[] enable by default and make it work regardless of what is animated.

- cgeref:OptimizeExtensiveTransformations[] we should generalize it by remembering the top-most transform that needs to be processed.

[#reloading]
### Improvements to auto-reloading changed data

In _Castle Game Engine_, we can auto-reload data files (https://castle-engine.io/using_images[images (in TCastleImageControl, TCastleImageTransform)], https://castle-engine.io/viewport_and_scenes[scenes], https://castle-engine.io/tiled_maps[Tiled maps]) when they changed on disk. This is great when quickly prototyping the game, you can just save the file in external program (like Blender, GIMP, Tiled), _Alt+Tab_ to the editor (or even your running game), and see how the new data "fits in".

Auto-reloading in link:editor[] (we call it _"at design-time"_) is done automatically. Auto-reloading during the running game (_"at run-time"_) must be explicitly enabled, by running the game in debug mode, then entering the inspector (using F8 key) and checking the _"Monitor and Auto-Reload Data"_ checkbox.

The above describes what already works :)

Below are some missing things related to this:

1. Changing textures used in a scene doesn't cause reload of the scene.
+
For example, when cgeref:TCastleScene[] has `xxx.gltf` loaded, and `xxx.gltf` refers to texture `my_texture.png`, then changing xxx.gltf correctly reloads the scene, but changing `my_texture.png` does not.
+
In general, cgeref:TCastleScene[] doesn't keep a centralized knowledge "all the files that are my dependencies".
+
Implementing this knowledge is a TODO, it would be good to have it. For auto-reloading, also for possible auto-detection of unused files in data. To expand on the latter idea: While we will probably always pack whole `data` to package (so it's like Unity `Assets/Resources`), it would be nice to have a button to allow to clean it manually ("list files in data that seem unused") or have a subdirectory in data (like `data/managed/`) where only used data is packaged.

2. Caching right now sometimes "defeats" reloading. If something is in the cache and is referenced at least 2 times (e.g. there are 2 cgeref:TCastleImageTransform[] components referring to the same image or you use cgeref:TCastleSceneCore.Cache[] and the same scene URL is loaded 2 times), then reloading doesn't work.
+
The underlying reason is that cache never "lets go" of the loaded resource, because always someone refers to it, even as cgeref:TCastleImageTransform[] is in the middle of cgeref:TCastleImageTransform.ReloadUrl[].
+
Fixing this means reimplementing our `ReloadUrl` routines to reload the resource in the cache. Likely a way to notify all users of this resource "this is changing" will be necessary.

3. We depend on LCL `TApplicationProperties.OnActivate` mechanism to detect the file changes when you switch back to CGE editor application. Unfortunately, it is not reliable with LCL GTK2 backend.
+
Details: Sometimes the `TApplicationProperties.OnActivate` event fires when it should, sometimes it doesn't... You can _Alt+Tab_ between CGE editor and some other application, and see that sometimes `TApplicationProperties.OnActivate` just doesn't happen. This was reproduced also with a trivial application (unrelated to CGE editor). It also happens with Lazarus IDE itself -- Lazarus IDE seems to use similar mechanism to detect when Pascal file, opened in Lazarus, changed on disk (e.g. because you also edit it in VS Code). And sometimes, when you _Alt+Tab_ between Lazarus and VS Code, Lazarus doesn't notice the file change.
+
The GTK2 can correctly send the relevant event (`focus_in_event`), which is used both by CGE `TCastleWindow` implementation on GTK, and Lazarus LCL GTK backend. So it doesn't seem to be GTK2 fault. A similar mechanism just works reliably for `TCastleWindow`.
+
We haven't found any useful workaround, no other LCL event seems to receive any event that could be useful to workaround it. We have to submit a bugreport for LCL GTK2 (if you want to handle this, please let us know, we can use a help!). And possibly switch to something else (but Qt5 backend also has problems, using libraries that most people don't have; and it will take some time before we can implement editor without LCL).
+
The end result: CGE editor on Linux sometimes does not reload the data that changed. However, CGE applications, with "Monitor and Auto-Reload Data" selected, do reload the data reliably at run-time.

[#saving_float_images]
### Saving float-based images preserving their precision

We can read float-based images, into these 4 image formats:

- cgeref:TGrayscaleFloatImage[],
- cgeref:TGrayscaleAlphaFloatImage[],
- cgeref:TRGBFloatImage[],
- cgeref:TRGBAlphaFloatImage[].

We can read from KTX, PNG, TIFF.

However, we cannot *save* them now.

- It doesn't seem Vampyre supports saving TIFF, it always crashes.
- We didn't test can Vampyre save 16-bit PNG images.
- We miss code to save 16-bit PNG images using LibPng.
- We miss code to save float images to KTX.

[#records_rtti]
### Remove the need for "record wrappers" like TCastleVector3Persistent, instead serialize/deserialize directly from TVector3, and expose in editor TVector3, as RTTI for them is available

See the https://lists.freepascal.org/pipermail/fpc-devel/2024-August/045761.html["Published record" thread on fpc-devel].

NOTE: This will require link:supported_compilers.php[bumping required FPC version], but it's worth it, as it will simplify a lot of code in CGE (and for users that define custom components). And before we get to this in CGE, possibly new FPC release(s) will happen, and most users will have the required FPC version anyway :)

While records (like `TVector3`) cannot be `published` in FPC, the information about `public` records is available using RTTI. We should add

```delphi
{$RTTI EXPLICIT
  PROPERTIES([vcPublic])
  FIELDS([vcPublic])
  METHODS([vcPublic])}
```

(or only `PROPERTIES([vcPublic]))`, seems enough?) to `castleconf.inc` and recommend it in link:custom_components[Custom components] documentation.

Then for `public` records, like this:

```delphi
TCastleTransform = class
public
  property Translation: TVector3 read ... write ...;
  property Scale: TVector3 read ... write ...;
end;
```

we should be able to read/write them using RTTT, hopefully with code mostly compatible with both FPC and Delphi.

On high level, this means:

1. We can enhance cgeref:CastleComponentSerialize[] to serialize/deserialize records. Do it, at least for our important records (`TVector2`, `TVector3`, `TVector4`...) or (if no big obstacles) for all records in general.
+

2. We can enhance the object inspector, used in CGE editor, to show records. See https://gitlab.com/freepascal.org/lazarus/lazarus/-/tree/main/components/jitclasses?ref_type=heads[lazarus/components/jitclasses/].

We shall be able to remove then:

- All wrapper classes `TCastleVector3Persistent` etc.
- All fields using them, like `TranslationPersistent: TCastleVector3Persistent` etc.
- Generator in `tools/internal/generate-persistent-vectors/`
- Complicated explanation about it in link:custom_components[Custom components] documentation.

### (IN PROGRESS) Options to deal with resources (like textures) when saving

PR: https://github.com/castle-engine/castle-engine/pull/636

There are a few useful options we could offer for saving, from all cases (from "Data -> Export to X3D, STL..." in editor, from https://castle-engine.io/castle-model-viewer , from https://castle-engine.io/castle-model-converter ):

- embed all resources using data URI (then the resulting big X3D is "self-contained", you can e.g. use it with https://castle-engine.io/castle-model-viewer-mobile ).

- or copy all resources to the target directory (this could be similar to an option offered by Blender -> glTF exporter, it can also copy textures on save).

The term "resources" means here everything -- other files referred to be `Inline` nodes, textures, audio files, shader files etc.

### Python integration

We want to allow using Python to develop _Castle Game Engine_ games.

NOTE: Thanks to _Kagamma_ (_Trung Le_, aka _kdoll_ on our Discord), we have a sample how to implement option 1 described below, i.e. a _behavior_ that allows to edit a piece of code in a scripting language in our editor: https://github.com/Kagamma/cge-script-integration-example .

Two use-cases are important:

1. Use Python just for adding some behaviors.
+
This means implementing a class like `TCastlePythonBehavior` with `PythonCode: TStringList` that descends from link:behaviors[TCastleBehavior] and allows to write Python code that can be executed at both design-time and run-time. This code should have access to all CGE API.
+
The main advantage here is that, in contrast to implementing link:behaviors[TCastleBehavior] in Pascal, Python code can run at design-time. The practical advantages of it:
+
--
- You don't need to rebuild the editor to change the `published` properties of the Python behavior in the editor's object inspector, so the workflow is a bit faster than using link:custom_components[custom Pascal components in editor]. (Python doesn't have `published` but we'll invent equivalent mechanism).

- You can test the execution in editor's _"Play Simulation"_ mode (now only used to test physics). You can quickly iterate on some code, by just using _"Play Simulation"_ from editor, and editing the Python code in the editor.
--
+
Overall, this approach should be useful for everyone, including Pascal developers. You don't resign from coding in Pascal, you just move some logic to Python. For some behaviors you lose some advantages of Pascal (type-safety checked at compile-time) but you gain some advantages of Python as a scripting language (you can iterate on some ideas faster, test them in editor).

2. Write the whole game in Python.
+
This means you write the whole game code in Python. In particular, view's code is in Python, so e.g. you have a design `gameviewmain.castle-user-interface` and a corresponding Python code in `gameviewmain.py`. The Python code defines `ViewMain` class.
+
This makes our engine available for Python developers. We get an additional crowd of people interested in our engine. To be clear, you can still mix Python with Pascal code, but the main use-case is here for people who want to write the whole game in Python.

*Why?*

- Choosing Python is enticing. It's very popular. It does not have a game engine like CGE, from what we can tell (comfortable editor, powerful 3D and 2D, open-source). (Unity used to have "Boo", but it is now removed.) So this part of the reason is "opportunistic", i.e. what we offer may be very attractive to Python developers.

- We have https://github.com/pyscripter/python4delphi project providing feature-rich integration. Very active, tested by both Delphi and FPC users. These are important point for something we would depend on.

- If someone is looking for a "simple scripting language", Python is closer to "super simple" than e.g. C#.

- Python is similar to https://docs.godotengine.org/en/stable/tutorials/scripting/gdscript/gdscript_basics.html[GDScript] known from Godot.

- This idea was somewhat validated at https://castle-engine.io/wp/2023/07/08/international-pascal-congress-my-presentation-slides-various-positive-thoughts-about-pascal-both-fpc-and-delphi-bright-future-and-why-you-should-use-pascal/[2023 conference in Salamanca]. Namely, Embarcadero tries to do a similar thing with FMX - offer it as library for Python devs, see https://github.com/embarcadero/DelphiFMX4Python[DelphiFMX for Python].

To be clear, this doesn't preclude integration with other scripting languages. Other reasonable choices exist and we welcome work on integrating them:

- _Lua_ is a popular choice in gamedev. One can implement something like `TCastleLuaBehavior` that allows to paste LUA scripts that interact with CGE.

- https://en.wikipedia.org/wiki/Pascal_Script[Pascal Script] is a natural choice for us. And it would bring us the benefits of "it can run at design-time" while staying within the same (well, similar) programming language.

- JS using QuickJS is an option too, and it's already being worked on. See `#quickjs` channel on our link:talk.php[Discord] and https://github.com/castle-engine/quickjs-demo[quickjs-demo repo].

[#materials]
### New material components (allowing also material editing in the editor)

We want to allow you to edit materials in the CGE editor,

- e.g. to modify (or completely override) materials from TCastleScene (like glTF),

- to have full control over primitives (like `TCastleSphere` or `TCastleImageTransform` look),

- to enable you to use cool CGE features like link:x3d_extensions_mirror_plane.php[mirrors], link:shaders[shader effects] easier.

This also means exposing new set of Pascal components to manage materials easier.

See https://github.com/castle-engine/castle-engine/files/10324944/new-material-components.txt for rough plans.

NOTE: This feature has been demoted from "must have for 7.0" to "we can do it after 7.0". Because engine makes sense without it, we have to limit our scope for release at some point :), and our link:shaders[shader effects] got some updates (to API and docs) making them easier to use (which satisfied some of reason for new material components).
