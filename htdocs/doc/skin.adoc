# Skinned animation using the Skin node
include::common.adoc[]
:description: Design the skinned animation skeleton and joints in Castle Game Engine.
:cge-social-share-image: skin_example_screenshot.png

WARNING: This feature is not yet merged to `master`. It's on https://github.com/castle-engine/castle-engine/tree/skinned-animation-gpu[skinned-animation-gpu] branch.

== Introduction

You can use _skinned animation_ when designing models in _Castle Game Engine_. This means that your 3D model defines a number of _joints_ (aka "bones") that can be transformed (e.g. to make a _"walk"_ animation) and cause a _mesh_ (aka "skin") to animate accordingly, following the joints.

In most cases, you don't need to do anything special on the engine side to use it. Simply design models using link:blender[Blender] with <PERSON><PERSON><PERSON>'s _armature_ and _bones_, export it to link:gltf[glTF], and load it in _Castle Game Engine_ as usual. A 3D model in _Castle Game Engine_ is displayed and processed using the cgeref:TCastleScene[] component, documented in link:viewport_and_scenes[Viewport with scenes, camera, navigation].

The engine will automatically load the skinned animation from glTF, and display it correctly. We can perform the animation on the GPU, which means it's efficient. We also have a fallback (for ancient GPUs) non-GPU implementation.

cgeimg::block[
  skin_example_screenshot.png|Skinned animation example - change joints by <PERSON> code,
  skin_fox.png|Animated fox,
  skin_bunny.png|Animated bunny,
  skin_alien.png|Animated alien
]

== Skin node under the hood

Each cgeref:TCastleScene[] is actually a graph of link:x3d[X3D nodes] (rooted in cgeref:TCastleSceneCore.RootNode[]). The nodes control everything -- display, animation of the model.

The skinned animation is realized using the cgeref:TSkinNode[], described on this page. It's connected to the `skinWeights0` and `skinJoints0` fields of most geometry nodes.

Understanding how it works allows you to design or control the animation of characters yourself, from Pascal code. For example, you can load your skinned animated characters from your custom 3D model formats, you can implement inverse kinematics or do any other direct manipulation of joints from code.

The documentation below accounts for two ways how you can build X3D nodes:

- You can just write X3D content in a text file, with extension `.x3dv` ("classic" syntax) or `.x3d` (XML syntax). The node definition then looks like `Skin { ... }`. See link:#example[example below] for more.

- You can build the X3D nodes graph in Pascal code. In this case, you create an instance of the cgeref:TSkinNode[] class. The properties of this class map one-to-one to the `Skin` node from X3D.

== Overview

We define a new node called `Skin` (Pascal: cgeref:TSkinNode[]). It allows to design a skinned animation (using skeleton with joints, and shapes with skin weights).

It has the following properties (click on each property for more detailed API docs):

////
|===
|Property type|X3D access type|Property name (link to Pascal API docs)|Default value|Allowed values

|cgeref:TMFNode[MFNode]
|[in,out]
|cgeref:TSkinNode.SetJoints[joints]
|[] (empty list)
|list of cgeref:TTransformNode[Transform] or cgeref:THAnimJointNode[HAnimJoint] nodes

|cgeref:TMFMatrix4f[MFMatrix4f]
|[in,out]
|cgeref:TSkinNode.SetInverseBindMatrices[inverseBindMatrices]
|[] (empty list)
|

|cgeref:TMFNode[MFNode]
|[in,out]
|cgeref:TSkinNode.SetShapes[shapes]
|[] (empty list)
|list of cgeref:TAbstractShapeNode[] nodes

|cgeref:TSFNode[SFNode]
|[in,out]
|cgeref:TSkinNode.Skeleton[skeleton]
|`NULL` (Pascal `nil`)
|cgeref:TAbstractGroupingNode[X3DGroupingNode]
|===
////

* cgeref:TSkinNode.SetJoints[joints]
+
When the joints are transformed (moved, rotated, scaled),  the skin (meshes listed in `Skin.shapes`) is updated accordingly.
+
_Allowed values:_ list of cgeref:TTransformNode[Transform] or cgeref:THAnimJointNode[HAnimJoint] nodes.

* cgeref:TSkinNode.SetInverseBindMatrices[inverseBindMatrices]
+
For each joint, an "inverse bind matrix" may be specified, which transforms the mesh into the local space of the joint.
+
_Allowed values:_ list of 4x4 matrices.

* cgeref:TSkinNode.SetShapes[shapes]
+
Shapes whose geometries are affected by the skinned animation, that is: their vertexes move to follow the joints that are associated with them.
+
_Allowed values:_ list of cgeref:TAbstractShapeNode[] nodes.

* cgeref:TSkinNode.Skeleton[skeleton]
+
Common root of the joints hierarchy.
+
_Allowed values:_ one cgeref:TAbstractGroupingNode[X3DGroupingNode] node.

This node defines how a set of joints influence a mesh, thus enabling skinned animation in a way that is simple, efficient and perfectly aligned with link:gltf[glTF]. This node directly corresponds to a single https://registry.khronos.org/glTF/specs/2.0/glTF-2.0.html#skins[glTF skin] in a glTF file.

The idea is that you have:

- A _skeleton_, which is a hierarchy of _joints (aka "bones")_, expressed in X3D as a hierarchy of cgeref:TTransformNode[] (or any other X3D node that defines transformation, like cgeref:THAnimJointNode[]).

- A number of meshes, which are a number of X3D geometry nodes using cgeref:TAbstractCoordinateNode[] with per-vertex data in `skinWeights0` and `skinJoints0`.

Each vertex of a mesh is affected by a small subset of _joints_. For each joint on each vertex,  a _weight_ determines how much each joint affects this vertex. You can change joints (change their transformations, e.g. by regular X3D animation using cgeref:TTimeSensorNode[] and interpolators, or just directly access the joint node from Pascal and change its translation / rotation / scale) and in effect the mesh should change.

The `Skin` node defines the connection between the joints and the skin.

Placement of the `Skin` node within the X3D nodes graph matters. `Skin` is a descendant of the `X3DChildNode` (cgeref:TAbstractChildNode[]) in Pascal) node. Placing it under a specific parent transformation makes the resulting joints (`Skin.joints`) and skin (`Skin.shapes`) be part of the X3D transformation hierarchy, so they are rendered with the designated transformation.

// TODO: update to link to master

NOTE: If you're familiar with the X3D specification syntax for node definitions, you can find link:https://github.com/castle-engine/castle-engine/blob/14632f88b87d7b9b5152d61e366e5140c56236aa/tools/internal/x3d-nodes-to-pascal/nodes-specification/CastleEngineExtensions.txt#L257[`Skin` node spec expressed in this way here].

NOTE: The `Skin` node itself is not an "animation" that you can play. The actual animations are defined by using `TimeSensor` nodes that cause interpolators to transform (move, rotate, scale) the joint nodes. Animating joints will in turn transform the meshes as indicated by this `Skin` node.

NOTE: There are some differences, but there are also some similarities between `Skin` node and `HAnimHumanoid` node. They both can be used for skinned animation. If you're familiar with H-Anim and `HAnimHumanoid` node, we outline the differences (and similarities) in the section below.

== Additional per-vertex information at geometry nodes

We also enhance geometry nodes with the necessary per-vertex information for each vertex (which joints affect it, how much):

- `skinJoints0` (from Pascal: cgeref:TAbstractComposedGeometryNode.SetSkinJoints0[]): for each vertex, 4 most important joints that affect it.
- `skinWeights0` (from Pascal: cgeref:TAbstractComposedGeometryNode.SetSkinWeights0[]): for each vertex, how much is it affected by the 4 most important joints.

This information is a per-vertex data useful for animation systems, whether implemented by CPU or GPU.

NOTE: In the future we may introduce fields like `skinJoints1`, `skinWeights1` etc. to support more than 4 joints per vertex. This is consistent with glTF data `JOINTS_0`, `WEIGHTS_0`, `JOINTS_1`, `WEIGHTS_1` etc.

[#example]
== X3D Example

Example `Skin` definition is as follows:

NOTE: Fully working version of this example is https://github.com/castle-engine/demo-models/blob/master/animation/skinned_animation.x3dv[in demo-models, animation/skinned_animation.x3dv]. You can open it using any engine tool, e.g. link:castle-model-viewer[Castle Model Viewer].

////
Note: Using PHP highlighter for X3D classic content.
Not precisely correct, but close -- # is comment, colors things before { } reasonably.
See supported languages by CodeRay:
https://docs.asciidoctor.org/asciidoctor/latest/syntax-highlighting/coderay/
////

[source,php]
----
Skin {
    # Shapes that are affected by the skinned animation.
    # They are displayed as part of displaying the Skin node.
    # Note that this list can only contain Shape nodes, not more
    # complicated compositions like transformations of them.
    shapes [
        DEF SkinnedMeshShape1 Shape {
            geometry IndexedFaceSet {
                ...
                skinWeights0 ...
                skinJoints0  ...
            }
        }
        DEF SkinnedMeshShape2 Shape {
            geometry IndexedFaceSet {
                ...
                skinWeights0 ...
                skinJoints0  ...
            }
        }
    ]

    # Joints hierarchy, starting from root node.
    #
    # Note: The hierarchy below is *just a trivial example*,
    # not a proper example of how joints for a typical humanoid
    # look like. Follow H-Anim conventions for joints to define
    # typical humanoid joints hierarchy.
    #
    # Any X3D node graph is allowed here, in particular you can
    # also specify Shape nodes within the transformations here.
    # Such Shape nodes will be just rigid 3D shapes attached to
    # the joints (like a sword may be attached to the avatar's hand).
    #
    # The Shape(s) that should be affected by the skin mechanism
    # (modified by joints) should *not* be listed here, they should
    # be placed only in the Skin.shapes field.
    skeleton DEF RootJoint Transform {
        children [
            DEF Body Transform {
                children [
                    DEF LeftThigh Transform {
                        children [
                            DEF LeftShin Transform {
                                children [
                                    DEF LeftFoot Transform {
                                    }
                                ]
                            }
                        ]
                    }
                    DEF RightThigh Transform {
                        children [
                            DEF RightShin Transform {
                                children [
                                    DEF RightFoot Transform {
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    }

    # List of joints.
    # This is a list of all the joints that control the shapes
    # in the "shapes" list.
    #
    # If a human writes the X3D content,
    # we suggest placing all the joints in "skeleton" hierarchy,
    # with names (DEF). Then in the "joints" field just list them with "USE".
    # This is easier to read and write for humans.
    #
    # That said, following X3D, it is possible to write using any order,
    # as long as each USE is after a corresponding DEF.
    # So you can also start by writing
    # joints in "joints" list, only interlinking to previous joints by USE,
    # and then define the "skeleton" hierarchy by a USE to the root joint.
    # For software, this is equally easy to write and later read, so not a problem.
    # For humans, it will likely look more complicated.
    #
    # The order of joints here matters:
    # joint indexes specified in the skinJoints0 array
    # refer to the position of joint on this list.
    #
    # All the joints listed here must also be part of the "skeleton"
    # hierarchy.
    # However, not *all* transformation nodes (Transform) from the "skeleton"
    # need to be listed here and considered "joints". You only need to list
    # the joints that actually affect some vertex in some some shape,
    # to be able to refer to this joint from skinJoints0 array.
    # If a joint is merely a parent for other joints but doesn't *directly*
    # influence any vertex, there's no need to list it here.
    joints [
        USE RootJoint
        USE Body
        USE LeftThigh
        USE LeftShin
        USE LeftFoot
        USE RightThigh
        USE RightShin
        USE RightFoot
    ]
}
----

== Pascal example (modify joints by code)

Build and run example https://github.com/castle-engine/castle-engine/tree/skinned-animation-gpu/examples/animations/animate_bones_by_code[examples/animations/animate_bones_by_code] to see how to modify joints by Pascal code.

== More testcases

Simply open any glTF file with a skinned animation in link:castle-model-viewer[Castle Model Viewer] and save it back to X3D. This is a way to have lots of testcases of the `Skin` node.

Links to many resources with animated glTF models are in link:assets.php[our assets page], e.g. look at https://quaternius.com/[Quaternius] and https://sketchfab.com/features/gltf[Sketchfab glTF models].

// See some ready glTF and X3D models in (TODO link, commit x3d versions) demo-models/animation/gltf_skinned_animation/ .

== Why?

Our approach is deliberately closely aligned with glTF.

- Transforming glTF skinned animation information into X3D `Skin` is straightforward.

- Implementing `Skin`, including implementing it on GPU (skin is applied in shaders) is as straightforward as in glTF. You can follow the https://www.khronos.org/files/gltf20-reference-guide.pdf[glTF cheat sheet] that describes the GPU implementation on skinning in 1.5 pages.

=== Difference from H-Anim

==== Different scope

First of all, the scope of this node is different (and deliberately much smaller) than what H-Anim spec offers.

- H-Anim defines various ways to animate humanoids and various conventions how to design humanoid joints (with different levels of articulation).

- We consider, in this node, only a single animation technique: skinned animation.

In this node, we're keeping it agnostic from whether you apply it to humanoids or non-humanoids, like non-humanoid animals or imaginary alien creatures, plants, rubbery machines etc.

If you wonder how to define joints for a humanoid, just follow _H-Anim_ LOA conventions for joints naming and organization. In fact you can use `HAnimJoint` nodes within our `Skin` node, we deliberately made it possible (though you can also just use `Transform` nodes instead of `HAnimJoint` nodes).

The `Skin` node simply provides an alternative way to specify skinned animation. We don't try to fill other use-cases of H-Anim.

==== Different way to specify skinned animation

X3D standard is integrated with the H-Anim standard, and thus already has a way to perform skinned animation. So why do we invent an alternative way?

- Because we think that skinned animation can be expressed a bit simpler than the H-Anim does. We think glTF approach is a good way to do it.
+
H-Anim is a big specification, that is linked from another big specification (X3D). We rather like the simplicity and efficiency of how skinned animation is defined in glTF. The way skinned animation is expressed in https://registry.khronos.org/glTF/specs/2.0/glTF-2.0.html[glTF spec] and in https://www.khronos.org/files/gltf20-reference-guide.pdf[glTF cheatsheet] seems simpler to us.

- One reason for this simplicity is that our approach doesn't invent any new concept for "joints". Our joints are just `Transform` nodes. We don't need `HAnimJoint`.

- We add extra flexibility, just like glTF has: you are not limited to one `skinCoord` in one `HAnimHumanoid`. One `Skin` node can influence multiple geometries with completely different `Coordinate` nodes, thanks to having `skinWeights0` and `skinJoints0` on each geometry node.

- Our `Skin`, just like glTF approach, has an obvious, simple and efficient GPU implementation. The https://www.khronos.org/files/gltf20-reference-guide.pdf[glTF cheat sheet] describes it in 1.5 pages and it's really easy to implement.

- Our design also makes some assumptions, that map to practical usage in our experience, and enable efficient GPU implementation.
+
Namely, per-vertex weights and joints are stored as 4D vectors, so each vertex has a list of "4 most important joints". See cgeref:TAbstractComposedGeometryNode.SetSkinWeights0[] and cgeref:TAbstractComposedGeometryNode.SetSkinJoints0[] for details. This is enough in practice, in our experience, for even quite complicated gamedev models.
+
In the future we will likely allow more than 4 joints per vertex, by introducing `skinWeights1` and `skinJoints1` (and maybe more). Some limit on the number of joints-influencing-a-vertex will in practice always be present, to enable efficient GPU skinning of meshes. That's also the reason why current fields `skinWeights0` and `skinJoints0` have `0` at the end.
+
We realize that this design "uncovers" an implementation detail (it's efficient to process things, on GPU and CPU, as 4D vectors). But at the same time, experience shows that it's not troublesome, and even with a limit of "4 joints per vertex", it's enough in practice for lots of 3D animations to look good.

- We want naming that clearly says it's an animation technique that works with any mesh, humanoid or not.
+
_H-Anim_ naming implies it's for humanoids, parts of H-Anim spec talk about joints for humanoids -- this makes using `HAnimHumanoid` for animating arbitrary meshes confusing for the developers. For this reason, we also wanted to have joints/bones simply defined as `Transform` nodes. This again echoes our desire to keep it simple (standard X3D `Transform` is a joint), and also we don't want to connect this to "humanoids", even by terminology. Skinned animation is a standard 2D and 3D animation technique, not specific to humanoids at all.

==== Similarities to H-Anim (HAnimHumanoid)

There are numerous deliberate similarities between `Skin` and `HAnimHumanoid`.

- They both have `joints` list and `skeleton` fields, with practically the same purpose and meaning.

- In both of them, you can use `HAnimJoint` nodes for joints. In `Skin`, you can also use simple `Transform` nodes for joints, but if you want to support both systems in a piece of code generating X3D, you can just use `HAnimJoint` nodes and later decide do you put them in `Skin` or `HAnimHumanoid`.

- The placement of `Skin` and `HAnimHumanoid` in the transformation matters. This is actually something where we follow X3D and _not_ glTF. It makes sense, for humans and for software, that `Skin` is part of the transformation hierarchy.

== How do collisions work when skin is calculated on GPU

=== You need to assign proper bounding box explicitly

You *absolutely must* explicitly specify the shape's bounding box if that shape is to be animated using the skinned animation.

- In X3D files, set the `bboxCenter` and `bboxSize` fields of the `Shape` node.

- In Pascal code, the equivalent is to set cgeref:TAbstractShapeNode.BBox[].

The bounding box must account for all possible skin arrangements in all possible animation frames.

Reasoning:

Since the mesh (skin) is only updated on GPU, the algorithms on CPU don't know about the updated skin vertexes. For example, imagine a 3D model of a very thin and very tall https://en.wikipedia.org/wiki/Giraffe[giraffe] that https://en.wikipedia.org/wiki/File:Flickr_-\_Rainbirder_-_Reticulated_Giraffe_drinking.jpg[lays down its long neck in one animation]. The effective bounding box of the giraffe in that animation is very different from the initial bounding box when the giraffe was standing. But since the animation is performed on GPU using shaders, the CPU doesn't know about the modified skin position and bounding box!

And it is bad if our algorithms on CPU think that the object is in one place, but it's actually in another place. For example, _frustum culling_ and _ray picking_ would consider a wrong box, and e.g. _frustum culling_ may decide to _cull_ (not render) the object when it should actually be visible.

The solution is to manually define the bounding box for the shapes affected by skin. You *have to* specify this bounding box.

NOTE: There are ways to transfer data calculated on GPU back to CPU. Like transform feedback, https://github.com/castle-engine/castle-engine/tree/master/examples/shaders/transform_feedback[we have a demo using it in our engine]. But we don't want to use it here -- it would complicate the implementation, and also would cost some efficiency.

NOTE: As an alternative to setting proper bounding box using cgeref:TAbstractShapeNode.BBox[] (which we recommend) you could also turn off frustum culling by changing both cgeref:TCastleScene.SceneFrustumCulling[] and cgeref:TCastleScene.ShapeFrustumCulling[] to `false`. But we really don't recommend this: it may cost significant performance, unless you only have a few such scenes in your world.

[NOTE]
====
This requirement is not specific to our engine. See e.g.

- https://threejs.org/[Three.js] also has this issue, see https://discourse.threejs.org/t/how-to-get-accurate-bounding-boxes-for-animated-gltf/14669[here] and https://discourse.threejs.org/t/object-bounds-not-updated-with-animation/3749[here]. Calculating a bounding box for skinned-animated objects (whether it is done on CPU or GPU), or updating any mesh collider, is a cost that you usually want to avoid. _Three.js_ advises just like us: specify a bounding box manually.

- https://github.com/bevyengine/bevy/issues/4971[Bevy issue], still open because there's no perfect solution -- calculating boxes (during loading or at run-time) always has a cost and we just don't have this information in glTF file.
====

=== Collisions that detect which body part is hit, using physics

To detect collisions using link:physics[], and detect e.g. whether a ray hits the head or leg of a skinned humanoid, you should attach physical colliders to joints. To do this:

- Expose a subset of joints using cgeref:TCastleSceneCore.ExposeTransforms[]. See link:expose[Expose scene elements] documentation. This will give you a hierarchy of cgeref:TCastleTransform[] that you can attach colliders to.

- Attach colliders, like simple cgeref:TCastleBoxCollider[] to the exposed transformations.

- Make sure to mark that the collider is going to move because of animation (and should not move because of physics forces like gravity):
+
--
- Set cgeref:TCastleRigidBody.Dynamic[] to `false`
- Set cgeref:TCastleRigidBody.Animated[] to `true`
--

== TODO

- Prepare a demo showing the _"Collisions that detect which body part is hit, using physics"_ to detect headshots on an animated model.
+
Also, switch to https://en.wikipedia.org/wiki/Ragdoll_physics[ragdoll physics] (by adding constraints, and toggling cgeref:TCastleRigidBody.Dynamic[]) when character is killed.

- Right now, https://castle-engine.io/shadow_volumes[shadow volumes] disable calculating skinned animation on GPU.
+
--
- This is necessary. To calculate efficient shadow quads, we need to calculate "silhouette edges", which in turn means we need to know all vertexes final coordinates (after skin) on CPU. And we need to have proper shadow quads in every frame (corresponding to the shadow caster rendering in this frame, not some old version), as shadow volumes assume that real mesh provides a "cap" to the geometry rendered into the stencil.
- So for now, when a shape is a shadow caster for shadow volumes, it doesn't use skinned animation on GPU. Skinned animation is done on CPU.
- Yes, in effect this makes https://castle-engine.io/shadow_volumes[shadow volumes] affect performance in a negative way -- not only the shadow volumes itself cost (rendering shadow quads, 2 passes), but also it disables skinned animation on GPU.
- To overcome this we'd need to use geometry shaders to render shadow quads, which is quite some additional work.
- Practical solution will likely be to recommend https://castle-engine.io/x3d_extensions_shadow_maps.php[shadow maps] once we https://castle-engine.io/roadmap#shadow_maps[improve them].
--