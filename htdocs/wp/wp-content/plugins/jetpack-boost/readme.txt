=== Jetpack Boost - Website Speed, Performance and Critical CSS  ===
Contributors: automattic, xwp, adnan007, b<PERSON><PERSON>, da<PERSON><PERSON><PERSON><PERSON>, da<PERSON><PERSON><PERSON>, dilirity, donncha, ebin<PERSON>n, exelero, jeherve, j<PERSON><PERSON><PERSON><PERSON>, karth<PERSON><PERSON><PERSON><PERSON>, kraftbj, l<PERSON><PERSON>, luch<PERSON><PERSON><PERSON>, pyr<PERSON>ur, r<PERSON><PERSON><PERSON><PERSON>, sc<PERSON><PERSON>, thingalon
Donate link: https://automattic.com
Tags: performance, speed, web vitals, critical css, cache
Requires at least: 6.7
Tested up to: 6.8
Requires PHP: 7.2
Stable tag: 4.2.1
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Speed up your WordPress site with one-click optimizations like Page Cache, Critical CSS, and Image CDN to improve Core Web Vitals.

== Description ==

Speed up your WordPress site by optimizing page performance with Jetpack Boost. Easily activate one-click optimizations to boost your Core Web Vitals.

Did you know that a faster website:-

- Ranks higher on Google.
- Improves bounce rate (people stay on your site for longer).
- Increases your conversion rate.

Increase your website performance and speed up your website with one-click optimizations that supercharge your WordPress site’s performance and improve core web vitals scores for better search engine listings.

Improving Core Web Vitals helps you rank higher on Google. A faster website also improves your SEO, helps you reduce your bounce rate and increase your ecommerce conversion rate.

- Largest Contentful Paint (LCP): Measures loading performance. Improve your LCP and improve your website loading speed.
- First Input Delay (FID): Measures interactivity. To improve user experience pages should have a low FID.
- Cumulative Layout Shift (CLS): Measures visual stability. Lowering your CLS helps improve your user experience.

### Performance Modules

Optimize your website with the same techniques used on the world's most successful websites.

Each technique that is used to increase website performance is packaged up as a module that you can activate and try out.

Currently, the plugin has 6 performance modules available:

1. *Optimize CSS Loading* generates Critical CSS for your homepage, posts and pages. This can allow your content to show up on the screen much faster, particularly for viewers using mobile devices.

   Read more about critical CSS generation at [web.dev](https://jetpack.com/redirect/?source=jetpack-boost-critical-css)

2. *Page Cache* speeds up your site by saving pages as static files. These files are quickly served to visitors, reducing load times and enhancing user experience.

3. *Defer Non-Essential JavaScript* moves some tasks to after the page loads, so that important visual information can be seen sooner and your website loads quicker.

   Read more about deferring JavaScript at [web.dev](https://jetpack.com/redirect/?source=jetpack-boost-defer-js)

4. *Image Guide* is a must-have feature for anyone who wants to optimize the images on their website. With this guide, you can ensure that the images on your site are the right size and dimensions, which is critical for improving user experience, page speed, and site ranking. Following the tips and best practices outlined in the guide, you can reduce image file sizes and speed up your site. Check out our [support page](https://jetpack.com/support/jetpack-boost/image-performance-guide/) to learn more about this feature and how it can help you achieve a faster and smoother website experience for your users.

5. *Image CDN* allows your site to serve automatically-resized images in modern web formats directly from Jetpack's worldwide Content Delivery Network.

   Read more about Image CDNs at [web.dev](https://web.dev/image-cdns/)

6. *Concatenate and Minify CSS and JS* combines and shrinks your JavaScript and CSS resources to reduce the number and size of requests to your server, ensuring your content loads faster.

   Read more about minifying files at [web.dev](https://web.dev/minify-css/)

Don’t want to have to manually generate your critical CSS each time you update your site? Let us do the heavy lifting for you with automated critical CSS – each time you update your site we will automatically regenerate your critical CSS and update your performance scores. Upgrading also gives you dedicated email support access.

### With 💚 by Jetpack

This is just the start!

We are working hard to bring more features and improvements to Jetpack Boost. Let us know your thoughts and ideas!

We'd also like to give a special THANK YOU to the XWP team who provided help with initial research and scoping of the plugin and were engaged with our team throughout the project.

== Frequently Asked Questions ==

= What does Jetpack Boost do to help speed up my WordPress site? =

Jetpack Boost makes small changes to the way that data is sent from your WordPress site to your users’ browser, to enable the browser to display your site faster.

Jetpack Boost includes a growing number of separate features which can be turned on individually to improve your site’s performance. These include:

* **Optimize CSS Loading**: This feature determines the most important CSS that your site needs to display your site’s initial content as quickly as possible, and embeds it directly into your site header.
* **Page Cache**: This feature stores your website's pages as static HTML files, bypassing the need for dynamic generation. This means visitors receive pages faster, reducing wait times and improving overall site performance.
* **Defer Non-Essential JavaScript**: This feature forces all of the JavaScript which is not deemed essential to displaying your site to load after your site’s main content has been loaded.
* **Image CDN**: This feature automatically resizes images to a more appropriate size for your visitors' screens, converts them to modern image formats, and serves them from Jetpack's worldwide network of servers.
* **Concatenate JS**: This feature reduces the size of your JavaScript resources, and automatically combines them into fewer files, allowing your site to load faster with fewer requests.
* **Concatenate CSS**: As with concatenating JavaScript, this feature shrinks your CSS files and allows them to load with fewer requests.

= What speed improvements can I expect when using Jetpack Boost? =

Website Performance is complicated and can be affected by a number of factors. As a result, it is difficult to accurately predict how much impact it will have on each site.

Generally, the lower your speed score is to begin with, the more Jetpack Boost may impact your performance. We have seen user reports of up to 25 Speed Score points improvement simply by installing and using Jetpack Boost.

However, as performance can be impacted by so many factors, it is also possible for Jetpack Boost to have a small negative performance impact in some rare cases.

We recommend that you install Jetpack Boost, and try it for yourself. It includes a tool for measuring your Speed Score, to check what impact it has on your site.

= Can I also defer non-essential CSS with Jetpack Boost? =

Jetpack Boost automatically defers non-essential CSS if its “Optimize CSS Loading” feature is enabled.

The “Optimize CSS Loading” feature identifies the most important CSS rules your site needs to display your pages as quickly as possible (commonly called “Critical CSS”), and defers all other CSS rules from loading until your main content has loaded.

= What are Web Vitals? =

Web Vitals are the measurements that Google uses to better understand the user experience on a website. By improving Web Vitals scores you're also improving the user experience on your site.

You can read more about Web Vitals on [web.dev](https://jetpack.com/redirect/?source=jetpack-boost-vitals)

= How does Jetpack Boost plugin improve Core Web Vitals? =

Each Core Web Vital relates to an aspect of how quickly your site can load and appear on new visitors’ screens.

Jetpack Boost makes small changes to the way that data is sent from your WordPress site to your users’ browsers, to enable your content to load faster. As a result, it can improve your Core Web Vitals scores.

For example, our “Optimize CSS Loading” feature ensures the most important CSS rules are sent to users’ browsers as early as possible, improving both First Contentful Paint (FCP) and Cumulative Layout Shift (CLS) scores.

= Does this plugin require Jetpack? =

Jetpack Boost is a part of the Jetpack brand, but it doesn’t require Jetpack plugin to run. This is a separate plugin from Jetpack and it will always remain that way.

= Will this plugin be able to improve performance on any website? =

This plugin includes a range of performance improvements, which can help almost any WordPress site perform better.

However, if your site is already extremely well optimized, Jetpack Boost may not have much room to improve it.

Jetpack Boost includes a tool for measuring your site’s Speed Score - we encourage users to try it out and see what impact it can have for them.

= How do I know if it's working? =

Every site is different and so performance benefits for each module may vary from site to site. That's why we recommend that you measure the performance improvements on your site by enabling the performance modules one by one. There are many tools out there that you can use for free to measure performance improvements:

* [WebPageTest.org](https://jetpack.com/redirect/?source=jetpack-boost-webpagetest)
* [web.dev/measure](https://jetpack.com/redirect/?source=jetpack-boost-measure)
* [PageSpeed Insights](https://jetpack.com/redirect/?source=jetpack-boost-pagespeed)
* [GTMetrix](https://jetpack.com/redirect/?source=jetpack-boost-gtmetrix)

Google PageSpeed measurements are built-in the Jetpack Boost dashboard.

= Is Speed Optimization with Jetpack Boost safe? =

Yes, it’s safe to try Jetpack Boost on any WordPress site.

Jetpack Boost does not alter your site’s content, it only modifies the way the content is sent to the user’s browser to allow it to display faster.

As a result, all of Jetpack Boost’s features can safely be turned off in the event of an incompatibility with your other plugins.

= How does Jetpack Boost compare with other speed optimization plugins? =

Speed Optimization plugins for WordPress can be complicated and opaque for users. They often offer columns of checkboxes with little explanation, and don’t include tools to measure the impact of each change or choice users make.

Jetpack Boost aims to be as easy to use as possible, and includes a Speed Score indicator to help users immediately measure the impact of their choices.

= Does it work with static page cache? =

Absolutely! If you have plugins like WP Super Cache or W3 Total Cache installed - Jetpack Boost is only going to help increase the performance benefits! Keep in mind that you need to wait for the cache to clear for Jetpack Boost improvements to show up.

= Can Jetpack Boost make my website load faster if I have a large database? =

Jetpack Boost does not include any optimizations that target large databases at this time. However, watch this space - we are always looking for new ways to help our users perform better.

= Does Jetpack Boost help with image optimization? =

Jetpack Boost's Image CDN feature automatically converts your images to more modern web formats, resulting in smaller image file sizes without a loss in quality.

= Is Jetpack Boost compatible with other caching and speed optimization plugins? =

With few exceptions, Jetpack Boost has no problems running alongside most caching and speed optimization plugins. As a guideline, we don’t recommend enabling the same feature in multiple optimization plugins.

For example, if two plugins attempt to defer your non-essential JavaScripts, then they may end up conflicting with each other and cause display problems on your site.

If you run into compatibility issues, please do let us know. You can drop us a line on [the Jetpack Boost Support Forums](https://wordpress.org/support/plugin/jetpack-boost/) at any time.



== Installation ==

1. Install Jetpack Boost via the plugin directory, and activate it.
2. Activate Jetpack Connection
3. Turn on performance modules one by one and observe how the performance score changes

== Screenshots ==

1. Jetpack Boost Critical CSS Generation
2. Jetpack Boost Speed Improvement

== Changelog ==
### 4.2.1 - 2025-07-24
#### Added
- Critical CSS: Exclude post types of popular builder plugins from generation.
- General: Add WP filter (jetpack_boost_can_module_run) to allow more control over which modules can run their functionality.
- My Jetpack: Added analytics for empty product search results.

#### Changed
- Cornerstone Pages: Ensure Home URL is always a predefined Cornerstone Page
- E2E tests: remove redundant logic in test fixture and converted the fixture to Typscript
- Improves performance of wpcom comments liking by caching and minimizing API requests.
- My Jetpack: Enabled access to My Jetpack on WP Multisite.
- Update package dependencies.

#### Deprecated
- Image Size Analysis: Hide UI by default, pending future removal of feature. Allow UI to be shown via a temporary filter.

#### Fixed
- General: Fix minor incompatibility with certain Boost labels and Gutenberg 21.2
- Update JITMs to remove jQuery dependency

Note: There was not a public 4.2.0 release.
--------

[See the previous changelogs here](https://github.com/Automattic/jetpack/blob/trunk/projects/plugins/boost/CHANGELOG.md#changelog)
