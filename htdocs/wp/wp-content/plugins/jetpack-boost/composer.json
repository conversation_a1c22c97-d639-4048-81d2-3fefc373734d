{"name": "automattic/jetpack-boost", "description": "Boost your WordPress site's performance, from the creators of Jetpack", "type": "library", "license": "GPL-2.0-or-later", "version": "4.2.1", "authors": [{"name": "Automattic, Inc.", "email": "<EMAIL>"}], "minimum-stability": "dev", "prefer-stable": true, "require": {"ext-json": "*", "automattic/jetpack-admin-ui": "^0.5.10", "automattic/jetpack-assets": "^4.3.0", "automattic/jetpack-autoloader": "^5.0.8", "automattic/jetpack-boost-core": "^0.3.11", "automattic/jetpack-boost-speed-score": "^0.4.10", "automattic/jetpack-composer-plugin": "^4.0.5", "automattic/jetpack-config": "^3.1.1", "automattic/jetpack-connection": "^6.16.0", "automattic/jetpack-device-detection": "^3.0.9", "automattic/jetpack-image-cdn": "^0.7.14", "automattic/jetpack-my-jetpack": "^5.20.1", "automattic/jetpack-plugin-deactivation": "^0.3.14", "automattic/jetpack-schema": "^0.2.5", "automattic/jetpack-status": "^6.0.0", "automattic/jetpack-sync": "^4.15.2", "automattic/jetpack-wp-js-data-sync": "^0.6.6", "matthiasmullie/minify": "^1.3"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^4.0.0"}, "scripts": {"phpunit": ["phpunit-select-config phpunit.#.xml.dist --bootstrap tests/bootstrap.php --testsuite unit --colors=always", "phpunit-select-config phpunit.#.xml.dist --bootstrap tests/bootstrap-wordbless.php --testsuite with-wordpress --colors=always"], "test-coverage": "pnpm concurrently --names php,js 'php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --bootstrap tests/bootstrap.php --testsuite unit --coverage-php \"$COVERAGE_DIR/php-unit.cov\" && php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --bootstrap tests/bootstrap-wordbless.php --testsuite with-wordpress --coverage-php \"$COVERAGE_DIR/php-critical.cov\"' 'pnpm:test-coverage'", "test-js": ["pnpm run test"], "test-php": ["@composer phpunit"], "build-development": ["pnpm run build"], "build-production": ["pnpm run build-production-concurrently"], "watch": ["Composer\\Config::disableProcessTimeout", "pnpm run watch"]}, "autoload-dev": {"psr-4": {"Automattic\\Jetpack_Boost\\Tests\\": "./tests/php"}}, "autoload": {"classmap": ["app/"]}, "config": {"sort-packages": true, "platform": {"ext-intl": "0.0.0"}, "autoloader-suffix": "b1e77e6231d50e7663f84529b6a3dfda_jetpack_boostⓥ4_2_1", "allow-plugins": {"automattic/jetpack-autoloader": true, "automattic/jetpack-composer-plugin": true}}, "repositories": [], "extra": {"mirror-repo": "Automattic/jetpack-boost-production", "autorelease": true, "autotagger": {"v": false}, "release-branch-prefix": "boost", "version-constants": {"JETPACK_BOOST_VERSION": "jetpack-boost.php"}, "wp-plugin-slug": "jetpack-boost", "wp-svn-autopublish": true, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-boost-production/compare/${old}...${new}"}, "non-mirrored-require-dev": ["automattic/jetpack-test-environment"]}}