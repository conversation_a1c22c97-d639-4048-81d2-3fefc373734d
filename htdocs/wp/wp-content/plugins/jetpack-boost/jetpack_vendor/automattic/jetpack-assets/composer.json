{"name": "automattic/jetpack-assets", "description": "Asset management utilities for Jetpack ecosystem packages", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"php": ">=7.2", "automattic/jetpack-constants": "^3.0.8", "automattic/jetpack-status": "^6.0.0"}, "require-dev": {"brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-changelogger": "^6.0.5", "wikimedia/testing-access-wrapper": "^1.0 || ^2.0 || ^3.0", "automattic/phpunit-select-config": "^1.0.3"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"files": ["actions.php"], "classmap": ["src/"]}, "scripts": {"build-development": ["pnpm run build"], "build-production": ["pnpm run build-production"], "phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": "pnpm concurrently --names php,js 'php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\"' 'pnpm:test-coverage'", "test-js": ["pnpm run test"], "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-assets", "textdomain": "jetpack-assets", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-assets/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "4.3.x-dev"}}}