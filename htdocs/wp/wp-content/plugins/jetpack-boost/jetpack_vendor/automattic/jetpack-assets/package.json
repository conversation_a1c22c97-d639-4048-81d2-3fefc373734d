{"private": true, "browserslist": "extends @wordpress/browserslist-config", "scripts": {"build": "pnpm run clean && pnpm run build-js", "build-js": "webpack", "build-production": "pnpm run clean && pnpm run build-production-js && pnpm run validate", "build-production-js": "NODE_ENV=production BABEL_ENV=production pnpm run build-js", "clean": "rm -rf build", "test": "jest --config=tests/jest.config.cjs", "test-coverage": "pnpm run test --coverage", "validate": "pnpm exec validate-es build/"}, "dependencies": {"@automattic/jetpack-script-data": "^0.5.0", "react": "18.3.1"}, "devDependencies": {"@automattic/jetpack-webpack-config": "workspace:*", "@wordpress/browserslist-config": "6.26.0", "concurrently": "7.6.0", "jest": "30.0.4", "md5-es": "1.8.2", "webpack": "5.94.0", "webpack-cli": "6.0.1"}}