# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.5.6] - 2025-07-21
### Changed
- Internal updates.

## [0.5.5] - 2025-04-28
### Changed
- Internal updates.

## [0.5.4] - 2025-03-21
### Changed
- Internal updates.

## [0.5.3] - 2025-03-12
### Changed
- Internal updates.

## [0.5.2] - 2025-03-05
### Changed
- Internal updates.

## [0.5.1] - 2025-02-24
### Changed
- Update dependencies.

## [0.5.0] - 2024-11-14
### Removed
- General: Update minimum PHP version to 7.2. [#40147]

## [0.4.4] - 2024-11-04
### Added
- Enable test coverage. [#39961]

## [0.4.3] - 2024-09-05
### Changed
- Update dependencies.

## [0.4.2] - 2024-08-26
### Changed
- Updated package dependencies. [#39004]

## [0.4.1] - 2024-08-15
### Fixed
- Fix incorrect next-version tokens in php `@since` and/or `@deprecated` docs. [#38869]

## [0.4.0] - 2024-05-20
### Deprecated
- Extract the 'is_current_request_activating_plugin_from_plugins_screen' method into Status package. [#37430]

## [0.3.5] - 2024-05-06
### Changed
- Internal updates.

## [0.3.4] - 2024-04-08
### Changed
- Internal updates.

## [0.3.3] - 2024-03-25
### Changed
- Internal updates.

## [0.3.2] - 2024-03-14
### Changed
- Internal updates.

## [0.3.1] - 2024-01-02
### Fixed
- Ensured that language packs are installed after installing a new plugin. [#34763]

## [0.3.0] - 2023-11-20
### Changed
- Updated required PHP version to >= 7.0. [#34192]

## [0.2.6] - 2023-11-14

## [0.2.5] - 2023-08-23
### Changed
- Updated package dependencies. [#32605]

## [0.2.4] - 2023-04-10
### Added
- Add Jetpack Autoloader package suggestion. [#29988]

## [0.2.3] - 2023-02-20
### Changed
- Minor internal updates.

## [0.2.2] - 2022-12-02
### Changed
- Updated package dependencies. [#27688]

## [0.2.1] - 2022-11-22
### Changed
- Updated package dependencies. [#27043]

## [0.2.0] - 2022-08-23
### Added
- Add method to check plugin activation context. [#25422]

## [0.1.4] - 2022-07-26
### Changed
- Updated package dependencies. [#25158]

## [0.1.3] - 2022-06-21
### Changed
- Renaming master to trunk.

## [0.1.2] - 2022-04-26
### Changed
- Updated package dependencies.

## [0.1.1] - 2022-03-29
### Added
- Set composer package type to "jetpack-library" so i18n will work.

## 0.1.0 - 2022-02-02
### Added
- First version
- New functions to safely check plugin statuses

### Changed
- Build: add missing mirror repo details, so package can be deployed.

### Fixed
- Fix method logic

[0.5.6]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.5.5...v0.5.6
[0.5.5]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.5.4...v0.5.5
[0.5.4]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.5.3...v0.5.4
[0.5.3]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.5.2...v0.5.3
[0.5.2]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.5.1...v0.5.2
[0.5.1]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.5.0...v0.5.1
[0.5.0]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.4.4...v0.5.0
[0.4.4]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.4.3...v0.4.4
[0.4.3]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.4.2...v0.4.3
[0.4.2]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.4.1...v0.4.2
[0.4.1]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.4.0...v0.4.1
[0.4.0]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.3.5...v0.4.0
[0.3.5]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.3.4...v0.3.5
[0.3.4]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.3.3...v0.3.4
[0.3.3]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.3.2...v0.3.3
[0.3.2]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.3.1...v0.3.2
[0.3.1]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.3.0...v0.3.1
[0.3.0]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.2.6...v0.3.0
[0.2.6]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.2.5...v0.2.6
[0.2.5]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.2.4...v0.2.5
[0.2.4]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.2.3...v0.2.4
[0.2.3]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.2.2...v0.2.3
[0.2.2]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.2.1...v0.2.2
[0.2.1]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.2.0...v0.2.1
[0.2.0]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.1.4...v0.2.0
[0.1.4]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.1.3...v0.1.4
[0.1.3]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.1.2...v0.1.3
[0.1.2]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.1.1...v0.1.2
[0.1.1]: https://github.com/Automattic/jetpack-plugins-installer/compare/v0.1.0...v0.1.1
