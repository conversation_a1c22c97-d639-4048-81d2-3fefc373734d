(()=>{"use strict";var t={455:t=>{t.exports=window.wp.apiFetch},490:t=>{t.exports=window.wp.domReady},832:t=>{t.exports=window.wp.url}},e={};function a(n){var i=e[n];if(void 0!==i)return i.exports;var s=e[n]={exports:{}};return t[n](s,s.exports,a),s.exports}a.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return a.d(e,{a:e}),e},a.d=(t,e)=>{for(var n in e)a.o(e,n)&&!a.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var n=a(455),i=a.n(n),s=a(490),c=a.n(s),o=a(832);c()((function(){const t="/wpcom/v3/jitm",e={default:function(t){const e='\n                <svg class="gridicon gridicons-external-link" height="17" width="17" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">\n                    <g>\n                        <path d="M19 13v6c0 1.105-.895 2-2 2H5c-1.105 0-2-.895-2-2V7c0-1.105.895-2 2-2h6v2H5v12h12v-6h2zM13 3v2h4.586l-7.793 7.793 1.414 1.414L19 6.414V11h2V3h-8z" />\n                    </g>\n                </svg>\n                ';let a='<div class="jitm-card jitm-banner '+(t.CTA.message?"has-call-to-action":"")+" is-upgrade-premium "+t.content.classes+'" data-stats_url="'+t.jitm_stats_url+'">';if(a+='<div class="jitm-banner__background"></div>',a+='<div class="jitm-banner__content">',a+='<div class="jitm-banner__icon-plan">'+t.content.icon+"</div>",a+='<div class="jitm-banner__info">',a+='<div class="jitm-banner__title">'+t.content.message+"</div>",t.content.description&&""!==t.content.description){if(a+='<div class="jitm-banner__description">'+t.content.description,t.content.list.length>0){a+='<ul class="banner__list">';for(let n=0;n<t.content.list.length;n++){let i=t.content.list[n].item;t.content.list[n].url&&(i='<a href="'+t.content.list[n].url+'" target="_blank" rel="noopener noreferrer" data-module="'+t.feature_class+'" data-jptracks-name="nudge_item_click" data-jptracks-prop="jitm-'+t.id+'">'+i+e+"</a>"),a+='<li>\n                <svg class="gridicon gridicons-checkmark" height="16" width="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">\n                    <g>\n                        <path d="M9 19.414l-6.707-6.707 1.414-1.414L9 16.586 20.293 5.293l1.414 1.414" />\n                    </g>\n                </svg>'+i+"</li>"}}a+="</div>"}if(a+="</div>",a+="</div>",a+='<div class="jitm-banner__buttons_container">',t.activate_module&&(a+='<div class="jitm-banner__action" id="jitm-banner__activate">',a+='<a href="#" data-module="'+t.activate_module+'" data-settings_link="'+t.module_settings_link+'" type="button" class="jitm-button is-compact is-primary" data-jptracks-name="nudge_click" data-jptracks-prop="jitm-'+t.id+'-activate_module" data-jitm-path="'+t.message_path+'">'+window.jitm_config.activate_module_text+"</a>",a+="</div>",t.module_settings_link&&(a+='<div class="jitm-banner__action" id="jitm-banner__settings" style="display:none;">',a+='<a href="'+t.module_settings_link+'" type="button" class="jitm-button is-compact is-primary" data-jptracks-name="nudge_click" data-jptracks-prop="jitm-'+t.id+'-settings_link">'+window.jitm_config.settings_module_text+"</a>",a+="</div>")),t.CTA.message){let n="jitm-button is-compact";t.CTA.primary&&null===t.activate_module?n+=" is-primary":n+=" is-secondary";const i=t.CTA.ajax_action,s=t.CTA.newWindow&&!i;a+='<div class="jitm-banner__action">',a+='<a href="'+(t.CTA.hasOwnProperty("link")&&t.CTA.link.length?t.CTA.link:t.url)+'" target="'+(s?"_blank":"_self")+'" rel="noopener noreferrer" title="'+t.CTA.message+'" data-module="'+t.feature_class+'" type="button" class="'+n+'" data-jptracks-name="nudge_click" data-jptracks-prop="jitm-'+t.id+'" data-jitm-path="'+t.message_path+'" '+(i?'data-ajax-action="'+i+'"':"")+">"+t.CTA.message+(s?e:"")+"</a>",a+="</div>"}a+="</div>",t.is_dismissible&&(a+='<a href="#" data-module="'+t.feature_class+'" class="jitm-banner__dismiss"></a>'),a+="</div>";const n=document.createElement("div");return n.innerHTML=a,n.firstChild}},a=function(){document.querySelectorAll(".jetpack-jitm-message").forEach((function(a){let n=a.dataset.messagePath;const s=a.dataset.query,c=a.dataset.redirect;let r=location.hash;r=r.replace(/#\//,"_"),n.includes("jetpack_page_my-jetpack")?n=n.replace("jetpack_page_my-jetpack","jetpack_page_my-jetpack"+r):"_dashboard"!==r&&(n=n.replace("toplevel_page_jetpack","toplevel_page_jetpack"+r));const d=!!document.querySelector(".jetpack-logo__masthead");i()({path:(0,o.addQueryArgs)(t,{message_path:n,query:s,full_jp_logo_exists:d}),method:"GET"}).then((function(n){const s=n?.[0];s?.content&&function(a,n,s){let c=n.template;c&&e[c]||(c="default"),n.url=n.url+"&redirect="+encodeURIComponent(s);const o=e[c](n),r=o.querySelector(".jitm-banner__dismiss");r&&r.addEventListener("click",(function(e){e.preventDefault(),o.style.display="none",i()({path:t,method:"POST",data:{id:n.id,feature_class:n.feature_class}})}));const d=document.getElementById("jp-admin-notices");if(d){const t=d.querySelector(".jitm-card");t?t.replaceWith(o):d.prepend(o)}else a.replaceWith(o);const l=o.querySelector("#jitm-banner__activate a");l&&l.addEventListener("click",(function(t){if(t.preventDefault(),l.hasAttribute("disabled"))return!1;fetch(window.jitm_config.api_root+"jetpack/v4/module/"+l.dataset.module+"/active",{method:"POST",headers:{"X-WP-Nonce":a.dataset.nonce,"Content-Type":"application/json"},credentials:"same-origin"}).then((t=>{if(t.ok){if(l.textContent=window.jitm_config.activated_module_text,l.setAttribute("disabled",!0),l.dataset.settings_link){const t=document.getElementById("jitm-banner__settings"),e=document.getElementById("jitm-banner__activate");return t&&(t.style.display="block"),void(e&&(e.style.display="none"))}setTimeout((function(){o.style.transition="opacity 0.5s",o.style.opacity="0",setTimeout((()=>{o.style.display="none"}),500)}),2e3)}}))})),o.querySelectorAll(".jitm-button[data-ajax-action]").forEach((t=>{t.addEventListener("click",(function(e){e.preventDefault(),t.setAttribute("disabled",!0);const n=new FormData;return n.append("action",t.dataset.ajaxAction),n.append("_nonce",a.dataset.ajaxNonce),fetch(window.ajaxurl,{method:"POST",credentials:"same-origin",body:n}).then((e=>{e.ok?(o.style.transition="opacity 0.5s",o.style.opacity="0",setTimeout((()=>{o.style.display="none"}),500)):t.removeAttribute("disabled")})).catch((()=>{t.removeAttribute("disabled")})),!1}))})),o.querySelectorAll(".jitm-button").forEach((t=>{t.addEventListener("click",(function(){const e=t.getAttribute("data-jptracks-name");if(!e)return;const a={clicked:t.getAttribute("data-jptracks-prop")||!1,jitm_message_path:t.getAttribute("data-jitm-path")||!1};window.jpTracksAJAX&&window.jpTracksAJAX.record_ajax_event(e,"click",a)}))}))}(a,s,c)}))}))};a(),window.addEventListener("hashchange",(function(t){const e=t.newURL;if(["jetpack","my-jetpack","jetpack-backup","jetpack-boost","jetpack-protect","jetpack-search","jetpack-social","jetpack-videopress"].some((t=>e.includes(`admin.php?page=${t}`)))){const t=document.querySelector(".jitm-card");t&&t.remove(),a()}}))}))})();