{"name": "automattic/jetpack-jitm", "description": "Just in time messages for Jet<PERSON>", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"php": ">=7.2", "automattic/jetpack-a8c-mc-stats": "^3.0.5", "automattic/jetpack-assets": "^4.3.0", "automattic/jetpack-connection": "^6.16.0", "automattic/jetpack-device-detection": "^3.0.9", "automattic/jetpack-logo": "^3.0.5", "automattic/jetpack-redirect": "^3.0.8", "automattic/jetpack-status": "^6.0.0"}, "require-dev": {"brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/"]}, "scripts": {"build-production": ["pnpm run build-production"], "build-development": ["pnpm run build"], "phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": ["php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\""], "test-php": ["@composer phpunit"], "watch": ["Composer\\Config::disableProcessTimeout", "pnpm run watch"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-jitm", "textdomain": "jetpack-jitm", "version-constants": {"::PACKAGE_VERSION": "src/class-jitm.php"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-jitm/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "4.3.x-dev"}}}