// Just in Time Messaging - message prompts
@use "sass:color";

// TODO: Switch to using the `@automattic/jetpack-base-styles`.
@use "../../../../plugins/jetpack/_inc/client/scss/functions/rem";
@use "../../../../plugins/jetpack/_inc/client/scss/variables/colors";
@use "../../../../plugins/jetpack/_inc/client/scss/mixins/breakpoints";
@use "../../../../plugins/jetpack/_inc/client/scss/calypso-colors";

@mixin clear-fix {

	&::after {
		content: ".";
		display: block;
		height: 0;
		clear: both;
		visibility: hidden;
	}
}

@mixin jitm-banner-color( $color ) {

	&::before {
		background-color: $color;
	}

	.jitm-banner__icon {
		color: $color;
	}

	.jitm-banner__icon-circle {
		background-color: $color;
	}
}

// New JITMS - modified calypso banner styles
calypso-colors.$blue-medium-dark:       #2271b1;
$jp-gray:                #dcdcde;
$jp-gray-0:              #f6f7f7;
$jp-gray-20:             #a7aaad;

.jitm-button {
	border-style: solid;
	border-width: 1px;
	cursor: pointer;
	display: inline-block;
	margin: 0;
	outline: 0;
	overflow: hidden;
	text-overflow: ellipsis;
	text-decoration: none;
	vertical-align: top;
	box-sizing: border-box;
	font-size: 14px;
	border-radius: 4px;
	padding: 7px 14px 9px;
	-webkit-appearance: none;
	appearance: none;
	text-align: center;
	min-width: 90px;
	font-weight: 600;
	border-color: #000;
	background: calypso-colors.$white;
	color: #000;
	width: 100%;

	// Primary buttons
	&.is-primary {
		background: #000;
		color: calypso-colors.$white;

		.gridicons-external-link {
			fill: calypso-colors.$white;
		}
	}

	// Secondary buttons
	&.is-secondary {

		.gridicons-external-link {
			fill: currentColor;
		}

		&:hover {
			color: #000;
			background: $jp-gray-0;

			.gridicons-external-link {
				fill: #000;
			}
		}
	}

	&:hover,
	&:focus {
		border-color: calypso-colors.$gray-dark;
		background: calypso-colors.$gray-dark;
		color: calypso-colors.$white;
	}

	&:focus {
		box-shadow:
			0 0 0 1px calypso-colors.$white,
			0 0 0 3px calypso-colors.$blue-medium-dark;
	}

	&[disabled],
	&:disabled {
		color: $jp-gray-20;
		background: $jp-gray;
		border-color: $jp-gray;
		cursor: default;
	}

	&.is-compact {
		height: 32px;
		font-size: 14px;
		line-height: 1;
		white-space: nowrap;

		.gridicon {
			top: 4px;
			margin-top: -8px;
		}
		// Make the left margin of the small plus icon visually less huge
		.gridicons-plus-small {
			margin-left: -4px;
		}
		// Reset the left margin if the button contains only the plus icon
		.gridicons-plus-small:last-of-type {
			margin-left: 0;
		}
		// Make plus icon nudged closer to adjacent icons for
		// add-people and add-plugin type buttons
		.gridicons-plus-small + .gridicon {
			margin-left: -4px;
		}

		// Properly align icon with the button text
		.gridicons-external-link {
			margin: -3px 0 -3px 2px;
		}
	}

	&.hidden {
		display: none;
	}
}

.jitm-card {
	display: block;
	clear: both;
	position: relative;
	margin: rem.convert(48px) rem.convert(20px) 0 auto;
	padding: rem.convert(16px);
	padding-left: rem.convert(20px);
	box-sizing: border-box;
	background-color: calypso-colors.$white;
	border: 1px solid calypso-colors.$white;
	border-radius: 2px;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.16);

	@include clear-fix;

	// Compact Card
	&.is-compact {
		margin-bottom: rem.convert(1px);

		@include breakpoints.breakpoint( ">480px" ) {
			margin-bottom: 1px;
			padding: rem.convert(16px) rem.convert(24px);
		}
	}

	&.is-card-link {
		padding-right: rem.convert(48px);
	}
}

// Minor adjustments for the display in My Jetpack.
.my-jetpack-jitm-card {

	.jitm-card {
		margin-right: 0;
		margin-bottom: 1.5rem;
		margin-top: 1.5rem;
	}
}

// if JITM appears directly below WordPress "help" menu adjust margins
#screen-meta-links + .jitm-card {
	margin: rem.convert(40px) 1.5385em 0 auto;
}

// remove right margin for jitms in the editor
.post-php .jitm-card {
	margin-right: 0;
}

// if JITM appears inside of the jetpack dashboard adjust margins
.jp-lower .jitm-card {
	margin: 0 0 rem.convert(24px);
}

.jitm-banner.jitm-card {
	display: flex;
	position: relative;
	z-index: 2;

	&::before {
		content: "";
		width: 4px;
		position: absolute;
		top: 0;
		left: 0;
		height: 100%;
		border-top-left-radius: 1px;
		border-bottom-left-radius: 1px;
	}

	@include breakpoints.breakpoint( "<480px" ) {
		display: flex;
		flex-direction: column;
	}

	&.is-card-link {
		padding: rem.convert(12px) rem.convert(48px) rem.convert(12px) rem.convert(16px);
	}

	&.is-dismissible {
		padding-right: rem.convert(48px);
	}

	@include jitm-banner-color( calypso-colors.$alert-green );

	&.is-upgrade-personal {

		@include jitm-banner-color( calypso-colors.$alert-yellow );
	}

	&.is-upgrade-premium {

		@include jitm-banner-color( #069e08 );
	}

	&.is-upgrade-business,
	&.woo-jitm {

		@include jitm-banner-color( calypso-colors.$alert-purple );
	}

	.jitm-card__link-indicator {
		align-items: center;
		color: calypso-colors.$blue-wordpress;
		display: flex;
	}

	&:hover {
		transition: all 100ms ease-in-out;

		&.is-card-link {
			box-shadow: 0 0 0 1px calypso-colors.$gray, 0 2px 4px color.adjust(calypso-colors.$gray, $lightness: 20%);
		}

		.jitm-card__link-indicator {
			color: calypso-colors.$blue-dark;
		}
	}

	@include breakpoints.breakpoint( ">480px" ) {
		padding: rem.convert(16px);
		padding-left: rem.convert(20px);

		&.is-dismissible {
			padding-right: rem.convert(16px);
		}
	}
}

.jitm-banner__buttons_container {
	display: flex;
	height: 50%;
	margin: auto rem.convert(16px) auto 0;
	gap: rem.convert(16px);
	overflow: hidden;

	@include breakpoints.breakpoint( ">480px" ) {
		margin-left: rem.convert(16px);
	}
}

.jitm-banner__icons {
	display: flex;

	.jitm-banner__icon,
	.jitm-banner__icon-circle {
		border-radius: 50%;
		flex-shrink: 0;
		height: rem.convert(24px);
		width: rem.convert(24px);
		margin-right: rem.convert(16px);
		margin-top: rem.convert(-2px);
		text-align: center;
		top: rem.convert(4px);
	}

	.jitm-banner__icon {
		align-self: center;
		color: calypso-colors.$white;
		display: block;
	}

	.jitm-banner__icon-circle {
		color: #fff;
		display: none;
		padding: rem.convert(3px) rem.convert(4px) rem.convert(4px) rem.convert(3px);
	}

	@include breakpoints.breakpoint( ">480px" ) {
		align-items: center;

		.jitm-banner__icon {
			display: none;
		}

		.jitm-banner__icon-circle {
			display: block;
		}
	}
}

.jitm-banner__icon-plan {
	display: flex;
	margin-right: rem.convert(16px);

	.dops-plan-icon {
		height: rem.convert(32px);
		width: rem.convert(32px);
	}

	.jp-emblem {
		position: relative;
		top: rem.convert(2px);

		@include breakpoints.breakpoint( "<480px" ) {
			margin-bottom: rem.convert(16px);
		}

		svg {
			height: rem.convert(32px);
			width: rem.convert(32px);
			fill: colors.$green-primary;
		}

		.jitm-jp-logo {
			height: inherit;
			width: rem.convert(40px);
			fill: inherit;
		}
	}

	@include breakpoints.breakpoint( ">480px" ) {
		align-items: center;
	}
}

.jitm-banner__background {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image: url(../images/background.png);
	background-repeat: no-repeat;
	background-position: right center;
	z-index: -1;

	.rtl & {
		background-position: left center;
		transform: scaleX(-1);
	}
}

.jitm-banner__content {
	align-items: center;
	display: flex;
	flex-grow: 1;
	flex-wrap: wrap;
	flex-shrink: 100000;

	@include breakpoints.breakpoint( "<480px" ) {
		margin-right: 0;
	}

	@include breakpoints.breakpoint( ">480px" ) {
		flex-wrap: nowrap;
	}

	@include breakpoints.breakpoint( "<960px" ) {
		margin-right: 5px;
	}
}

.jitm-banner__info {
	flex-grow: 1;
	line-height: 1.4;

	@include breakpoints.breakpoint( ">480px" ) {
		flex-basis: 50%;
	}

	@include breakpoints.breakpoint( ">960px" ) {
		flex-basis: 70%;
	}

	.jitm-banner__title,
	.jitm-banner__description,
	.jitm-banner__description a {
		color: #000;
	}

	.jitm-banner__title {
		font-size: rem.convert(15px);
		font-weight: 700;
		// stylelint-disable-next-line declaration-property-unit-allowed-list -- this should be changed to a unitless value: https://developer.wordpress.org/coding-standards/wordpress-coding-standards/css/#values
		line-height: 136%;
	}

	.jitm-banner__description {
		font-size: rem.convert(13px);
		// stylelint-disable-next-line declaration-property-unit-allowed-list -- this should be changed to a unitless value: https://developer.wordpress.org/coding-standards/wordpress-coding-standards/css/#values
		line-height: 150%;
		margin-top: rem.convert(2px);
	}

	.banner__list {
		font-size: 12px;
		list-style: none;
		margin: 10px 0;

		li {
			margin: 6px 0;

			.gridicon {
				fill: calypso-colors.$gray;
				display: inline;
				margin-right: 12px;
				vertical-align: bottom;
			}
		}
	}
}

.jitm-banner__action {
	align-self: center;
	font-size: rem.convert(12px);
	text-align: left;
	overflow: hidden;

	.jitm-banner__prices {
		display: flex;
		justify-content: flex-start;

		.dops-plan-price {
			margin-bottom: 0;
		}

		.dops-plan-price.is-discounted,
		.dops-plan-price.is-discounted .dops-plan-price__currency-symbol {
			color: calypso-colors.$gray-dark;
		}

		.has-call-to-action & .dops-plan-price {
			margin-bottom: rem.convert(8px);
		}
	}

	@include breakpoints.breakpoint( "<480px" ) {
		margin-top: 1rem;
	}

	@include breakpoints.breakpoint( ">480px" ) {
		text-align: center;
		width: auto;

		.is-dismissible {
			margin-top: rem.convert(40px);
		}

		.jitm-banner__prices {
			justify-content: flex-end;
			text-align: right;
		}
	}
}

.jitm-banner__dismiss {
	display: block;
	text-decoration: none;
	line-height: 0.5;
	margin-top: auto;
	margin-bottom: auto;

	&::before {
		color: color.adjust(calypso-colors.$gray, $lightness: -20%);
		font: 400 20px/1 dashicons;
		content: "\f335";
	}

	@include breakpoints.breakpoint( ">660px" ) {
		margin-right: rem.convert(-8px);
	}

	@include breakpoints.breakpoint( "<480px" ) {
		position: absolute;
		top: 0;
		right: 0;

		display: flex;
		justify-content: center;
		align-items: center;
		margin: 0;

		// Minimum touch area
		width: 48px;
		height: 48px;
	}
}

.jitm-banner__action + .jitm-banner__dismiss {
	margin-left: rem.convert(10px);
}

// if JITM appears directly below WordPress hello dolly adjust margins
#dolly + .jitm-card {
	margin: 3rem 1rem 0 auto;
}

.jetpack_page_jetpack-forms-admin .jitm-card {
	margin-left: 1.25rem;
}
