# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.3.4] - 2025-07-21
### Changed
- Update package dependencies. [#44356]

## [0.3.3] - 2025-07-08
### Changed
- Update package dependencies. [#44217]

## [0.3.2] - 2025-07-03
### Changed
- Update package dependencies. [#44151]

## [0.3.1] - 2025-06-30
### Changed
- Update dependencies. [#43068]

## [0.3.0] - 2025-06-27
### Changed
- Create custom explat client with public-api fetch for assignments. [#44081]

## [0.2.26] - 2025-06-24
### Added
- Improve error handling on Chrome AI events. [#44048]

## [0.2.25] - 2025-06-23
### Changed
- Update package dependencies. [#44020]

## [0.2.24] - 2025-06-18
### Changed
- Internal updates.

## [0.2.23] - 2025-06-05
### Changed
- Update package dependencies. [#43718] [#43734] [#43766]

## [0.2.22] - 2025-06-02
### Changed
- Update dependencies. [#43068]

## [0.2.21] - 2025-05-26
### Changed
- Update package dependencies. [#43578]

## [0.2.20] - 2025-05-22
### Changed
- Update package dependencies. [#43557]

## [0.2.19] - 2025-05-12
### Changed
- Stable release management: Do not ship source files. [#43310]

## [0.2.18] - 2025-05-05
### Changed
- Update package dependencies. [#43326]

## [0.2.17] - 2025-04-28
### Changed
- Internal updates.

## [0.2.16] - 2025-04-14
### Changed
- Update dependencies. [#43001]

## [0.2.15] - 2025-04-03
### Changed
- Update package dependencies. [#42809]

## [0.2.14] - 2025-04-01
### Changed
- Update package dependencies. [#42762]

## [0.2.13] - 2025-03-21
### Changed
- Internal updates.

## [0.2.12] - 2025-03-18
### Changed
- Update package dependencies. [#42511]

## [0.2.11] - 2025-03-12
### Changed
- Internal updates.

## [0.2.10] - 2025-03-05
### Changed
- Internal updates.

## [0.2.9] - 2025-03-03
### Changed
- Update package dependencies. [#42163]

## [0.2.8] - 2025-02-24
### Changed
- Update dependencies.

## [0.2.7] - 2025-02-17
### Changed
- Update dependencies.

## [0.2.6] - 2025-02-10
### Changed
- Updated package dependencies. [#41491]

## [0.2.5] - 2025-02-03
### Changed
- Updated package dependencies. [#41286]

## [0.2.4] - 2025-01-20
### Changed
- Updated package dependencies. [#41099]

## [0.2.3] - 2024-12-16
### Changed
- Updated package dependencies. [#40564]

## [0.2.2] - 2024-12-04
### Changed
- Updated package dependencies. [#40363] [#40372]

## [0.2.1] - 2024-11-25
### Changed
- Updated package dependencies. [#40232] [#40288]

## [0.2.0] - 2024-11-14
### Removed
- General: Update minimum PHP version to 7.2. [#40147]

## [0.1.15] - 2024-11-11
### Changed
- Updated package dependencies. [#39999] [#40000] [#40060]

## [0.1.14] - 2024-11-04
### Added
- Enable test coverage. [#39961]

## [0.1.13] - 2024-10-29
### Changed
- Internal updates.

## [0.1.12] - 2024-10-14
### Changed
- Only include `wp-polyfill` as a script dependency when needed. [#39629]

## [0.1.11] - 2024-10-10
### Changed
- Updated package dependencies. [#39649] [#39707]

## [0.1.10] - 2024-10-07
### Changed
- Updated package dependencies. [#39594]

## [0.1.9] - 2024-09-23
### Changed
- Update dependencies.

## [0.1.8] - 2024-09-10
### Changed
- Updated package dependencies. [#39302]

## [0.1.7] - 2024-09-05
### Changed
- Update dependencies.

## [0.1.6] - 2024-09-05
### Changed
- Updated package dependencies. [#39176]

## [0.1.5] - 2024-08-29
### Changed
- Updated package dependencies. [#39111]

## [0.1.4] - 2024-08-26
### Changed
- Updated package dependencies. [#39004]

## [0.1.3] - 2024-08-21
### Changed
- Internal updates.

## [0.1.2] - 2024-08-15
### Changed
- Updated package dependencies. [#38662]

## [0.1.1] - 2024-08-01
### Changed
- Internal updates.

## 0.1.0 - 2024-07-29
### Added
- Adds a new component to fetch experiments specifically for authenticated users [#37999]
- Initial version. [#37910]
- Introduce the both the backend layer and frontend components for the ExPlat package. [#37958]

### Changed
- ExPlat: add condition to prevent fetching the experiment assignment if there's not anon id (meaning that Tracks is likely disabled) [#38327]
- Updated package dependencies. [#38132]

[0.3.4]: https://github.com/Automattic/jetpack-explat/compare/v0.3.3...v0.3.4
[0.3.3]: https://github.com/Automattic/jetpack-explat/compare/v0.3.2...v0.3.3
[0.3.2]: https://github.com/Automattic/jetpack-explat/compare/v0.3.1...v0.3.2
[0.3.1]: https://github.com/Automattic/jetpack-explat/compare/v0.3.0...v0.3.1
[0.3.0]: https://github.com/Automattic/jetpack-explat/compare/v0.2.26...v0.3.0
[0.2.26]: https://github.com/Automattic/jetpack-explat/compare/v0.2.25...v0.2.26
[0.2.25]: https://github.com/Automattic/jetpack-explat/compare/v0.2.24...v0.2.25
[0.2.24]: https://github.com/Automattic/jetpack-explat/compare/v0.2.23...v0.2.24
[0.2.23]: https://github.com/Automattic/jetpack-explat/compare/v0.2.22...v0.2.23
[0.2.22]: https://github.com/Automattic/jetpack-explat/compare/v0.2.21...v0.2.22
[0.2.21]: https://github.com/Automattic/jetpack-explat/compare/v0.2.20...v0.2.21
[0.2.20]: https://github.com/Automattic/jetpack-explat/compare/v0.2.19...v0.2.20
[0.2.19]: https://github.com/Automattic/jetpack-explat/compare/v0.2.18...v0.2.19
[0.2.18]: https://github.com/Automattic/jetpack-explat/compare/v0.2.17...v0.2.18
[0.2.17]: https://github.com/Automattic/jetpack-explat/compare/v0.2.16...v0.2.17
[0.2.16]: https://github.com/Automattic/jetpack-explat/compare/v0.2.15...v0.2.16
[0.2.15]: https://github.com/Automattic/jetpack-explat/compare/v0.2.14...v0.2.15
[0.2.14]: https://github.com/Automattic/jetpack-explat/compare/v0.2.13...v0.2.14
[0.2.13]: https://github.com/Automattic/jetpack-explat/compare/v0.2.12...v0.2.13
[0.2.12]: https://github.com/Automattic/jetpack-explat/compare/v0.2.11...v0.2.12
[0.2.11]: https://github.com/Automattic/jetpack-explat/compare/v0.2.10...v0.2.11
[0.2.10]: https://github.com/Automattic/jetpack-explat/compare/v0.2.9...v0.2.10
[0.2.9]: https://github.com/Automattic/jetpack-explat/compare/v0.2.8...v0.2.9
[0.2.8]: https://github.com/Automattic/jetpack-explat/compare/v0.2.7...v0.2.8
[0.2.7]: https://github.com/Automattic/jetpack-explat/compare/v0.2.6...v0.2.7
[0.2.6]: https://github.com/Automattic/jetpack-explat/compare/v0.2.5...v0.2.6
[0.2.5]: https://github.com/Automattic/jetpack-explat/compare/v0.2.4...v0.2.5
[0.2.4]: https://github.com/Automattic/jetpack-explat/compare/v0.2.3...v0.2.4
[0.2.3]: https://github.com/Automattic/jetpack-explat/compare/v0.2.2...v0.2.3
[0.2.2]: https://github.com/Automattic/jetpack-explat/compare/v0.2.1...v0.2.2
[0.2.1]: https://github.com/Automattic/jetpack-explat/compare/v0.2.0...v0.2.1
[0.2.0]: https://github.com/Automattic/jetpack-explat/compare/v0.1.15...v0.2.0
[0.1.15]: https://github.com/Automattic/jetpack-explat/compare/v0.1.14...v0.1.15
[0.1.14]: https://github.com/Automattic/jetpack-explat/compare/v0.1.13...v0.1.14
[0.1.13]: https://github.com/Automattic/jetpack-explat/compare/v0.1.12...v0.1.13
[0.1.12]: https://github.com/Automattic/jetpack-explat/compare/v0.1.11...v0.1.12
[0.1.11]: https://github.com/Automattic/jetpack-explat/compare/v0.1.10...v0.1.11
[0.1.10]: https://github.com/Automattic/jetpack-explat/compare/v0.1.9...v0.1.10
[0.1.9]: https://github.com/Automattic/jetpack-explat/compare/v0.1.8...v0.1.9
[0.1.8]: https://github.com/Automattic/jetpack-explat/compare/v0.1.7...v0.1.8
[0.1.7]: https://github.com/Automattic/jetpack-explat/compare/v0.1.6...v0.1.7
[0.1.6]: https://github.com/Automattic/jetpack-explat/compare/v0.1.5...v0.1.6
[0.1.5]: https://github.com/Automattic/jetpack-explat/compare/v0.1.4...v0.1.5
[0.1.4]: https://github.com/Automattic/jetpack-explat/compare/v0.1.3...v0.1.4
[0.1.3]: https://github.com/Automattic/jetpack-explat/compare/v0.1.2...v0.1.3
[0.1.2]: https://github.com/Automattic/jetpack-explat/compare/v0.1.1...v0.1.2
[0.1.1]: https://github.com/Automattic/jetpack-explat/compare/v0.1.0...v0.1.1
