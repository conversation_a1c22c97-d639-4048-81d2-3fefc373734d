(()=>{var e={792:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(790),s=n(609);const o={isEligible:!0};function i(e){const t=(t,n={})=>{const r={...o,...n},[,i]=(0,s.useReducer)((e=>e+1),0),a=(0,s.useRef)(t);if((0,s.useEffect)((()=>{let n=!0;return r.isEligible&&e.loadExperimentAssignment(t).then((()=>{n&&i()})),()=>{n=!1}}),[t,r.isEligible]),t===a.current||a.current.startsWith("explat_test")||e.config.logError({message:"[ExPlat] useExperiment: experimentName should never change between renders!"}),!r.isEligible)return[!1,null];const c=e.dangerouslyGetMaybeLoadedExperimentAssignment(t);return[!c,c]};return{useExperiment:t,Experiment:({defaultExperience:e,treatmentExperience:n,loadingExperience:s,name:o,options:i})=>{const[a,c]=t(o,i);return a?(0,r.jsx)(r.Fragment,{children:s}):c?.variationName?(0,r.jsx)(r.Fragment,{children:n}):(0,r.jsx)(r.Fragment,{children:e})},ProvideExperimentData:({children:e,name:n,options:r})=>{const[s,o]=t(n,r);return e(s,o)}}}},517:(e,t,n)=>{"use strict";n.d(t,{kU:()=>u,pg:()=>m});var r=n(689),s=n(808),o=n(738),i=n(762),a=n(626);const c=1e4;Error;function u(e){if("undefined"==typeof window)throw new Error("Running outside of a browser context.");const t={},n=(...t)=>{try{e.logError(...t)}catch(e){}};try{(0,r.bZ)()}catch(e){n({message:e.message,source:"removeExpiredExperimentAssignments-error"})}return{loadExperimentAssignment:async u=>{try{if(!a.Eo(u))throw new Error(`Invalid experimentName: "${u}"`);const n=(0,r.B1)(u);if(n&&s.H2(n))return n;void 0===t[u]&&(t[u]=(t=>i.MC((async()=>{const n=await o.FI(e,t);return(0,r.a2)(n),n})))(u));let m=c;Math.random()>.5&&(m=5e3);const l=await i.BK(t[u](),m);if(!l)throw new Error("Could not fetch ExperimentAssignment");return l}catch(e){n({message:e.message,experimentName:u,source:"loadExperimentAssignment-initialError"})}try{const e=(0,r.B1)(u);if(e)return e;const t=(0,s.fj)(u);return(0,r.a2)(t),t}catch(e){return n({message:e.message,experimentName:u,source:"loadExperimentAssignment-fallbackError"}),(0,s.fj)(u)}},dangerouslyGetExperimentAssignment:t=>{try{if(!a.Eo(t))throw new Error(`Invalid experimentName: ${t}`);const s=(0,r.B1)(t);if(!s)throw new Error("Trying to dangerously get an ExperimentAssignment that hasn't loaded.");return e.isDevelopmentMode&&s&&i.XZ()-s.retrievedTimestamp<1e3&&n({message:"Warning: Trying to dangerously get an ExperimentAssignment too soon after loading it.",experimentName:t,source:"dangerouslyGetExperimentAssignment"}),s}catch(r){return e.isDevelopmentMode&&n({message:r.message,experimentName:t,source:"dangerouslyGetExperimentAssignment-error"}),(0,s.fj)(t)}},dangerouslyGetMaybeLoadedExperimentAssignment:t=>{try{if(!a.Eo(t))throw new Error(`Invalid experimentName: ${t}`);const e=(0,r.B1)(t);return e||null}catch(r){return e.isDevelopmentMode&&n({message:r.message,experimentName:t,source:"dangerouslyGetMaybeLoadedExperimentAssignment-error"}),(0,s.fj)(t)}},config:e}}function m(e){return{loadExperimentAssignment:async t=>(e.logError({message:"Attempting to load ExperimentAssignment in SSR context",experimentName:t}),(0,s.fj)(t)),dangerouslyGetExperimentAssignment:t=>(e.logError({message:"Attempting to dangerously get ExperimentAssignment in SSR context",experimentName:t}),(0,s.fj)(t)),dangerouslyGetMaybeLoadedExperimentAssignment:t=>(e.logError({message:"Attempting to dangerously get ExperimentAssignment in SSR context",experimentName:t}),(0,s.fj)(t)),config:e}}},226:(e,t,n)=>{"use strict";n.d(t,{k:()=>s});var r=n(517);const s="undefined"==typeof window?r.pg:r.kU},689:(e,t,n)=>{"use strict";n.d(t,{B1:()=>u,a2:()=>c,bZ:()=>f});var r=n(808),s=n(765),o=n(626);const i="explat-experiment-",a=e=>`${i}-${e}`;function c(e){o.zV(e);const t=u(e.experimentName);if(t&&e.retrievedTimestamp<t.retrievedTimestamp)throw new Error("Trying to store an older experiment assignment than is present in the store, likely a race condition.");s.A.setItem(a(e.experimentName),JSON.stringify(e))}function u(e){const t=s.A.getItem(a(e));if(t)return o.zV(JSON.parse(t))}const m=e=>[...Array(e).keys()];function l(e){return e.startsWith(i)}function d(e){return e.slice(i.length+1)}function f(){m(s.A.length).map((e=>s.A.key(e))).filter(l).map(d).filter((e=>{try{if(r.H2(u(e)))return!1}catch(e){}return!0})).map(a).map((e=>s.A.removeItem(e)))}},808:(e,t,n)=>{"use strict";n.d(t,{H2:()=>s,fj:()=>i,fn:()=>o});var r=n(762);function s(e){return r.XZ()<e.ttl*r.If+e.retrievedTimestamp}const o=60,i=(e,t=o)=>({experimentName:e,variationName:null,retrievedTimestamp:r.XZ(),ttl:Math.max(o,t),isFallbackExperimentAssignment:!0})},765:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});let r={_data:{},setItem:function(e,t){this._data[e]=t},getItem:function(e){return this._data.hasOwnProperty(e)?this._data[e]:null},removeItem:function(e){delete this._data[e]},clear:function(){this._data={}},get length(){return Object.keys(this._data).length},key:function(e){const t=Object.keys(this._data);return e in t?t[e]:null}};try{window.localStorage&&(r=window.localStorage)}catch(e){}const s=r},738:(e,t,n)=>{"use strict";n.d(t,{FI:()=>l});var r=n(808),s=n(765),o=n(762),i=n(626);function a(e){if(function(e){return(0,i.Gv)(e)&&(0,i.Gv)(e.variations)&&"number"==typeof e.ttl&&0<e.ttl}(e))return e;throw new Error("Invalid FetchExperimentAssignmentResponse")}const c="explat-last-anon-id",u="explat-last-anon-id-retrieval-time",m=async e=>{const t=await e();if(t)return s.A.setItem(c,t),s.A.setItem(u,String((0,o.XZ)())),t;const n=s.A.getItem(c),r=s.A.getItem(u);return n&&r&&(0,o.XZ)()-parseInt(r,10)<864e5?n:null};async function l(e,t){const n=(0,o.XZ)(),{variations:s,ttl:c}=a(await e.fetchExperimentAssignment({anonId:await m(e.getAnonId),experimentName:t})),u=Math.max(r.fn,c),l=Object.entries(s).map((([e,t])=>({experimentName:e,variationName:t,retrievedTimestamp:n,ttl:u}))).map(i.zV);if(l.length>1)throw new Error("Received multiple experiment assignments while trying to fetch exactly one.");if(0===l.length)return r.fj(t,u);const d=l[0];if(d.experimentName!==t)throw new Error("Newly fetched ExperimentAssignment's experiment name does not match request.");if(!r.H2(d))throw new Error("Newly fetched experiment isn't alive.");return d}},762:(e,t,n)=>{"use strict";n.d(t,{BK:()=>i,If:()=>r,MC:()=>a,XZ:()=>o});const r=1e3;let s=Date.now();function o(){const e=Date.now();return s=s<e?e:s+1,s}function i(e,t){return Promise.race([e,new Promise(((e,n)=>setTimeout((()=>n(new Error(`Promise has timed-out after ${t}ms.`))),t)))])}function a(e){let t=null;return()=>(t||(t=e().finally((()=>{t=null}))),t)}},626:(e,t,n)=>{"use strict";function r(e){return"object"==typeof e&&null!==e}function s(e){return"string"==typeof e&&""!==e&&/^[a-z0-9_]*$/.test(e)}function o(e){if(!function(e){return r(e)&&s(e.experimentName)&&(s(e.variationName)||null===e.variationName)&&"number"==typeof e.retrievedTimestamp&&"number"==typeof e.ttl&&0!==e.ttl}(e))throw new Error("Invalid ExperimentAssignment");return e}n.d(t,{Eo:()=>s,Gv:()=>r,zV:()=>o})},172:(e,t)=>{"use strict";t.qg=function(e,t){const n=new a,r=e.length;if(r<2)return n;const s=t?.decode||m;let o=0;do{const t=e.indexOf("=",o);if(-1===t)break;const i=e.indexOf(";",o),a=-1===i?r:i;if(t>a){o=e.lastIndexOf(";",t-1)+1;continue}const m=c(e,o,t),l=u(e,t,m),d=e.slice(m,l);if(void 0===n[d]){let r=c(e,t+1,a),o=u(e,a,r);const i=s(e.slice(r,o));n[d]=i}o=a+1}while(o<r);return n};const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,s=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,i=Object.prototype.toString,a=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function c(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function u(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function m(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},941:(e,t,n)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;t.splice(1,0,n,"color: inherit");let r=0,s=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(r++,"%c"===e&&(s=r))})),t.splice(s,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")||t.storage.getItem("DEBUG")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=n(212)(t);const{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},212:(e,t,n)=>{e.exports=function(e){function t(e){let n,s,o,i=null;function a(...e){if(!a.enabled)return;const r=a,s=Number(new Date),o=s-(n||s);r.diff=o,r.prev=n,r.curr=s,n=s,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,s)=>{if("%%"===n)return"%";i++;const o=t.formatters[s];if("function"==typeof o){const t=e[i];n=o.call(r,t),e.splice(i,1),i--}return n})),t.formatArgs.call(r,e);(r.log||t.log).apply(r,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(s!==t.namespaces&&(s=t.namespaces,o=t.enabled(e)),o),set:e=>{i=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,n){const r=t(this.namespace+(void 0===n?":":n)+e);return r.log=this.log,r}function s(e,t){let n=0,r=0,s=-1,o=0;for(;n<e.length;)if(r<t.length&&(t[r]===e[n]||"*"===t[r]))"*"===t[r]?(s=r,o=n,r++):(n++,r++);else{if(-1===s)return!1;r=s+1,o++,n=o}for(;r<t.length&&"*"===t[r];)r++;return r===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const n=("string"==typeof e?e:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const e of n)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const n of t.skips)if(s(e,n))return!1;for(const n of t.names)if(s(e,n))return!0;return!1},t.humanize=n(997),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((n=>{t[n]=e[n]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t}},997:e=>{var t=1e3,n=60*t,r=60*n,s=24*r,o=7*s,i=365.25*s;function a(e,t,n,r){var s=t>=1.5*n;return Math.round(e/n)+" "+r+(s?"s":"")}e.exports=function(e,c){c=c||{};var u=typeof e;if("string"===u&&e.length>0)return function(e){if((e=String(e)).length>100)return;var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!a)return;var c=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*i;case"weeks":case"week":case"w":return c*o;case"days":case"day":case"d":return c*s;case"hours":case"hour":case"hrs":case"hr":case"h":return c*r;case"minutes":case"minute":case"mins":case"min":case"m":return c*n;case"seconds":case"second":case"secs":case"sec":case"s":return c*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(e);if("number"===u&&isFinite(e))return c.long?function(e){var o=Math.abs(e);if(o>=s)return a(e,o,s,"day");if(o>=r)return a(e,o,r,"hour");if(o>=n)return a(e,o,n,"minute");if(o>=t)return a(e,o,t,"second");return e+" ms"}(e):function(e){var o=Math.abs(e);if(o>=s)return Math.round(e/s)+"d";if(o>=r)return Math.round(e/r)+"h";if(o>=n)return Math.round(e/n)+"m";if(o>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},889:(e,t,n)=>{"use strict";n.d(t,{Ck:()=>i,wf:()=>o});var r=n(172);let s=null;const o=async()=>{let e=0;return s=new Promise((t=>{const n=()=>{const s=(0,r.qg)(document.cookie).tk_ai||null;"string"!=typeof s||""===s?99<=e?t(null):(e+=1,setTimeout(n,50)):t(s)};n()})),s},i=async()=>await s},222:(e,t,n)=>{"use strict";n.d(t,{V:()=>m,z:()=>u});var r=n(455),s=n.n(r),o=n(832),i=n(941);const a=n.n(i)()("jetpack-explat:client:assignment"),c=(e=!1)=>async({experimentName:t,anonId:n})=>{if(!n)throw a("anonId is null"),new Error("Tracking is disabled, can't fetch experimentAssignment");const r={experiment_name:t,anon_id:n??void 0,as_connected_user:e};a("params",r);const i=(0,o.addQueryArgs)("jetpack/v4/explat/assignments",r);return a("assignmentsRequestUrl",i),await s()({path:i})},u=c(!1),m=c(!0)},69:(e,t,n)=>{"use strict";n.d(t,{v:()=>s});var r=n(382);const s=e=>{const t=e=>{r.D&&console.error("[ExPlat] Unable to send error to server:",e)};try{const{message:n,...s}=e,o={message:n,properties:{...s,context:"explat",explat_client:"jetpack"}};if(r.D)console.error("[ExPlat] ",e.message,e);else{const e=new window.FormData;e.append("error",JSON.stringify(o)),window.fetch("https://public-api.wordpress.com/rest/v1.1/js-error",{method:"POST",body:e}).catch(t)}}catch(e){t(e)}}},382:(e,t,n)=>{"use strict";n.d(t,{D:()=>r});const r=!1},609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},455:e=>{"use strict";e.exports=window.wp.apiFetch},832:e=>{"use strict";e.exports=window.wp.url}},t={};function n(r){var s=t[r];if(void 0!==s)return s.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=n(226),t=n(792),r=n(941),s=n.n(r),o=n(889),i=n(222),a=n(69),c=n(382);const u=s()("jetpack-explat:client");(async()=>{u("initializing explat"),(0,o.wf)().catch((e=>(0,a.v)({message:e.message})))})();const m=(0,e.k)({fetchExperimentAssignment:i.z,getAnonId:o.Ck,logError:a.v,isDevelopmentMode:c.D}),{loadExperimentAssignment:l,dangerouslyGetExperimentAssignment:d}=m,{useExperiment:f,Experiment:p,ProvideExperimentData:g}=(0,t.A)(m),C=(0,e.k)({fetchExperimentAssignment:i.V,getAnonId:o.Ck,logError:a.v,isDevelopmentMode:c.D}),{loadExperimentAssignment:h,dangerouslyGetExperimentAssignment:x}=C,{useExperiment:y,Experiment:w,ProvideExperimentData:E}=(0,t.A)(C)})()})();