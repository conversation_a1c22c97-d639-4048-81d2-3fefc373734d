# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.3.14] - 2025-07-23
### Changed
- Internal updates.

## [0.3.13] - 2025-07-08
### Changed
- Update dependencies. [#44142]

## [0.3.12] - 2025-06-23
### Changed
- Update dependencies. [#44002]

## [0.3.11] - 2025-06-04
### Changed
- Update dependencies. [#43569]
- Update package dependencies. [#43766]

## [0.3.10] - 2025-05-15
### Changed
- Update package dependencies. [#43398] [#43400]

## [0.3.9] - 2025-04-16
### Changed
- Code: First pass of style coding standards. [#42734]

## [0.3.8] - 2025-04-03
### Changed
- Update dependencies. [#42830]

## [0.3.7] - 2025-04-01
### Changed
- Update package dependencies. [#42762]

## [0.3.6] - 2025-03-26
### Changed
- Internal updates.

## [0.3.5] - 2025-03-18
### Changed
- Internal updates.

## [0.3.4] - 2025-03-05
### Changed
- Update dependencies. [#41847]

## [0.3.3] - 2025-02-12
### Changed
- Updated package dependencies. [#41286]

## [0.3.2] - 2025-01-23
### Changed
- Code: Use function-style exit() and die() with a default status code of 0. [#41167]

## [0.3.1] - 2025-01-06
### Changed
- Internal updates.

## [0.3.0] - 2024-11-28
### Removed
- General: Update minimum PHP version to 7.2. [#40147]

## [0.2.4] - 2024-11-04
### Added
- Enable test coverage. [#39961]

## [0.2.3] - 2024-10-29
### Changed
- Only include `wp-polyfill` as a script dependency when needed. [#39629]

## [0.2.2] - 2024-08-29
### Changed
- Updated package dependencies. [#39004] [#39111]

## [0.2.1] - 2024-03-15
### Changed
- Internal updates.

## [0.2.0] - 2024-01-22
### Changed
- The package now requires PHP >= 7.0. [#34192]

## [0.1.6] - 2023-10-26

- Updated package dependencies.

## [0.1.5] - 2023-09-01
### Changed
- Updated package dependencies. [#32040] [#32605]

## [0.1.4] - 2023-05-11
### Added
- Add Jetpack Autoloader package suggestion. [#29988]

### Changed
- Updated package dependencies.

## [0.1.3] - 2023-04-06
### Changed
- Updated package dependencies. [#29471]

## [0.1.2] - 2023-01-17
### Changed
- Updated package dependencies.

### Fixed
- Updating package to fix boost beta release [#27550]

## [0.1.1] - 2022-11-22
### Fixed
- Fixing plugin-deactivation package

## 0.1.0 - 2022-11-21
### Added
- Added package to intercept plugin deactivation [#27081]

[0.3.14]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.3.13...v0.3.14
[0.3.13]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.3.12...v0.3.13
[0.3.12]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.3.11...v0.3.12
[0.3.11]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.3.10...v0.3.11
[0.3.10]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.3.9...v0.3.10
[0.3.9]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.3.8...v0.3.9
[0.3.8]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.3.7...v0.3.8
[0.3.7]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.3.6...v0.3.7
[0.3.6]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.3.5...v0.3.6
[0.3.5]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.3.4...v0.3.5
[0.3.4]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.3.3...v0.3.4
[0.3.3]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.3.2...v0.3.3
[0.3.2]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.3.1...v0.3.2
[0.3.1]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.3.0...v0.3.1
[0.3.0]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.2.4...v0.3.0
[0.2.4]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.2.3...v0.2.4
[0.2.3]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.2.2...v0.2.3
[0.2.2]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.2.1...v0.2.2
[0.2.1]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.2.0...v0.2.1
[0.2.0]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.1.6...v0.2.0
[0.1.6]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.1.5...v0.1.6
[0.1.5]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.1.4...v0.1.5
[0.1.4]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.1.3...v0.1.4
[0.1.3]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.1.2...v0.1.3
[0.1.2]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.1.1...v0.1.2
[0.1.1]: https://github.com/Automattic/jetpack-plugin-deactivation/compare/v0.1.0...v0.1.1
