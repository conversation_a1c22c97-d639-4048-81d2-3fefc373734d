{"name": "automattic/jetpack-plugin-deactivation", "description": "Ask for feedback while deactivating a plugin", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"php": ">=7.2", "automattic/jetpack-assets": "^4.3.0"}, "require-dev": {"yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": ["php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\""], "test-php": ["@composer phpunit"], "build-development": ["pnpm run build"], "build-production": ["NODE_ENV=production pnpm run build"], "watch": ["Composer\\Config::disableProcessTimeout", "pnpm run watch"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"mirror-repo": "Automattic/jetpack-plugin-deactivation", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-plugin-deactivation/compare/v${old}...v${new}"}, "autotagger": true, "branch-alias": {"dev-trunk": "0.3.x-dev"}, "textdomain": "jetpack-plugin-deactivation", "version-constants": {"::PACKAGE_VERSION": "src/class-deactivation-handler.php"}}}