(()=>{var e={2144:()=>{},2145:()=>{},9958:()=>{},9626:()=>{},4804:(e,t,n)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;t.splice(1,0,n,"color: inherit");let s=0,r=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(s++,"%c"===e&&(r=s))})),t.splice(r,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")||t.storage.getItem("DEBUG")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=n(5067)(t);const{formatters:s}=e.exports;s.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},5067:(e,t,n)=>{e.exports=function(e){function t(e){let n,r,o,i=null;function a(...e){if(!a.enabled)return;const s=a,r=Number(new Date),o=r-(n||r);s.diff=o,s.prev=n,s.curr=r,n=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,r)=>{if("%%"===n)return"%";i++;const o=t.formatters[r];if("function"==typeof o){const t=e[i];n=o.call(s,t),e.splice(i,1),i--}return n})),t.formatArgs.call(s,e);(s.log||t.log).apply(s,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=s,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(r!==t.namespaces&&(r=t.namespaces,o=t.enabled(e)),o),set:e=>{i=e}}),"function"==typeof t.init&&t.init(a),a}function s(e,n){const s=t(this.namespace+(void 0===n?":":n)+e);return s.log=this.log,s}function r(e,t){let n=0,s=0,r=-1,o=0;for(;n<e.length;)if(s<t.length&&(t[s]===e[n]||"*"===t[s]))"*"===t[s]?(r=s,o=n,s++):(n++,s++);else{if(-1===r)return!1;s=r+1,o++,n=o}for(;s<t.length&&"*"===t[s];)s++;return s===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const n=("string"==typeof e?e:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const e of n)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const n of t.skips)if(r(e,n))return!1;for(const n of t.names)if(r(e,n))return!0;return!1},t.humanize=n(3594),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((n=>{t[n]=e[n]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t}},3594:e=>{var t=1e3,n=60*t,s=60*n,r=24*s,o=7*r,i=365.25*r;function a(e,t,n,s){var r=t>=1.5*n;return Math.round(e/n)+" "+s+(r?"s":"")}e.exports=function(e,c){c=c||{};var d=typeof e;if("string"===d&&e.length>0)return function(e){if((e=String(e)).length>100)return;var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!a)return;var c=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*i;case"weeks":case"week":case"w":return c*o;case"days":case"day":case"d":return c*r;case"hours":case"hour":case"hrs":case"hr":case"h":return c*s;case"minutes":case"minute":case"mins":case"min":case"m":return c*n;case"seconds":case"second":case"secs":case"sec":case"s":return c*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(e);if("number"===d&&isFinite(e))return c.long?function(e){var o=Math.abs(e);if(o>=r)return a(e,o,r,"day");if(o>=s)return a(e,o,s,"hour");if(o>=n)return a(e,o,n,"minute");if(o>=t)return a(e,o,t,"second");return e+" ms"}(e):function(e){var o=Math.abs(e);if(o>=r)return Math.round(e/r)+"d";if(o>=s)return Math.round(e/s)+"h";if(o>=n)return Math.round(e/n)+"m";if(o>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},1583:(e,t,n)=>{"use strict";var s=n(1752);function r(){}function o(){}o.resetWarningCache=r,e.exports=function(){function e(e,t,n,r,o,i){if(i!==s){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:r};return n.PropTypes=n,n}},3619:(e,t,n)=>{e.exports=n(1583)()},1752:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},372:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var s=n(4804);const r=n.n(s)()("dops:analytics");let o,i;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const a={initialize:function(e,t,n){a.setUser(e,t),a.setSuperProps(n),a.identifyUser()},setGoogleAnalyticsEnabled:function(e,t=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){i={ID:e,username:t}},setSuperProps:function(e){o=e},assignSuperProps:function(e){o=Object.assign(o||{},e)},mc:{bumpStat:function(e,t){const n=function(e,t){let n="";if("object"==typeof e){for(const t in e)n+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);r("Bumping stats %o",e)}else n="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),r('Bumping stat "%s" in group "%s"',t,e);return n}(e,t);a.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+n+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const n=function(e,t){let n="";if("object"==typeof e){for(const t in e)n+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);r("Built stats %o",e)}else n="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),r('Built stat "%s" in group "%s"',t,e);return n}(e,t);a.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+n+"&t="+Math.random())}},pageView:{record:function(e,t){a.tracks.recordPageView(e),a.ga.recordPageView(e,t)}},purchase:{record:function(e,t,n,s,r,o,i){a.ga.recordPurchase(e,t,n,s,r,o,i)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(o&&(r("- Super Props: %o",o),t=Object.assign(t,o)),r('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):r('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};a.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){a.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){r("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};a.ga.initialized||(i&&(e={userId:"u-"+i.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),a.ga.initialized=!0)},recordPageView:function(e,t){a.ga.initialize(),r("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,n,s){a.ga.initialize();let o="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==n&&(o+=" [Option Label: "+n+"]"),void 0!==s&&(o+=" [Option Value: "+s+"]"),r(o),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,n,s)},recordPurchase:function(e,t,n,s,r,o,i){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:s,currency:i}),window.ga("ecommerce:addItem",{id:e,name:t,sku:n,price:r,quantity:o}),window.ga("ecommerce:send")}},identifyUser:function(){i&&window._tkq.push(["identifyUser",i.ID,i.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},c=a},5932:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>p});var s=n(6439),r=n(3832);function o(e){class t extends Error{constructor(...t){super(...t),this.name=e}}return t}const i=o("JsonParseError"),a=o("JsonParseAfterRedirectError"),c=o("Api404Error"),d=o("Api404AfterRedirectError"),l=o("FetchNetworkError");const p=new function(e,t){let n=e,o=e,i={"X-WP-Nonce":t},a={credentials:"same-origin",headers:i},c={method:"post",credentials:"same-origin",headers:Object.assign({},i,{"Content-type":"application/json"})},d=function(e){const t=e.split("?"),n=t.length>1?t[1]:"",s=n.length?n.split("&"):[];return s.push("_cacheBuster="+(new Date).getTime()),t[0]+"?"+s.join("&")};const l={setApiRoot(e){n=e},setWpcomOriginApiUrl(e){o=e},setApiNonce(e){i={"X-WP-Nonce":e},a={credentials:"same-origin",headers:i},c={method:"post",credentials:"same-origin",headers:Object.assign({},i,{"Content-type":"application/json"})}},setCacheBusterCallback:e=>{d=e},registerSite:(e,t,r)=>{const o={};return(0,s.jetpackConfigHas)("consumer_slug")&&(o.plugin_slug=(0,s.jetpackConfigGet)("consumer_slug")),null!==t&&(o.redirect_uri=t),r&&(o.from=r),m(`${n}jetpack/v4/connection/register`,c,{body:JSON.stringify(o)}).then(h).then(u)},fetchAuthorizationUrl:e=>p((0,r.addQueryArgs)(`${n}jetpack/v4/connection/authorize_url`,{no_iframe:"1",redirect_uri:e}),a).then(h).then(u),fetchSiteConnectionData:()=>p(`${n}jetpack/v4/connection/data`,a).then(u),fetchSiteConnectionStatus:()=>p(`${n}jetpack/v4/connection`,a).then(u),fetchSiteConnectionTest:()=>p(`${n}jetpack/v4/connection/test`,a).then(h).then(u),fetchUserConnectionData:()=>p(`${n}jetpack/v4/connection/data`,a).then(u),fetchUserTrackingSettings:()=>p(`${n}jetpack/v4/tracking/settings`,a).then(h).then(u),updateUserTrackingSettings:e=>m(`${n}jetpack/v4/tracking/settings`,c,{body:JSON.stringify(e)}).then(h).then(u),disconnectSite:()=>m(`${n}jetpack/v4/connection`,c,{body:JSON.stringify({isActive:!1})}).then(h).then(u),fetchConnectUrl:()=>p(`${n}jetpack/v4/connection/url`,a).then(h).then(u),unlinkUser:(e=!1,t={})=>{const s={linked:!1,force:!!e};return t.disconnectAllUsers&&(s["disconnect-all-users"]=!0),m(`${n}jetpack/v4/connection/user`,c,{body:JSON.stringify(s)}).then(h).then(u)},reconnect:()=>m(`${n}jetpack/v4/connection/reconnect`,c).then(h).then(u),fetchConnectedPlugins:()=>p(`${n}jetpack/v4/connection/plugins`,a).then(h).then(u),setHasSeenWCConnectionModal:()=>m(`${n}jetpack/v4/seen-wc-connection-modal`,c).then(h).then(u),fetchModules:()=>p(`${n}jetpack/v4/module/all`,a).then(h).then(u),fetchModule:e=>p(`${n}jetpack/v4/module/${e}`,a).then(h).then(u),activateModule:e=>m(`${n}jetpack/v4/module/${e}/active`,c,{body:JSON.stringify({active:!0})}).then(h).then(u),deactivateModule:e=>m(`${n}jetpack/v4/module/${e}/active`,c,{body:JSON.stringify({active:!1})}),updateModuleOptions:(e,t)=>m(`${n}jetpack/v4/module/${e}`,c,{body:JSON.stringify(t)}).then(h).then(u),updateSettings:e=>m(`${n}jetpack/v4/settings`,c,{body:JSON.stringify(e)}).then(h).then(u),getProtectCount:()=>p(`${n}jetpack/v4/module/protect/data`,a).then(h).then(u),resetOptions:e=>m(`${n}jetpack/v4/options/${e}`,c,{body:JSON.stringify({reset:!0})}).then(h).then(u),activateVaultPress:()=>m(`${n}jetpack/v4/plugins`,c,{body:JSON.stringify({slug:"vaultpress",status:"active"})}).then(h).then(u),getVaultPressData:()=>p(`${n}jetpack/v4/module/vaultpress/data`,a).then(h).then(u),installPlugin:(e,t)=>{const s={slug:e,status:"active"};return t&&(s.source=t),m(`${n}jetpack/v4/plugins`,c,{body:JSON.stringify(s)}).then(h).then(u)},activateAkismet:()=>m(`${n}jetpack/v4/plugins`,c,{body:JSON.stringify({slug:"akismet",status:"active"})}).then(h).then(u),getAkismetData:()=>p(`${n}jetpack/v4/module/akismet/data`,a).then(h).then(u),checkAkismetKey:()=>p(`${n}jetpack/v4/module/akismet/key/check`,a).then(h).then(u),checkAkismetKeyTyped:e=>m(`${n}jetpack/v4/module/akismet/key/check`,c,{body:JSON.stringify({api_key:e})}).then(h).then(u),getFeatureTypeStatus:e=>p(`${n}jetpack/v4/feature/${e}`,a).then(h).then(u),fetchStatsData:e=>p(function(e){let t=`${n}jetpack/v4/module/stats/data`;-1!==t.indexOf("?")?t+=`&range=${encodeURIComponent(e)}`:t+=`?range=${encodeURIComponent(e)}`;return t}(e),a).then(h).then(u).then(f),getPluginUpdates:()=>p(`${n}jetpack/v4/updates/plugins`,a).then(h).then(u),getPlans:()=>p(`${n}jetpack/v4/plans`,a).then(h).then(u),fetchSettings:()=>p(`${n}jetpack/v4/settings`,a).then(h).then(u),updateSetting:e=>m(`${n}jetpack/v4/settings`,c,{body:JSON.stringify(e)}).then(h).then(u),fetchSiteData:()=>p(`${n}jetpack/v4/site`,a).then(h).then(u).then((e=>JSON.parse(e.data))),fetchSiteFeatures:()=>p(`${n}jetpack/v4/site/features`,a).then(h).then(u).then((e=>JSON.parse(e.data))),fetchSiteProducts:()=>p(`${n}jetpack/v4/site/products`,a).then(h).then(u),fetchSitePurchases:()=>p(`${n}jetpack/v4/site/purchases`,a).then(h).then(u).then((e=>JSON.parse(e.data))),fetchSiteBenefits:()=>p(`${n}jetpack/v4/site/benefits`,a).then(h).then(u).then((e=>JSON.parse(e.data))),fetchSiteDiscount:()=>p(`${n}jetpack/v4/site/discount`,a).then(h).then(u).then((e=>e.data)),fetchSetupQuestionnaire:()=>p(`${n}jetpack/v4/setup/questionnaire`,a).then(h).then(u),fetchRecommendationsData:()=>p(`${n}jetpack/v4/recommendations/data`,a).then(h).then(u),fetchRecommendationsProductSuggestions:()=>p(`${n}jetpack/v4/recommendations/product-suggestions`,a).then(h).then(u),fetchRecommendationsUpsell:()=>p(`${n}jetpack/v4/recommendations/upsell`,a).then(h).then(u),fetchRecommendationsConditional:()=>p(`${n}jetpack/v4/recommendations/conditional`,a).then(h).then(u),saveRecommendationsData:e=>m(`${n}jetpack/v4/recommendations/data`,c,{body:JSON.stringify({data:e})}).then(h),fetchProducts:()=>p(`${n}jetpack/v4/products`,a).then(h).then(u),fetchRewindStatus:()=>p(`${n}jetpack/v4/rewind`,a).then(h).then(u).then((e=>JSON.parse(e.data))),fetchScanStatus:()=>p(`${n}jetpack/v4/scan`,a).then(h).then(u).then((e=>JSON.parse(e.data))),dismissJetpackNotice:e=>m(`${n}jetpack/v4/notice/${e}`,c,{body:JSON.stringify({dismissed:!0})}).then(h).then(u),fetchPluginsData:()=>p(`${n}jetpack/v4/plugins`,a).then(h).then(u),fetchIntroOffers:()=>p(`${n}jetpack/v4/intro-offers`,a).then(h).then(u),fetchVerifySiteGoogleStatus:e=>p(null!==e?`${n}jetpack/v4/verify-site/google/${e}`:`${n}jetpack/v4/verify-site/google`,a).then(h).then(u),verifySiteGoogle:e=>m(`${n}jetpack/v4/verify-site/google`,c,{body:JSON.stringify({keyring_id:e})}).then(h).then(u),submitSurvey:e=>m(`${n}jetpack/v4/marketing/survey`,c,{body:JSON.stringify(e)}).then(h).then(u),saveSetupQuestionnaire:e=>m(`${n}jetpack/v4/setup/questionnaire`,c,{body:JSON.stringify(e)}).then(h).then(u),updateLicensingError:e=>m(`${n}jetpack/v4/licensing/error`,c,{body:JSON.stringify(e)}).then(h).then(u),updateLicenseKey:e=>m(`${n}jetpack/v4/licensing/set-license`,c,{body:JSON.stringify({license:e})}).then(h).then(u),getUserLicensesCounts:()=>p(`${n}jetpack/v4/licensing/user/counts`,a).then(h).then(u),getUserLicenses:()=>p(`${n}jetpack/v4/licensing/user/licenses`,a).then(h).then(u),updateLicensingActivationNoticeDismiss:e=>m(`${n}jetpack/v4/licensing/user/activation-notice-dismiss`,c,{body:JSON.stringify({last_detached_count:e})}).then(h).then(u),updateRecommendationsStep:e=>m(`${n}jetpack/v4/recommendations/step`,c,{body:JSON.stringify({step:e})}).then(h),confirmIDCSafeMode:()=>m(`${n}jetpack/v4/identity-crisis/confirm-safe-mode`,c).then(h),startIDCFresh:e=>m(`${n}jetpack/v4/identity-crisis/start-fresh`,c,{body:JSON.stringify({redirect_uri:e})}).then(h).then(u),migrateIDC:()=>m(`${n}jetpack/v4/identity-crisis/migrate`,c).then(h),attachLicenses:e=>m(`${n}jetpack/v4/licensing/attach-licenses`,c,{body:JSON.stringify({licenses:e})}).then(h).then(u),fetchSearchPlanInfo:()=>p(`${o}jetpack/v4/search/plan`,a).then(h).then(u),fetchSearchSettings:()=>p(`${o}jetpack/v4/search/settings`,a).then(h).then(u),updateSearchSettings:e=>m(`${o}jetpack/v4/search/settings`,c,{body:JSON.stringify(e)}).then(h).then(u),fetchSearchStats:()=>p(`${o}jetpack/v4/search/stats`,a).then(h).then(u),fetchWafSettings:()=>p(`${n}jetpack/v4/waf`,a).then(h).then(u),updateWafSettings:e=>m(`${n}jetpack/v4/waf`,c,{body:JSON.stringify(e)}).then(h).then(u),fetchWordAdsSettings:()=>p(`${n}jetpack/v4/wordads/settings`,a).then(h).then(u),updateWordAdsSettings:e=>m(`${n}jetpack/v4/wordads/settings`,c,{body:JSON.stringify(e)}),fetchSearchPricing:()=>p(`${o}jetpack/v4/search/pricing`,a).then(h).then(u),fetchMigrationStatus:()=>p(`${n}jetpack/v4/migration/status`,a).then(h).then(u),fetchBackupUndoEvent:()=>p(`${n}jetpack/v4/site/backup/undo-event`,a).then(h).then(u),fetchBackupPreflightStatus:()=>p(`${n}jetpack/v4/site/backup/preflight`,a).then(h).then(u)};function p(e,t){return fetch(d(e),t)}function m(e,t,n){return fetch(e,Object.assign({},t,n)).catch(g)}function f(e){return e.general&&void 0===e.general.response||e.week&&void 0===e.week.response||e.month&&void 0===e.month.response?e:{}}Object.assign(this,l)};function h(e){return e.status>=200&&e.status<300?e:404===e.status?new Promise((()=>{throw e.redirected?new d(e.redirected):new c})):e.json().catch((e=>m(e))).then((t=>{const n=new Error(`${t.message} (Status ${e.status})`);throw n.response=t,n.name="ApiError",n}))}function u(e){return e.json().catch((t=>m(t,e.redirected,e.url)))}function m(e,t,n){throw t?new a(n):new i}function g(){throw new l}},7142:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var s=n(7723),r=n(2231),o=n(790);const __=s.__,i=({logoColor:e="#069e08",showText:t=!0,className:n,height:s=32,...i})=>{const a=t?"0 0 118 32":"0 0 32 32";return(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:a,className:(0,r.A)("jetpack-logo",n),"aria-labelledby":"jetpack-logo-title",height:s,...i,role:"img",children:[(0,o.jsx)("title",{id:"jetpack-logo-title",children:__("Jetpack Logo","jetpack-connection")}),(0,o.jsx)("path",{fill:e,d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z M15,19H7l8-16V19z M17,29V13h8L17,29z"}),t&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("path",{d:"M41.3,26.6c-0.5-0.7-0.9-1.4-1.3-2.1c2.3-1.4,3-2.5,3-4.6V8h-3V6h6v13.4C46,22.8,45,24.8,41.3,26.6z"}),(0,o.jsx)("path",{d:"M65,18.4c0,1.1,0.8,1.3,1.4,1.3c0.5,0,2-0.2,2.6-0.4v2.1c-0.9,0.3-2.5,0.5-3.7,0.5c-1.5,0-3.2-0.5-3.2-3.1V12H60v-2h2.1V7.1 H65V10h4v2h-4V18.4z"}),(0,o.jsx)("path",{d:"M71,10h3v1.3c1.1-0.8,1.9-1.3,3.3-1.3c2.5,0,4.5,1.8,4.5,5.6s-2.2,6.3-5.8,6.3c-0.9,0-1.3-0.1-2-0.3V28h-3V10z M76.5,12.3 c-0.8,0-1.6,0.4-2.5,1.2v5.9c0.6,0.1,0.9,0.2,1.8,0.2c2,0,3.2-1.3,3.2-3.9C79,13.4,78.1,12.3,76.5,12.3z"}),(0,o.jsx)("path",{d:"M93,22h-3v-1.5c-0.9,0.7-1.9,1.5-3.5,1.5c-1.5,0-3.1-1.1-3.1-3.2c0-2.9,2.5-3.4,4.2-3.7l2.4-0.3v-0.3c0-1.5-0.5-2.3-2-2.3 c-0.7,0-2.3,0.5-3.7,1.1L84,11c1.2-0.4,3-1,4.4-1c2.7,0,4.6,1.4,4.6,4.7L93,22z M90,16.4l-2.2,0.4c-0.7,0.1-1.4,0.5-1.4,1.6 c0,0.9,0.5,1.4,1.3,1.4s1.5-0.5,2.3-1V16.4z"}),(0,o.jsx)("path",{d:"M104.5,21.3c-1.1,0.4-2.2,0.6-3.5,0.6c-4.2,0-5.9-2.4-5.9-5.9c0-3.7,2.3-6,6.1-6c1.4,0,2.3,0.2,3.2,0.5V13 c-0.8-0.3-2-0.6-3.2-0.6c-1.7,0-3.2,0.9-3.2,3.6c0,2.9,1.5,3.8,3.3,3.8c0.9,0,1.9-0.2,3.2-0.7V21.3z"}),(0,o.jsx)("path",{d:"M110,15.2c0.2-0.3,0.2-0.8,3.8-5.2h3.7l-4.6,5.7l5,6.3h-3.7l-4.2-5.8V22h-3V6h3V15.2z"}),(0,o.jsx)("path",{d:"M58.5,21.3c-1.5,0.5-2.7,0.6-4.2,0.6c-3.6,0-5.8-1.8-5.8-6c0-3.1,1.9-5.9,5.5-5.9s4.9,2.5,4.9,4.9c0,0.8,0,1.5-0.1,2h-7.3 c0.1,2.5,1.5,2.8,3.6,2.8c1.1,0,2.2-0.3,3.4-0.7C58.5,19,58.5,21.3,58.5,21.3z M56,15c0-1.4-0.5-2.9-2-2.9c-1.4,0-2.3,1.3-2.4,2.9 C51.6,15,56,15,56,15z"})]})]})}},6461:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var s=n(3619),r=n.n(s),o=(n(2144),n(790));const i=({color:e="#FFFFFF",className:t="",size:n=20})=>{const s=t+" jp-components-spinner",r={width:n,height:n,fontSize:n,borderTopColor:e},i={borderTopColor:e,borderRightColor:e};return(0,o.jsx)("div",{className:s,children:(0,o.jsx)("div",{className:"jp-components-spinner__outer",style:r,children:(0,o.jsx)("div",{className:"jp-components-spinner__inner",style:i})})})};i.propTypes={color:r().string,className:r().string,size:r().number};const a=i},3924:(e,t,n)=>{"use strict";function s(e,t={}){const n={};let s;if("undefined"!=typeof window&&(s=window?.JP_CONNECTION_INITIAL_STATE?.calypsoEnv),0===e.search("https://")){const t=new URL(e);e=`https://${t.host}${t.pathname}`,n.url=encodeURIComponent(e)}else n.source=encodeURIComponent(e);for(const e in t)n[e]=encodeURIComponent(t[e]);!Object.keys(n).includes("site")&&"undefined"!=typeof jetpack_redirects&&Object.hasOwn(jetpack_redirects,"currentSiteRawUrl")&&(n.site=jetpack_redirects.currentBlogID??jetpack_redirects.currentSiteRawUrl),s&&(n.calypso_env=s);return"https://jetpack.com/redirect/?"+Object.keys(n).map((e=>e+"="+n[e])).join("&")}n.d(t,{A:()=>s})},6439:(e,t,n)=>{let s={};try{s=n(9074)}catch{console.error("jetpackConfig is missing in your webpack config file. See @automattic/jetpack-config"),s={missingConfig:!0}}const r=e=>Object.hasOwn(s,e);e.exports={jetpackConfigHas:r,jetpackConfigGet:e=>{if(!r(e))throw'This app requires the "'+e+'" Jetpack Config to be defined in your webpack configuration file. See details in @automattic/jetpack-config package docs.';return s[e]}}},3685:(e,t,n)=>{"use strict";n.d(t,{A:()=>k});var s=n(3924),r=n(6461),o=n(6427),i=n(7143),a=n(6087),c=n(7723),d=n(3619),l=n.n(d),p=n(3207),h=n(1057),u=n(6043),m=n(6576),g=n(790);const __=c.__,f=(e,t)=>(0,g.jsxs)("div",{children:[(0,g.jsx)("div",{className:"jp-idc__idc-screen__card-action-sitename",children:e}),(0,g.jsx)(o.Dashicon,{icon:"minus",className:"jp-idc__idc-screen__card-action-separator"}),(0,g.jsx)("div",{className:"jp-idc__idc-screen__card-action-sitename",children:t})]}),j=e=>{const{isStartingFresh:t=!1,startFreshCallback:n=()=>{},customContent:d={},hasError:l=!1,isDevelopmentSite:h}=e,j=(0,u.A)(e.wpcomHomeUrl),k=(0,u.A)(e.currentUrl),y=(0,i.useSelect)((e=>e(p.a).getIsActionInProgress()),[]),v=d.startFreshButtonLabel||__("Create a fresh connection","jetpack-connection");return(0,g.jsxs)("div",{className:"jp-idc__idc-screen__card-action-base"+(l?" jp-idc__idc-screen__card-action-error":""),children:[(0,g.jsxs)("div",{className:"jp-idc__idc-screen__card-action-top",children:[(0,g.jsx)("h4",{children:d.startFreshCardTitle?(0,a.createInterpolateElement)(d.startFreshCardTitle,{em:(0,g.jsx)("em",{})}):__("Treat each site as independent sites","jetpack-connection")}),h?(0,g.jsx)("div",{className:"jp-idc__dev-mode-content",children:(0,a.createInterpolateElement)(d.startFreshCardBodyTextDev||(0,c.sprintf)(/* translators: %1$s: The current site domain name. %2$s: The original site domain name. */
__("<p><strong>Recommended for</strong></p><list><item>development sites</item><item>sites that need access to all Jetpack features</item></list><p><strong>Please note</strong> that creating a fresh connection for <hostname>%1$s</hostname> would require restoring the connection on <hostname>%2$s</hostname> if that site is cloned back to production. <safeModeLink>Learn more</safeModeLink>.</p>","jetpack-connection"),k,j),{p:(0,g.jsx)("p",{}),hostname:(0,g.jsx)("strong",{}),em:(0,g.jsx)("em",{}),strong:(0,g.jsx)("strong",{}),list:(0,g.jsx)("ul",{}),item:(0,g.jsx)("li",{}),safeModeLink:(0,g.jsx)("a",{href:d.supportURL||(0,s.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})})}):(0,g.jsx)("p",{children:(0,a.createInterpolateElement)(d.startFreshCardBodyText||(0,c.sprintf)(/* translators: %1$s: The current site domain name. %2$s: The original site domain name. */
__("<hostname>%1$s</hostname> settings, stats, and subscribers will start fresh. <hostname>%2$s</hostname> will keep its data as is.","jetpack-connection"),k,j),{hostname:(0,g.jsx)("strong",{}),em:(0,g.jsx)("em",{}),strong:(0,g.jsx)("strong",{})})})]}),(0,g.jsxs)("div",{className:"jp-idc__idc-screen__card-action-bottom",children:[(0,g.jsx)("div",{children:h?null:f(j,k)}),(0,g.jsx)(o.Button,{className:"jp-idc__idc-screen__card-action-button",label:v,onClick:n,disabled:y,children:t?(0,g.jsx)(r.A,{}):v}),l&&(_=d.supportURL,(0,g.jsx)(m.A,{children:(0,a.createInterpolateElement)(__("Could not create the connection. Retry or find out more <a>here</a>.","jetpack-connection"),{a:(0,g.jsx)("a",{href:_||(0,s.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})})}))]})]});var _};j.propTypes={wpcomHomeUrl:l().string.isRequired,currentUrl:l().string.isRequired,isStartingFresh:l().bool,startFreshCallback:l().func,customContent:l().shape(h.A),hasError:l().bool,isDevelopmentSite:l().bool};const k=j},6930:(e,t,n)=>{"use strict";n.d(t,{A:()=>j});var s=n(3924),r=n(6461),o=n(6427),i=n(7143),a=n(6087),c=n(7723),d=n(3619),l=n.n(d),p=n(3207),h=n(1057),u=n(6043),m=n(6576),g=n(790);const __=c.__,f=e=>{const t=(0,u.A)(e.wpcomHomeUrl),n=(0,u.A)(e.currentUrl),d=(0,i.useSelect)((e=>e(p.a).getIsActionInProgress()),[]),{isMigrating:l=!1,migrateCallback:h=()=>{},customContent:f={},hasError:j=!1}=e,k=f.migrateButtonLabel||__("Move your settings","jetpack-connection");return(0,g.jsxs)("div",{className:"jp-idc__idc-screen__card-action-base"+(j?" jp-idc__idc-screen__card-action-error":""),children:[(0,g.jsxs)("div",{className:"jp-idc__idc-screen__card-action-top",children:[(0,g.jsx)("h4",{children:f.migrateCardTitle?(0,a.createInterpolateElement)(f.migrateCardTitle,{em:(0,g.jsx)("em",{})}):__("Move Jetpack data","jetpack-connection")}),(0,g.jsx)("p",{children:(0,a.createInterpolateElement)(f.migrateCardBodyText||(0,c.sprintf)(/* translators: %1$s: The current site domain name. %2$s: The original site domain name. */
__("Move all your settings, stats and subscribers to your other URL, <hostname>%1$s</hostname>. <hostname>%2$s</hostname> will be disconnected from Jetpack.","jetpack-connection"),n,t),{hostname:(0,g.jsx)("strong",{}),em:(0,g.jsx)("em",{}),strong:(0,g.jsx)("strong",{})})})]}),(0,g.jsxs)("div",{className:"jp-idc__idc-screen__card-action-bottom",children:[(0,g.jsx)("div",{className:"jp-idc__idc-screen__card-action-sitename",children:t}),(0,g.jsx)(o.Dashicon,{icon:"arrow-down-alt",className:"jp-idc__idc-screen__card-action-separator"}),(0,g.jsx)("div",{className:"jp-idc__idc-screen__card-action-sitename",children:n}),(0,g.jsx)(o.Button,{className:"jp-idc__idc-screen__card-action-button",label:k,onClick:h,disabled:d,children:l?(0,g.jsx)(r.A,{}):k}),j&&(y=f.supportURL,(0,g.jsx)(m.A,{children:(0,a.createInterpolateElement)(__("Could not move your settings. Retry or find out more <a>here</a>.","jetpack-connection"),{a:(0,g.jsx)("a",{href:y||(0,s.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})})}))]})]});var y};f.propTypes={wpcomHomeUrl:l().string.isRequired,currentUrl:l().string.isRequired,isMigrating:l().bool,migrateCallback:l().func,customContent:l().shape(h.A),hasError:l().bool};const j=f},9882:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var s=n(790);const r=()=>(0,s.jsxs)("svg",{className:"error-gridicon",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",height:24,children:[(0,s.jsx)("rect",{x:"0",fill:"none",width:"24",height:"24"}),(0,s.jsx)("g",{children:(0,s.jsx)("path",{d:"M12 4c4.411 0 8 3.589 8 8s-3.589 8-8 8-8-3.589-8-8 3.589-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-2-2h2l.5-6h-3l.5 6z"})})]})},6576:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var s=n(9882),r=(n(2145),n(790));const o=e=>{const{children:t}=e;return(0,r.jsxs)("div",{className:"jp-idc__error-message",children:[(0,r.jsx)(s.A,{}),(0,r.jsx)("span",{children:t})]})}},7459:(e,t,n)=>{"use strict";n.d(t,{A:()=>j});var s=n(5932),r=n(7143),o=n(3619),i=n.n(o),a=n(1609),c=n(8101),d=n(5574),l=n(8502),p=n(3207),h=n(1057),u=n(2879),m=n(8979),g=n(790);const f=e=>{const{logo:t,customContent:n={},wpcomHomeUrl:o,currentUrl:i,apiNonce:h,apiRoot:f,redirectUri:j,tracksUserData:k,tracksEventData:y,isAdmin:v,possibleDynamicSiteUrlDetected:_,isDevelopmentSite:C}=e,[b,w]=(0,a.useState)(!1),S=(0,r.useSelect)((e=>e(p.a).getErrorType()),[]),{isMigrating:x,migrateCallback:A}=(0,c.A)((0,a.useCallback)((()=>{w(!0)}),[w])),{isStartingFresh:F,startFreshCallback:I}=(0,l.A)(j),{isFinishingMigration:E,finishMigrationCallback:U}=(0,d.A)();return(0,a.useEffect)((()=>{s.Ay.setApiRoot(f),s.Ay.setApiNonce(h),(0,u.f)(y,k),y&&(Object.hasOwn(y,"isAdmin")&&y.isAdmin?(0,u.A)("notice_view"):(0,u.A)("non_admin_notice_view",{page:!!Object.hasOwn(y,"currentScreen")&&y.currentScreen}))}),[f,h,k,y]),(0,g.jsx)(m.A,{logo:t,customContent:n,wpcomHomeUrl:o,currentUrl:i,redirectUri:j,isMigrating:x,migrateCallback:A,isMigrated:b,finishMigrationCallback:U,isFinishingMigration:E,isStartingFresh:F,startFreshCallback:I,isAdmin:v,hasStaySafeError:"safe-mode"===S,hasFreshError:"start-fresh"===S,hasMigrateError:"migrate"===S,possibleDynamicSiteUrlDetected:_,isDevelopmentSite:C})};f.propTypes={logo:i().oneOfType([i().string,i().object]),customContent:i().shape(h.A),wpcomHomeUrl:i().string.isRequired,currentUrl:i().string.isRequired,redirectUri:i().string.isRequired,apiRoot:i().string.isRequired,apiNonce:i().string.isRequired,tracksUserData:i().object,tracksEventData:i().object,isAdmin:i().bool.isRequired,possibleDynamicSiteUrlDetected:i().bool,isDevelopmentSite:i().bool};const j=f},1217:(e,t,n)=>{"use strict";n.d(t,{A:()=>f});var s=n(3924),r=n(6087),o=n(7723),i=n(3619),a=n.n(i),c=n(1609),d=n(1057),l=n(6043),p=n(3685),h=n(6930),u=n(1133),m=n(790);const __=o.__,g=e=>{const{wpcomHomeUrl:t,currentUrl:n,isMigrating:i=!1,migrateCallback:a,isStartingFresh:d=!1,startFreshCallback:g,customContent:f={},hasMigrateError:j=!1,hasFreshError:k=!1,hasStaySafeError:y=!1,possibleDynamicSiteUrlDetected:v=!1,isDevelopmentSite:_}=e,C=(0,l.A)(e.wpcomHomeUrl),b=(0,l.A)(e.currentUrl);return(0,m.jsxs)(c.Fragment,{children:[(0,m.jsx)("h2",{children:f.mainTitle?(0,r.createInterpolateElement)(f.mainTitle,{em:(0,m.jsx)("em",{})}):__("Safe Mode has been activated","jetpack-connection")}),(0,m.jsx)("p",{children:_?(0,r.createInterpolateElement)(f.mainBodyTextDev||(0,o.sprintf)(/* translators: %1$s: The current site domain name. %2$s: The original site domain name. */
__("<span>Your site is in Safe Mode because <hostname>%1$s</hostname> appears to be a staging or development copy of <hostname>%2$s</hostname>.</span>Two sites that are telling Jetpack they’re the same site. <safeModeLink>Learn more or troubleshoot common Safe mode issues</safeModeLink>.","jetpack-connection"),b,C),{span:(0,m.jsx)("span",{style:{display:"block"}}),hostname:(0,m.jsx)("strong",{}),em:(0,m.jsx)("em",{}),strong:(0,m.jsx)("strong",{}),safeModeLink:(0,m.jsx)("a",{href:f.supportURL||(0,s.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})}):(0,r.createInterpolateElement)(f.mainBodyText||__("Your site is in Safe Mode because you have 2 Jetpack-powered sites that appear to be duplicates. Two sites that are telling Jetpack they’re the same site. <safeModeLink>Learn more about safe mode.</safeModeLink>","jetpack-connection"),{safeModeLink:(0,m.jsx)("a",{href:f.supportURL||(0,s.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"}),em:(0,m.jsx)("em",{}),strong:(0,m.jsx)("strong",{})})}),v&&(0,m.jsx)("p",{children:(0,r.createInterpolateElement)(f.dynamicSiteUrlText||__("<strong>Notice:</strong> It appears that your 'wp-config.php' file might be using dynamic site URL values. Dynamic site URLs could cause Jetpack to enter Safe Mode. <dynamicSiteUrlSupportLink>Learn how to set a static site URL.</dynamicSiteUrlSupportLink>","jetpack-connection"),{dynamicSiteUrlSupportLink:(0,m.jsx)("a",{href:f.dynamicSiteUrlSupportLink||(0,s.A)("jetpack-idcscreen-dynamic-site-urls"),rel:"noopener noreferrer",target:"_blank"}),em:(0,m.jsx)("em",{}),strong:(0,m.jsx)("strong",{})})}),(0,m.jsx)("h3",{children:__("Please select an option","jetpack-connection")}),(0,m.jsx)("div",{className:"jp-idc__idc-screen__cards"+(j||k?" jp-idc__idc-screen__cards-error":""),children:_?(0,m.jsxs)(c.Fragment,{children:[(0,m.jsx)(p.A,{wpcomHomeUrl:t,currentUrl:n,isStartingFresh:d,startFreshCallback:g,customContent:f,hasError:k,isDevelopmentSite:_}),(0,m.jsx)("div",{className:"jp-idc__idc-screen__cards-separator",children:"or"}),(0,m.jsx)(u.A,{hasError:y,customContent:f,isDevelopmentSite:_})]}):(0,m.jsxs)(c.Fragment,{children:[(0,m.jsx)(h.A,{wpcomHomeUrl:t,currentUrl:n,isMigrating:i,migrateCallback:a,customContent:f,hasError:j}),(0,m.jsx)("div",{className:"jp-idc__idc-screen__cards-separator",children:"or"}),(0,m.jsx)(p.A,{wpcomHomeUrl:t,currentUrl:n,isStartingFresh:d,startFreshCallback:g,customContent:f,hasError:k,isDevelopmentSite:_})]})}),_?null:(0,m.jsx)(u.A,{hasError:y,customContent:f})]})};g.propTypes={wpcomHomeUrl:a().string.isRequired,currentUrl:a().string.isRequired,isMigrating:a().bool,migrateCallback:a().func,isStartingFresh:a().bool,startFreshCallback:a().func,customContent:a().shape(d.A),hasMigrateError:a().bool,hasFreshError:a().bool,hasStaySafeError:a().bool,possibleDynamicSiteUrlDetected:a().bool,isDevelopmentSite:a().bool};const f=g},4295:(e,t,n)=>{"use strict";n.d(t,{A:()=>m});var s=n(6461),r=n(6427),o=n(6087),i=n(7723),a=n(3619),c=n.n(a),d=n(1609),l=n(1057),p=n(6043),h=n(790);const __=i.__,u=e=>{const{finishCallback:t=()=>{},isFinishing:n=!1,customContent:a={}}=e,c=(0,p.A)(e.wpcomHomeUrl),l=(0,p.A)(e.currentUrl),u=__("Got it, thanks","jetpack-connection");return(0,h.jsxs)(d.Fragment,{children:[(0,h.jsx)("h2",{children:a.migratedTitle?(0,o.createInterpolateElement)(a.migratedTitle,{em:(0,h.jsx)("em",{})}):__("Your Jetpack settings have migrated successfully","jetpack-connection")}),(0,h.jsx)("p",{children:(0,o.createInterpolateElement)(a.migratedBodyText||(0,i.sprintf)(/* translators: %1$s: The current site domain name. */
__("Safe Mode has been switched off for <hostname>%1$s</hostname> website and Jetpack is fully functional.","jetpack-connection"),l),{hostname:(0,h.jsx)("strong",{}),em:(0,h.jsx)("em",{}),strong:(0,h.jsx)("strong",{})})}),(0,h.jsxs)("div",{className:"jp-idc__idc-screen__card-migrated",children:[(0,h.jsx)("div",{className:"jp-idc__idc-screen__card-migrated-hostname",children:c}),(0,h.jsx)(r.Dashicon,{icon:"arrow-down-alt",className:"jp-idc__idc-screen__card-migrated-separator"}),(0,h.jsx)(r.Dashicon,{icon:"arrow-right-alt",className:"jp-idc__idc-screen__card-migrated-separator-wide"}),(0,h.jsx)("div",{className:"jp-idc__idc-screen__card-migrated-hostname",children:l})]}),(0,h.jsx)(r.Button,{className:"jp-idc__idc-screen__card-action-button jp-idc__idc-screen__card-action-button-migrated",onClick:t,label:u,children:n?(0,h.jsx)(s.A,{}):u})]})};u.propTypes={wpcomHomeUrl:c().string.isRequired,currentUrl:c().string.isRequired,finishCallback:c().func,isFinishing:c().bool,customContent:c().shape(l.A)};const m=u},9291:(e,t,n)=>{"use strict";n.d(t,{A:()=>h});var s=n(3924),r=n(6087),o=n(7723),i=n(3619),a=n.n(i),c=n(1609),d=n(1057),l=n(790);const __=o.__,p=e=>{const{customContent:t={}}=e;return(0,l.jsxs)(c.Fragment,{children:[(0,l.jsx)("h2",{children:t.nonAdminTitle?(0,r.createInterpolateElement)(t.nonAdminTitle,{em:(0,l.jsx)("em",{})}):__("Safe Mode has been activated","jetpack-connection")}),(0,l.jsx)("p",{children:(0,r.createInterpolateElement)(t.nonAdminBodyText||__("This site is in Safe Mode because there are 2 Jetpack-powered sites that appear to be duplicates. 2 sites that are telling Jetpack they’re the same site. <safeModeLink>Learn more about safe mode.</safeModeLink>","jetpack-connection"),{safeModeLink:(0,l.jsx)("a",{href:t.supportURL||(0,s.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"}),em:(0,l.jsx)("em",{}),strong:(0,l.jsx)("strong",{})})}),t.nonAdminBodyText?"":(0,l.jsx)("p",{children:__("An administrator of this site can take Jetpack out of Safe Mode.","jetpack-connection")})]})};p.propTypes={customContent:a().shape(d.A)};const h=p},8979:(e,t,n)=>{"use strict";n.d(t,{A:()=>g});var s=n(7142),r=n(6087),o=n(7723),i=n(3619),a=n.n(i),c=n(1057),d=n(1217),l=n(4295),p=n(9291),h=(n(9958),n(790));const __=o.__,u=(e,t)=>"string"==typeof e||e instanceof String?(0,h.jsx)("img",{src:e,alt:t,className:"jp-idc__idc-screen__logo-image"}):e,m=e=>{const{logo:t=(0,h.jsx)(s.A,{height:24}),customContent:n={},wpcomHomeUrl:o,currentUrl:i,redirectUri:a,isMigrating:c=!1,migrateCallback:m,isMigrated:g=!1,finishMigrationCallback:f,isFinishingMigration:j=!1,isStartingFresh:k=!1,startFreshCallback:y,isAdmin:v,hasMigrateError:_=!1,hasFreshError:C=!1,hasStaySafeError:b=!1,possibleDynamicSiteUrlDetected:w=!1,isDevelopmentSite:S}=e,x=v?"":(0,h.jsx)(p.A,{customContent:n});let A="";return v&&(A=g?(0,h.jsx)(l.A,{wpcomHomeUrl:o,currentUrl:i,finishCallback:f,isFinishing:j,customContent:n}):(0,h.jsx)(d.A,{wpcomHomeUrl:o,currentUrl:i,redirectUri:a,customContent:n,isMigrating:c,migrateCallback:m,isStartingFresh:k,startFreshCallback:y,hasMigrateError:_,hasFreshError:C,hasStaySafeError:b,possibleDynamicSiteUrlDetected:w,isDevelopmentSite:S})),(0,h.jsxs)("div",{className:"jp-idc__idc-screen"+(g?" jp-idc__idc-screen__success":""),children:[(0,h.jsxs)("div",{className:"jp-idc__idc-screen__header",children:[(0,h.jsx)("div",{className:"jp-idc__idc-screen__logo",children:u(t,n.logoAlt||"")}),(0,h.jsx)("div",{className:"jp-idc__idc-screen__logo-label",children:n.headerText?(0,r.createInterpolateElement)(n.headerText,{em:(0,h.jsx)("em",{}),strong:(0,h.jsx)("strong",{})}):__("Safe Mode","jetpack-connection")})]}),x,A]})};m.propTypes={logo:a().oneOfType([a().string,a().object]),customContent:a().shape(c.A),wpcomHomeUrl:a().string.isRequired,currentUrl:a().string.isRequired,redirectUri:a().string.isRequired,isMigrating:a().bool,migrateCallback:a().func,isMigrated:a().bool,finishMigrationCallback:a().func,isFinishingMigration:a().bool,isStartingFresh:a().bool,startFreshCallback:a().func,isAdmin:a().bool.isRequired,hasMigrateError:a().bool,hasFreshError:a().bool,hasStaySafeError:a().bool,possibleDynamicSiteUrlDetected:a().bool,isDevelopmentSite:a().bool};const g=m},1133:(e,t,n)=>{"use strict";n.d(t,{A:()=>C});var s=n(5932),r=n(6461),o=n(3924),i=n(6427),a=n(9491),c=n(7143),d=n(6087),l=n(7723),p=n(3832),h=n(3619),u=n.n(h),m=n(1609),g=n(3207),f=n(1057),j=n(2879),k=n(6576),y=(n(9626),n(790));const __=l.__,v=e=>(0,y.jsx)(k.A,{children:(0,d.createInterpolateElement)(__("Could not stay in safe mode. Retry or find out more <a>here</a>.","jetpack-connection"),{a:(0,y.jsx)("a",{href:e||(0,o.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})})}),_=e=>{const{isActionInProgress:t,setIsActionInProgress:n,setErrorType:a,clearErrorType:c,hasError:l=!1,customContent:h,isDevelopmentSite:u}=e,[g,f]=(0,m.useState)(!1),k=h.stayInSafeModeButtonLabel||__("Stay in Safe mode","jetpack-connection"),_=(0,m.useCallback)((()=>{t||(f(!0),n(!0),c(),(0,j.A)("confirm_safe_mode"),s.Ay.confirmIDCSafeMode().then((()=>{window.location.href=(0,p.removeQueryArgs)(window.location.href,"jetpack_idc_clear_confirmation","_wpnonce")})).catch((e=>{throw n(!1),f(!1),a("safe-mode"),e})))}),[t,n,a,c]);return(0,y.jsx)(m.Fragment,{children:u?(0,y.jsxs)("div",{className:"jp-idc__idc-screen__card-action-base"+(l?" jp-idc__idc-screen__card-action-error":""),children:[(0,y.jsxs)("div",{className:"jp-idc__idc-screen__card-action-top",children:[(0,y.jsx)("h4",{children:h.safeModeTitle?(0,d.createInterpolateElement)(h.safeModeTitle,{em:(0,y.jsx)("em",{})}):__("Stay in Safe Mode","jetpack-connection")}),(0,y.jsx)("div",{children:(0,d.createInterpolateElement)(h.safeModeCardBodyText||/* translators: %1$s: The current site domain name. %2$s: The original site domain name. */
__("<p><strong>Recommended for</strong></p><list><item>short-lived test sites</item><item>sites that will be cloned back to production after testing</item></list><p><strong>Please note</strong> that staying in Safe mode will disable some Jetpack features, including security features such as SSO, firewall, and site monitor. <safeModeLink>Learn more</safeModeLink>.</p>","jetpack-connection"),{p:(0,y.jsx)("p",{}),hostname:(0,y.jsx)("strong",{}),em:(0,y.jsx)("em",{}),strong:(0,y.jsx)("strong",{}),list:(0,y.jsx)("ul",{}),item:(0,y.jsx)("li",{}),safeModeLink:(0,y.jsx)("a",{href:h.supportURL||(0,o.A)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})})})]}),(0,y.jsxs)("div",{className:"jp-idc__idc-screen__card-action-bottom",children:[(0,y.jsx)(i.Button,{className:"jp-idc__idc-screen__card-action-button-secondary",label:k,onClick:_,disabled:t,children:g?(0,y.jsx)(r.A,{color:"black"}):k}),l&&v(h.supportURL)]})]}):(0,y.jsxs)("div",{className:"jp-idc__safe-mode",children:[g?(0,y.jsxs)("div",{className:"jp-idc__safe-mode__staying-safe",children:[(0,y.jsx)(r.A,{color:"black"}),(0,y.jsx)("span",{children:__("Finishing setting up Safe mode…","jetpack-connection")})]}):(C=_,b=t,(0,d.createInterpolateElement)(__("Or decide later and stay in <button>Safe mode</button>","jetpack-connection"),{button:(0,y.jsx)(i.Button,{label:__("Safe mode","jetpack-connection"),variant:"link",onClick:C,disabled:b})})),l&&v(h.supportURL)]})});var C,b};_.propTypes={isActionInProgress:u().bool,setIsActionInProgress:u().func.isRequired,setErrorType:u().func.isRequired,clearErrorType:u().func.isRequired,hasError:u().bool,customContent:u().shape(f.A),isDevelopmentSite:u().bool};const C=(0,a.compose)([(0,c.withSelect)((e=>({isActionInProgress:e(g.a).getIsActionInProgress()}))),(0,c.withDispatch)((e=>({setIsActionInProgress:e(g.a).setIsActionInProgress,setErrorType:e(g.a).setErrorType,clearErrorType:e(g.a).clearErrorType})))])(_)},5574:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var s=n(1609);const r=()=>{const[e,t]=(0,s.useState)(!1),n=(0,s.useCallback)((()=>{e||(t(!0),window.location.reload())}),[e,t]);return{isFinishingMigration:e,finishMigrationCallback:n}}},8101:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var s=n(5932),r=n(7143),o=n(1609),i=n(3207),a=n(2879);const c=e=>{const[t,n]=(0,o.useState)(!1),c=(0,r.useSelect)((e=>e(i.a).getIsActionInProgress()),[]),{setIsActionInProgress:d,setErrorType:l,clearErrorType:p}=(0,r.useDispatch)(i.a);return{isMigrating:t,migrateCallback:(0,o.useCallback)((()=>{c||((0,a.A)("migrate"),d(!0),n(!0),p(),s.Ay.migrateIDC().then((()=>{n(!1),e&&"[object Function]"==={}.toString.call(e)&&e()})).catch((e=>{throw d(!1),n(!1),l("migrate"),e})))}),[n,e,c,d,l,p])}}},8502:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var s=n(5932),r=n(7143),o=n(1609),i=n(3207),a=n(2879);const c=e=>{const[t,n]=(0,o.useState)(!1),c=(0,r.useSelect)((e=>e(i.a).getIsActionInProgress()),[]),{setIsActionInProgress:d,setErrorType:l,clearErrorType:p}=(0,r.useDispatch)(i.a);return{isStartingFresh:t,startFreshCallback:(0,o.useCallback)((()=>{c||((0,a.A)("start_fresh"),d(!0),n(!0),p(),s.Ay.startIDCFresh(e).then((e=>{window.location.href=e+"&from=idc-notice"})).catch((e=>{throw d(!1),n(!1),l("start-fresh"),e})))}),[n,c,d,e,l,p])}}},8269:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>i,Xs:()=>s,sL:()=>o,xj:()=>r});const s="SET_IS_ACTION_IN_PROGRESS",r="SET_ERROR_TYPE",o="CLEAR_ERROR_TYPE",i={setIsActionInProgress:e=>({type:s,isInProgress:e}),setErrorType:e=>({type:r,errorType:e}),clearErrorType:()=>({type:o})}},2093:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var s=n(7143),r=n(8269);const o=(0,s.combineReducers)({isActionInProgress:(e=!1,t)=>t.type===r.Xs?t.isInProgress:e,errorType:(e=null,t)=>{switch(t.type){case r.xj:return t.errorType;case r.sL:return null}return e}})},8918:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});const s={getIsActionInProgress:e=>e.isActionInProgress||!1,getErrorType:e=>e.errorType||null}},1908:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var s=n(7143);class r{static store=null;static mayBeInit(e,t){null===r.store&&(r.store=(0,s.createReduxStore)(e,t),(0,s.register)(r.store))}}const o=r},3207:(e,t,n)=>{"use strict";n.d(t,{a:()=>a});var s=n(8269),r=n(2093),o=n(8918),i=n(1908);const a="jetpack-idc";i.A.mayBeInit(a,{reducer:r.A,actions:s.Ay,selectors:o.A})},1057:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var s=n(3619),r=n.n(s);const o={headerText:r().string,logoAlt:r().string,mainTitle:r().string,mainBodyText:r().string,mainBodyTextDev:r().string,migratedTitle:r().string,migratedBodyText:r().string,migrateCardTitle:r().string,migrateButtonLabel:r().string,migrateCardBodyText:r().string,startFreshCardTitle:r().string,startFreshCardBodyText:r().string,safeModeCardBodyText:r().string,startFreshCardBodyTextDev:r().string,startFreshButtonLabel:r().string,nonAdminTitle:r().string,nonAdminBodyText:r().string,supportURL:r().string,stayInSafeModeButtonLabel:r().string}},6043:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});const s=e=>/^https?:\/\//.test(e)?new URL(e).hostname:e.replace(/\/$/,"")},2879:(e,t,n)=>{"use strict";n.d(t,{A:()=>o,f:()=>r});var s=n(372);function r(e,t){t&&Object.hasOwn(t,"userid")&&Object.hasOwn(t,"username")&&s.A.initialize(t.userid,t.username),e&&(Object.hasOwn(e,"blogID")&&s.A.assignSuperProps({blog_id:e.blogID}),Object.hasOwn(e,"platform")&&s.A.assignSuperProps({platform:e.platform})),s.A.setMcAnalyticsEnabled(!0)}function o(e,t={}){void 0!==t&&"object"==typeof t||(t={}),e&&e.length&&void 0!==s.A&&s.A.tracks&&s.A.mc&&(e=0!==(e=e.replace(/-/g,"_")).indexOf("jetpack_idc_")?"jetpack_idc_"+e:e,s.A.tracks.recordEvent(e,t),e=(e=e.replace("jetpack_idc_","")).replace(/_/g,"-"),s.A.mc.bumpStat("jetpack-idc",e))}},9074:e=>{"use strict";e.exports={consumer_slug:"connection_package"}},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},6427:e=>{"use strict";e.exports=window.wp.components},9491:e=>{"use strict";e.exports=window.wp.compose},7143:e=>{"use strict";e.exports=window.wp.data},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},3832:e=>{"use strict";e.exports=window.wp.url},2231:(e,t,n)=>{"use strict";function s(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=s(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}n.d(t,{A:()=>r});const r=function(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=s(e))&&(r&&(r+=" "),r+=t);return r}}},t={};function n(s){var r=t[s];if(void 0!==r)return r.exports;var o=t[s]={exports:{}};return e[s](o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var s in t)n.o(t,s)&&!n.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=n(7459),t=n(6087),s=n(790);window.addEventListener("load",(()=>function(){if(!Object.hasOwn(window,"JP_IDENTITY_CRISIS__INITIAL_STATE"))return;const n=document.getElementById(window.JP_IDENTITY_CRISIS__INITIAL_STATE.containerID||"jp-identity-crisis-container");if(null===n)return;const{WP_API_root:r,WP_API_nonce:o,wpcomHomeUrl:i,currentUrl:a,redirectUri:c,tracksUserData:d,tracksEventData:l,isSafeModeConfirmed:p,consumerData:h,isAdmin:u,possibleDynamicSiteUrlDetected:m,isDevelopmentSite:g}=window.JP_IDENTITY_CRISIS__INITIAL_STATE;if(!p){const p=(0,s.jsx)(e.A,{wpcomHomeUrl:i,currentUrl:a,apiRoot:r,apiNonce:o,redirectUri:c,tracksUserData:d||{},tracksEventData:l,customContent:Object.hasOwn(h,"customContent")?h.customContent:{},isAdmin:u,logo:Object.hasOwn(h,"logo")?h.logo:void 0,possibleDynamicSiteUrlDetected:m,isDevelopmentSite:g});t.createRoot(n).render(p)}}()))})()})();