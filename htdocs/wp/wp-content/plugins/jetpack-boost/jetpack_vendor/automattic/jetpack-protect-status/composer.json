{"name": "automattic/jetpack-protect-status", "description": "This package contains the Protect Status API functionality to retrieve a site's scan status (WordPress, Themes, and Plugins threats).", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"php": ">=7.2", "automattic/jetpack-connection": "^6.15.0", "automattic/jetpack-plugins-installer": "^0.5.5", "automattic/jetpack-sync": "^4.15.2", "automattic/jetpack-protect-models": "^0.6.0", "automattic/jetpack-plans": "^0.9.0"}, "require-dev": {"yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3"}, "autoload": {"classmap": ["src/"]}, "scripts": {"build-development": "echo 'Add your build step to composer.j<PERSON>, please!'", "build-production": "echo 'Add your build step to composer.j<PERSON>, please!'", "phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": ["php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\""], "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "config": {"allow-plugins": {"roots/wordpress-core-installer": true}}, "extra": {"autotagger": true, "branch-alias": {"dev-trunk": "0.6.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-protect-status/compare/v${old}...v${new}"}, "mirror-repo": "Automattic/jetpack-protect-status", "textdomain": "jetpack-protect-status", "version-constants": {"::PACKAGE_VERSION": "src/class-status.php"}}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}}