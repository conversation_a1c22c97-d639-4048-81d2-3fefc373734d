{"name": "automattic/jetpack-image-cdn", "description": "Serve images through Jetpack's powerful CDN", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"php": ">=7.2", "automattic/jetpack-assets": "^4.2.0", "automattic/jetpack-status": "^6.0.0"}, "require-dev": {"automattic/jetpack-test-environment": "@dev", "yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": ["php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\""], "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"mirror-repo": "Automattic/jetpack-image-cdn", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-image-cdn/compare/v${old}...v${new}"}, "autotagger": true, "branch-alias": {"dev-trunk": "0.7.x-dev"}, "textdomain": "jetpack-image-cdn", "version-constants": {"::PACKAGE_VERSION": "src/class-image-cdn.php"}}, "config": {"allow-plugins": {"roots/wordpress-core-installer": true}}}