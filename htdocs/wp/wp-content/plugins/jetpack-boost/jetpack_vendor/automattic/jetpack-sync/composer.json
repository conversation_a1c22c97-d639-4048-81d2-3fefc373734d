{"name": "automattic/jetpack-sync", "description": "Everything needed to allow syncing to the WP.com infrastructure.", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"php": ">=7.2", "automattic/jetpack-connection": "^6.15.0", "automattic/jetpack-constants": "^3.0.8", "automattic/jetpack-password-checker": "^0.4.8", "automattic/jetpack-ip": "^0.4.9", "automattic/jetpack-roles": "^3.0.8", "automattic/jetpack-status": "^6.0.0"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-search": "@dev", "automattic/jetpack-waf": "^0.27.1", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": ["php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\""], "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-sync", "textdomain": "jetpack-sync", "version-constants": {"::PACKAGE_VERSION": "src/class-package-version.php"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-sync/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "4.15.x-dev"}, "dependencies": {"test-only": ["packages/search", "packages/waf"]}}, "config": {"allow-plugins": {"roots/wordpress-core-installer": true}}}