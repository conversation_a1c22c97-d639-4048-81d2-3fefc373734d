{"name": "automattic/jetpack-my-jetpack", "description": "WP Admin page with information and configuration shared among all Jetpack stand-alone plugins", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"php": ">=7.2", "automattic/jetpack-admin-ui": "^0.5.10", "automattic/jetpack-assets": "^4.3.0", "automattic/jetpack-boost-speed-score": "^0.4.10", "automattic/jetpack-connection": "^6.16.0", "automattic/jetpack-explat": "^0.3.4", "automattic/jetpack-jitm": "^4.3.0", "automattic/jetpack-licensing": "^3.0.9", "automattic/jetpack-plugins-installer": "^0.5.6", "automattic/jetpack-redirect": "^3.0.8", "automattic/jetpack-constants": "^3.0.8", "automattic/jetpack-plans": "^0.9.0", "automattic/jetpack-status": "^6.0.0", "automattic/jetpack-sync": "^4.15.2", "automattic/jetpack-protect-status": "^0.6.1"}, "require-dev": {"yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/", "src/products"]}, "scripts": {"phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": "pnpm concurrently --names php,js 'php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\"' 'pnpm:test-coverage'", "test-php": ["@composer phpunit"], "test-js": ["pnpm run test"], "test-js-watch": ["Composer\\Config::disableProcessTimeout", "pnpm run test --watch"], "build-development": ["pnpm run build"], "build-production": ["NODE_ENV=production pnpm run build"], "watch": ["Composer\\Config::disableProcessTimeout", "pnpm run watch"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-my-jetpack", "textdomain": "jetpack-my-jetpack", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-my-jetpack/compare/${old}...${new}"}, "branch-alias": {"dev-trunk": "5.20.x-dev"}, "version-constants": {"::PACKAGE_VERSION": "src/class-initializer.php"}, "dependencies": {"test-only": ["packages/search", "packages/videopress"]}}, "config": {"allow-plugins": {"roots/wordpress-core-installer": true}}}