{"name": "automattic/jetpack-plans", "description": "Fetch information about Jetpack Plans from wpcom", "type": "library", "license": "GPL-2.0-or-later", "require": {"php": ">=7.2", "automattic/jetpack-connection": "^6.15.0"}, "require-dev": {"yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-status": "^6.0.0", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": ["php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\""], "test-php": ["@composer phpunit"], "build-production": "echo 'Add your build step to composer.j<PERSON>, please!'", "build-development": "echo 'Add your build step to composer.j<PERSON>, please!'"}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-plans", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-plans/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "0.9.x-dev"}}, "config": {"allow-plugins": {"roots/wordpress-core-installer": true}}}