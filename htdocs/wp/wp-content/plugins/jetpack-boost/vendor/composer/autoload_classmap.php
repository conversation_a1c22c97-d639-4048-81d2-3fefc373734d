<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Automattic\\Jetpack\\A8c_Mc_Stats' => $baseDir . '/jetpack_vendor/automattic/jetpack-a8c-mc-stats/src/class-a8c-mc-stats.php',
    'Automattic\\Jetpack\\Admin_UI\\Admin_Menu' => $baseDir . '/jetpack_vendor/automattic/jetpack-admin-ui/src/class-admin-menu.php',
    'Automattic\\Jetpack\\Assets' => $baseDir . '/jetpack_vendor/automattic/jetpack-assets/src/class-assets.php',
    'Automattic\\Jetpack\\Assets\\Logo' => $baseDir . '/jetpack_vendor/automattic/jetpack-logo/src/class-logo.php',
    'Automattic\\Jetpack\\Assets\\Script_Data' => $baseDir . '/jetpack_vendor/automattic/jetpack-assets/src/class-script-data.php',
    'Automattic\\Jetpack\\Assets\\Semver' => $baseDir . '/jetpack_vendor/automattic/jetpack-assets/src/class-semver.php',
    'Automattic\\Jetpack\\Autoloader\\AutoloadFileWriter' => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadFileWriter.php',
    'Automattic\\Jetpack\\Autoloader\\AutoloadGenerator' => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadGenerator.php',
    'Automattic\\Jetpack\\Autoloader\\AutoloadProcessor' => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadProcessor.php',
    'Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin' => $vendorDir . '/automattic/jetpack-autoloader/src/CustomAutoloaderPlugin.php',
    'Automattic\\Jetpack\\Autoloader\\ManifestGenerator' => $vendorDir . '/automattic/jetpack-autoloader/src/ManifestGenerator.php',
    'Automattic\\Jetpack\\Automatic_Install_Skin' => $baseDir . '/jetpack_vendor/automattic/jetpack-plugins-installer/src/class-automatic-install-skin.php',
    'Automattic\\Jetpack\\Boost\\App\\Contracts\\Is_Dev_Feature' => $baseDir . '/app/contracts/interface-is-dev-feature.php',
    'Automattic\\Jetpack\\Boost_Core\\Contracts\\Boost_API_Client' => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/contracts/boost-api-client.php',
    'Automattic\\Jetpack\\Boost_Core\\Lib\\Boost_API' => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-boost-api.php',
    'Automattic\\Jetpack\\Boost_Core\\Lib\\Cacheable' => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-cacheable.php',
    'Automattic\\Jetpack\\Boost_Core\\Lib\\Transient' => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-transient.php',
    'Automattic\\Jetpack\\Boost_Core\\Lib\\Url' => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-url.php',
    'Automattic\\Jetpack\\Boost_Core\\Lib\\Utils' => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-utils.php',
    'Automattic\\Jetpack\\Boost_Core\\Lib\\WPCOM_Boost_API_Client' => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-wpcom-boost-api-client.php',
    'Automattic\\Jetpack\\Boost_Speed_Score\\Jetpack_Boost_Modules' => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-jetpack-boost-modules.php',
    'Automattic\\Jetpack\\Boost_Speed_Score\\Speed_Score' => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-speed-score.php',
    'Automattic\\Jetpack\\Boost_Speed_Score\\Speed_Score_Graph_History_Request' => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-speed-score-graph-history-request.php',
    'Automattic\\Jetpack\\Boost_Speed_Score\\Speed_Score_History' => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-speed-score-history.php',
    'Automattic\\Jetpack\\Boost_Speed_Score\\Speed_Score_Request' => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-speed-score-request.php',
    'Automattic\\Jetpack\\Composer\\Manager' => $vendorDir . '/automattic/jetpack-composer-plugin/src/class-manager.php',
    'Automattic\\Jetpack\\Composer\\Plugin' => $vendorDir . '/automattic/jetpack-composer-plugin/src/class-plugin.php',
    'Automattic\\Jetpack\\Config' => $baseDir . '/jetpack_vendor/automattic/jetpack-config/src/class-config.php',
    'Automattic\\Jetpack\\Connection\\Authorize_Json_Api' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-authorize-json-api.php',
    'Automattic\\Jetpack\\Connection\\Client' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-client.php',
    'Automattic\\Jetpack\\Connection\\Connection_Assets' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-connection-assets.php',
    'Automattic\\Jetpack\\Connection\\Connection_Notice' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-connection-notice.php',
    'Automattic\\Jetpack\\Connection\\Error_Handler' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-error-handler.php',
    'Automattic\\Jetpack\\Connection\\Initial_State' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-initial-state.php',
    'Automattic\\Jetpack\\Connection\\Manager' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-manager.php',
    'Automattic\\Jetpack\\Connection\\Manager_Interface' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/interface-manager.php',
    'Automattic\\Jetpack\\Connection\\Nonce_Handler' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-nonce-handler.php',
    'Automattic\\Jetpack\\Connection\\Package_Version' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-package-version.php',
    'Automattic\\Jetpack\\Connection\\Package_Version_Tracker' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-package-version-tracker.php',
    'Automattic\\Jetpack\\Connection\\Plugin' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-plugin.php',
    'Automattic\\Jetpack\\Connection\\Plugin_Storage' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-plugin-storage.php',
    'Automattic\\Jetpack\\Connection\\REST_Connector' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-rest-connector.php',
    'Automattic\\Jetpack\\Connection\\Rest_Authentication' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-rest-authentication.php',
    'Automattic\\Jetpack\\Connection\\SSO' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-sso.php',
    'Automattic\\Jetpack\\Connection\\SSO\\Force_2FA' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-force-2fa.php',
    'Automattic\\Jetpack\\Connection\\SSO\\Helpers' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-helpers.php',
    'Automattic\\Jetpack\\Connection\\SSO\\Notices' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-notices.php',
    'Automattic\\Jetpack\\Connection\\SSO\\User_Admin' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-user-admin.php',
    'Automattic\\Jetpack\\Connection\\Secrets' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-secrets.php',
    'Automattic\\Jetpack\\Connection\\Server_Sandbox' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-server-sandbox.php',
    'Automattic\\Jetpack\\Connection\\Tokens' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-tokens.php',
    'Automattic\\Jetpack\\Connection\\Tokens_Locks' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-tokens-locks.php',
    'Automattic\\Jetpack\\Connection\\Traits\\WPCOM_REST_API_Proxy_Request' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/traits/trait-wpcom-rest-api-proxy-request.php',
    'Automattic\\Jetpack\\Connection\\Urls' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-urls.php',
    'Automattic\\Jetpack\\Connection\\User_Account_Status' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-user-account-status.php',
    'Automattic\\Jetpack\\Connection\\Users_Connection_Admin' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-users-connection-admin.php',
    'Automattic\\Jetpack\\Connection\\Utils' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-utils.php',
    'Automattic\\Jetpack\\Connection\\Webhooks' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-webhooks.php',
    'Automattic\\Jetpack\\Connection\\Webhooks\\Authorize_Redirect' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/webhooks/class-authorize-redirect.php',
    'Automattic\\Jetpack\\Connection\\XMLRPC_Async_Call' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-xmlrpc-async-call.php',
    'Automattic\\Jetpack\\Connection\\XMLRPC_Connector' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-xmlrpc-connector.php',
    'Automattic\\Jetpack\\Constants' => $baseDir . '/jetpack_vendor/automattic/jetpack-constants/src/class-constants.php',
    'Automattic\\Jetpack\\CookieState' => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-cookiestate.php',
    'Automattic\\Jetpack\\Current_Plan' => $vendorDir . '/automattic/jetpack-plans/src/class-current-plan.php',
    'Automattic\\Jetpack\\Device_Detection' => $baseDir . '/jetpack_vendor/automattic/jetpack-device-detection/src/class-device-detection.php',
    'Automattic\\Jetpack\\Device_Detection\\User_Agent_Info' => $baseDir . '/jetpack_vendor/automattic/jetpack-device-detection/src/class-user-agent-info.php',
    'Automattic\\Jetpack\\Errors' => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-errors.php',
    'Automattic\\Jetpack\\ExPlat' => $baseDir . '/jetpack_vendor/automattic/jetpack-explat/src/class-explat.php',
    'Automattic\\Jetpack\\ExPlat\\REST_Controller' => $baseDir . '/jetpack_vendor/automattic/jetpack-explat/src/class-rest-controller.php',
    'Automattic\\Jetpack\\Files' => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-files.php',
    'Automattic\\Jetpack\\Heartbeat' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-heartbeat.php',
    'Automattic\\Jetpack\\IP\\Utils' => $baseDir . '/jetpack_vendor/automattic/jetpack-ip/src/class-utils.php',
    'Automattic\\Jetpack\\IdentityCrisis\\Exception' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-exception.php',
    'Automattic\\Jetpack\\IdentityCrisis\\REST_Endpoints' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-rest-endpoints.php',
    'Automattic\\Jetpack\\IdentityCrisis\\UI' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-ui.php',
    'Automattic\\Jetpack\\IdentityCrisis\\URL_Secret' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-url-secret.php',
    'Automattic\\Jetpack\\Identity_Crisis' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-identity-crisis.php',
    'Automattic\\Jetpack\\Image_CDN\\Image_CDN' => $baseDir . '/jetpack_vendor/automattic/jetpack-image-cdn/src/class-image-cdn.php',
    'Automattic\\Jetpack\\Image_CDN\\Image_CDN_Core' => $baseDir . '/jetpack_vendor/automattic/jetpack-image-cdn/src/class-image-cdn-core.php',
    'Automattic\\Jetpack\\Image_CDN\\Image_CDN_Image' => $baseDir . '/jetpack_vendor/automattic/jetpack-image-cdn/src/class-image-cdn-image.php',
    'Automattic\\Jetpack\\Image_CDN\\Image_CDN_Image_Sizes' => $baseDir . '/jetpack_vendor/automattic/jetpack-image-cdn/src/class-image-cdn-image-sizes.php',
    'Automattic\\Jetpack\\Image_CDN\\Image_CDN_Setup' => $baseDir . '/jetpack_vendor/automattic/jetpack-image-cdn/src/class-image-cdn-setup.php',
    'Automattic\\Jetpack\\JITMS\\JITM' => $baseDir . '/jetpack_vendor/automattic/jetpack-jitm/src/class-jitm.php',
    'Automattic\\Jetpack\\JITMS\\Post_Connection_JITM' => $baseDir . '/jetpack_vendor/automattic/jetpack-jitm/src/class-post-connection-jitm.php',
    'Automattic\\Jetpack\\JITMS\\Pre_Connection_JITM' => $baseDir . '/jetpack_vendor/automattic/jetpack-jitm/src/class-pre-connection-jitm.php',
    'Automattic\\Jetpack\\JITMS\\Rest_Api_Endpoints' => $baseDir . '/jetpack_vendor/automattic/jetpack-jitm/src/class-rest-api-endpoints.php',
    'Automattic\\Jetpack\\Licensing' => $baseDir . '/jetpack_vendor/automattic/jetpack-licensing/src/class-licensing.php',
    'Automattic\\Jetpack\\Licensing\\Endpoints' => $baseDir . '/jetpack_vendor/automattic/jetpack-licensing/src/class-endpoints.php',
    'Automattic\\Jetpack\\Modules' => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-modules.php',
    'Automattic\\Jetpack\\My_Jetpack\\Activitylog' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-activitylog.php',
    'Automattic\\Jetpack\\My_Jetpack\\Historically_Active_Modules' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-historically-active-modules.php',
    'Automattic\\Jetpack\\My_Jetpack\\Hybrid_Product' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-hybrid-product.php',
    'Automattic\\Jetpack\\My_Jetpack\\Initializer' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-initializer.php',
    'Automattic\\Jetpack\\My_Jetpack\\Jetpack_Manage' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-jetpack-manage.php',
    'Automattic\\Jetpack\\My_Jetpack\\Module_Product' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-module-product.php',
    'Automattic\\Jetpack\\My_Jetpack\\Product' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-product.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-products.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Anti_Spam' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-anti-spam.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Backup' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-backup.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Boost' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-boost.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Complete' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-complete.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Creator' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-creator.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Crm' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-crm.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Extras' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-extras.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Growth' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-growth.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Jetpack_Ai' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-jetpack-ai.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Newsletter' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-newsletter.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Protect' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-protect.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Related_Posts' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-related-posts.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Scan' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-scan.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Search' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-search.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Search_Stats' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-search-stats.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Security' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-security.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Site_Accelerator' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-site-accelerator.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Social' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-social.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Starter' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-starter.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Stats' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-stats.php',
    'Automattic\\Jetpack\\My_Jetpack\\Products\\Videopress' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-videopress.php',
    'Automattic\\Jetpack\\My_Jetpack\\REST_AI' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-ai.php',
    'Automattic\\Jetpack\\My_Jetpack\\REST_Products' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-products.php',
    'Automattic\\Jetpack\\My_Jetpack\\REST_Purchases' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-purchases.php',
    'Automattic\\Jetpack\\My_Jetpack\\REST_Recommendations_Evaluation' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-recommendations-evaluation.php',
    'Automattic\\Jetpack\\My_Jetpack\\REST_Zendesk_Chat' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-zendesk-chat.php',
    'Automattic\\Jetpack\\My_Jetpack\\Red_Bubble_Notifications' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-red-bubble-notifications.php',
    'Automattic\\Jetpack\\My_Jetpack\\Wpcom_Products' => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-wpcom-products.php',
    'Automattic\\Jetpack\\Partner' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-partner.php',
    'Automattic\\Jetpack\\Partner_Coupon' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-partner-coupon.php',
    'Automattic\\Jetpack\\Password_Checker' => $baseDir . '/jetpack_vendor/automattic/jetpack-password-checker/src/class-password-checker.php',
    'Automattic\\Jetpack\\Paths' => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-paths.php',
    'Automattic\\Jetpack\\Plans' => $vendorDir . '/automattic/jetpack-plans/src/class-plans.php',
    'Automattic\\Jetpack\\Plugin_Deactivation\\Deactivation_Handler' => $baseDir . '/jetpack_vendor/automattic/jetpack-plugin-deactivation/src/class-deactivation-handler.php',
    'Automattic\\Jetpack\\Plugins_Installer' => $baseDir . '/jetpack_vendor/automattic/jetpack-plugins-installer/src/class-plugins-installer.php',
    'Automattic\\Jetpack\\Protect_Models' => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-protect-models.php',
    'Automattic\\Jetpack\\Protect_Models\\Extension_Model' => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-extension-model.php',
    'Automattic\\Jetpack\\Protect_Models\\History_Model' => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-history-model.php',
    'Automattic\\Jetpack\\Protect_Models\\Status_Model' => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-status-model.php',
    'Automattic\\Jetpack\\Protect_Models\\Threat_Model' => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-threat-model.php',
    'Automattic\\Jetpack\\Protect_Models\\Vulnerability_Model' => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-vulnerability-model.php',
    'Automattic\\Jetpack\\Protect_Status\\Plan' => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-plan.php',
    'Automattic\\Jetpack\\Protect_Status\\Protect_Status' => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-protect-status.php',
    'Automattic\\Jetpack\\Protect_Status\\REST_Controller' => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-rest-controller.php',
    'Automattic\\Jetpack\\Protect_Status\\Scan_Status' => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-scan-status.php',
    'Automattic\\Jetpack\\Protect_Status\\Status' => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-status.php',
    'Automattic\\Jetpack\\Redirect' => $baseDir . '/jetpack_vendor/automattic/jetpack-redirect/src/class-redirect.php',
    'Automattic\\Jetpack\\Roles' => $baseDir . '/jetpack_vendor/automattic/jetpack-roles/src/class-roles.php',
    'Automattic\\Jetpack\\Schema\\Modifiers\\Modifier_Fallback' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/modifiers/class-modifier-fallback.php',
    'Automattic\\Jetpack\\Schema\\Parser' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/interface-parser.php',
    'Automattic\\Jetpack\\Schema\\Schema' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/class-schema.php',
    'Automattic\\Jetpack\\Schema\\Schema_Context' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/class-schema-context.php',
    'Automattic\\Jetpack\\Schema\\Schema_Error' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/class-schema-error.php',
    'Automattic\\Jetpack\\Schema\\Schema_Parser' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/class-schema-parser.php',
    'Automattic\\Jetpack\\Schema\\Types\\Type_Any' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-any.php',
    'Automattic\\Jetpack\\Schema\\Types\\Type_Any_JSON' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-any-json.php',
    'Automattic\\Jetpack\\Schema\\Types\\Type_Array' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-array.php',
    'Automattic\\Jetpack\\Schema\\Types\\Type_Assoc_Array' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-assoc-array.php',
    'Automattic\\Jetpack\\Schema\\Types\\Type_Boolean' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-boolean.php',
    'Automattic\\Jetpack\\Schema\\Types\\Type_Enum' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-enum.php',
    'Automattic\\Jetpack\\Schema\\Types\\Type_Float' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-float.php',
    'Automattic\\Jetpack\\Schema\\Types\\Type_Literal' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-literal.php',
    'Automattic\\Jetpack\\Schema\\Types\\Type_Number' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-number.php',
    'Automattic\\Jetpack\\Schema\\Types\\Type_String' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-string.php',
    'Automattic\\Jetpack\\Schema\\Types\\Type_Void' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-void.php',
    'Automattic\\Jetpack\\Schema\\Utils' => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/class-utils.php',
    'Automattic\\Jetpack\\Status' => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-status.php',
    'Automattic\\Jetpack\\Status\\Cache' => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-cache.php',
    'Automattic\\Jetpack\\Status\\Host' => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-host.php',
    'Automattic\\Jetpack\\Status\\Request' => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-request.php',
    'Automattic\\Jetpack\\Status\\Visitor' => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-visitor.php',
    'Automattic\\Jetpack\\Sync\\Actions' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-actions.php',
    'Automattic\\Jetpack\\Sync\\Codec_Interface' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/interface-codec.php',
    'Automattic\\Jetpack\\Sync\\Data_Settings' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-data-settings.php',
    'Automattic\\Jetpack\\Sync\\Dedicated_Sender' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-dedicated-sender.php',
    'Automattic\\Jetpack\\Sync\\Default_Filter_Settings' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-default-filter-settings.php',
    'Automattic\\Jetpack\\Sync\\Defaults' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-defaults.php',
    'Automattic\\Jetpack\\Sync\\Functions' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-functions.php',
    'Automattic\\Jetpack\\Sync\\Health' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-health.php',
    'Automattic\\Jetpack\\Sync\\JSON_Deflate_Array_Codec' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-json-deflate-array-codec.php',
    'Automattic\\Jetpack\\Sync\\Listener' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-listener.php',
    'Automattic\\Jetpack\\Sync\\Lock' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-lock.php',
    'Automattic\\Jetpack\\Sync\\Main' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-main.php',
    'Automattic\\Jetpack\\Sync\\Modules' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-modules.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Attachments' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-attachments.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Callables' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-callables.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Comments' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-comments.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Constants' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-constants.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Full_Sync' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-full-sync.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Full_Sync_Immediately' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-full-sync-immediately.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Import' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-import.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Menus' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-menus.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Meta' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-meta.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Module' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-module.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Network_Options' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-network-options.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Options' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-options.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Plugins' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-plugins.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Posts' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-posts.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Protect' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-protect.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Search' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-search.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Stats' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-stats.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Term_Relationships' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-term-relationships.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Terms' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-terms.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Themes' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-themes.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Updates' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-updates.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Users' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-users.php',
    'Automattic\\Jetpack\\Sync\\Modules\\WP_Super_Cache' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-wp-super-cache.php',
    'Automattic\\Jetpack\\Sync\\Modules\\WooCommerce' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-woocommerce.php',
    'Automattic\\Jetpack\\Sync\\Modules\\WooCommerce_HPOS_Orders' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-woocommerce-hpos-orders.php',
    'Automattic\\Jetpack\\Sync\\Package_Version' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-package-version.php',
    'Automattic\\Jetpack\\Sync\\Queue' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-queue.php',
    'Automattic\\Jetpack\\Sync\\Queue\\Queue_Storage_Options' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/sync-queue/class-queue-storage-options.php',
    'Automattic\\Jetpack\\Sync\\Queue\\Queue_Storage_Table' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/sync-queue/class-queue-storage-table.php',
    'Automattic\\Jetpack\\Sync\\Queue_Buffer' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-queue-buffer.php',
    'Automattic\\Jetpack\\Sync\\REST_Endpoints' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-rest-endpoints.php',
    'Automattic\\Jetpack\\Sync\\REST_Sender' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-rest-sender.php',
    'Automattic\\Jetpack\\Sync\\Replicastore' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-replicastore.php',
    'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/replicastore/class-table-checksum.php',
    'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum_Usermeta' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/replicastore/class-table-checksum-usermeta.php',
    'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum_Users' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/replicastore/class-table-checksum-users.php',
    'Automattic\\Jetpack\\Sync\\Replicastore_Interface' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/interface-replicastore.php',
    'Automattic\\Jetpack\\Sync\\Sender' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-sender.php',
    'Automattic\\Jetpack\\Sync\\Server' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-server.php',
    'Automattic\\Jetpack\\Sync\\Settings' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-settings.php',
    'Automattic\\Jetpack\\Sync\\Simple_Codec' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-simple-codec.php',
    'Automattic\\Jetpack\\Sync\\Users' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-users.php',
    'Automattic\\Jetpack\\Sync\\Utils' => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-utils.php',
    'Automattic\\Jetpack\\Terms_Of_Service' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-terms-of-service.php',
    'Automattic\\Jetpack\\Tracking' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-tracking.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Data_Sync_Action' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-data-sync-action.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Data_Sync_Entry' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-data-sync-entry.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Entry_Can_Delete' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-entry-can-delete.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Entry_Can_Get' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-entry-can-get.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Entry_Can_Merge' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-entry-can-merge.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Entry_Can_Set' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-entry-can-set.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Entry_Has_Custom_Endpoints' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-entry-custom-endpoint.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Lazy_Entry' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-lazy-entry.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\DS_Utils' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/class-ds-utils.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Data_Sync' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/class-data-sync.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Data_Sync_Entry_Adapter' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/class-data-sync-entry-adapter.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Data_Sync_Option' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/class-data-sync-option.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Data_Sync_Readonly' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/class-data-sync-readonly.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Endpoints\\Action_Endpoint' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/endpoints/class-action-endpoint.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Endpoints\\Authenticated_Nonce' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/endpoints/class-authenticated-nonce.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Endpoints\\Endpoint' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/endpoints/class-endpoint.php',
    'Automattic\\Jetpack\\WP_JS_Data_Sync\\Registry' => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/class-registry.php',
    'Automattic\\Jetpack_Boost\\Admin\\Admin' => $baseDir . '/app/admin/class-admin.php',
    'Automattic\\Jetpack_Boost\\Admin\\Config' => $baseDir . '/app/admin/class-config.php',
    'Automattic\\Jetpack_Boost\\Admin\\Regenerate_Admin_Notice' => $baseDir . '/app/admin/class-regenerate-admin-notice.php',
    'Automattic\\Jetpack_Boost\\Contracts\\Changes_Output_After_Activation' => $baseDir . '/app/contracts/interface-changes-output-after-activation.php',
    'Automattic\\Jetpack_Boost\\Contracts\\Changes_Output_On_Activation' => $baseDir . '/app/contracts/interface-changes-output-on-activation.php',
    'Automattic\\Jetpack_Boost\\Contracts\\Feature' => $baseDir . '/app/contracts/interface-feature.php',
    'Automattic\\Jetpack_Boost\\Contracts\\Has_Activate' => $baseDir . '/app/contracts/interface-has-activate.php',
    'Automattic\\Jetpack_Boost\\Contracts\\Has_Data_Sync' => $baseDir . '/app/contracts/interface-has-data-sync.php',
    'Automattic\\Jetpack_Boost\\Contracts\\Has_Deactivate' => $baseDir . '/app/contracts/interface-has-deactivate.php',
    'Automattic\\Jetpack_Boost\\Contracts\\Has_Setup' => $baseDir . '/app/contracts/interface-has-setup.php',
    'Automattic\\Jetpack_Boost\\Contracts\\Has_Slug' => $baseDir . '/app/contracts/interface-has-slug.php',
    'Automattic\\Jetpack_Boost\\Contracts\\Is_Always_On' => $baseDir . '/app/contracts/interface-is-always-on.php',
    'Automattic\\Jetpack_Boost\\Contracts\\Needs_To_Be_Ready' => $baseDir . '/app/contracts/interface-needs-to-be-ready.php',
    'Automattic\\Jetpack_Boost\\Contracts\\Needs_Website_To_Be_Public' => $baseDir . '/app/contracts/interface-needs-website-to-be-public.php',
    'Automattic\\Jetpack_Boost\\Contracts\\Optimization' => $baseDir . '/app/contracts/interface-optimization.php',
    'Automattic\\Jetpack_Boost\\Contracts\\Sub_Feature' => $baseDir . '/app/contracts/interface-sub-feature.php',
    'Automattic\\Jetpack_Boost\\Data_Sync\\Cornerstone_Pages_Entry' => $baseDir . '/app/data-sync/class-cornerstone-pages-entry.php',
    'Automattic\\Jetpack_Boost\\Data_Sync\\Critical_CSS_Meta_Entry' => $baseDir . '/app/data-sync/class-critical-css-meta-entry.php',
    'Automattic\\Jetpack_Boost\\Data_Sync\\Getting_Started_Entry' => $baseDir . '/app/data-sync/class-getting-started-entry.php',
    'Automattic\\Jetpack_Boost\\Data_Sync\\Mergeable_Array_Entry' => $baseDir . '/app/data-sync/class-mergeable-array-entry.php',
    'Automattic\\Jetpack_Boost\\Data_Sync\\Minify_Excludes_State_Entry' => $baseDir . '/app/data-sync/class-minify-excludes-state-entry.php',
    'Automattic\\Jetpack_Boost\\Data_Sync\\Modules_State_Entry' => $baseDir . '/app/data-sync/class-modules-state-entry.php',
    'Automattic\\Jetpack_Boost\\Data_Sync\\Performance_History_Entry' => $baseDir . '/app/data-sync/class-performance-history-entry.php',
    'Automattic\\Jetpack_Boost\\Jetpack_Boost' => $baseDir . '/app/class-jetpack-boost.php',
    'Automattic\\Jetpack_Boost\\Lib\\Analytics' => $baseDir . '/app/lib/class-analytics.php',
    'Automattic\\Jetpack_Boost\\Lib\\Assets' => $baseDir . '/app/lib/class-assets.php',
    'Automattic\\Jetpack_Boost\\Lib\\Boost_Health' => $baseDir . '/app/lib/class-boost-health.php',
    'Automattic\\Jetpack_Boost\\Lib\\CLI' => $baseDir . '/app/lib/class-cli.php',
    'Automattic\\Jetpack_Boost\\Lib\\Cache_Compatibility' => $baseDir . '/app/lib/class-cache-compatibility.php',
    'Automattic\\Jetpack_Boost\\Lib\\Collection' => $baseDir . '/app/lib/class-collection.php',
    'Automattic\\Jetpack_Boost\\Lib\\Connection' => $baseDir . '/app/lib/class-connection.php',
    'Automattic\\Jetpack_Boost\\Lib\\Cornerstone\\Cornerstone_Pages' => $baseDir . '/app/lib/cornerstone/class-cornerstone-pages.php',
    'Automattic\\Jetpack_Boost\\Lib\\Cornerstone\\Cornerstone_Utils' => $baseDir . '/app/lib/cornerstone/class-cornerstone-utils.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Admin_Bar_Compatibility' => $baseDir . '/app/lib/critical-css/class-admin-bar-compatibility.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Critical_CSS_Invalidator' => $baseDir . '/app/lib/critical-css/class-critical-css-invalidator.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Critical_CSS_State' => $baseDir . '/app/lib/critical-css/class-critical-css-state.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Critical_CSS_Storage' => $baseDir . '/app/lib/critical-css/class-critical-css-storage.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Data_Sync\\Data_Sync_Schema' => $baseDir . '/app/lib/critical-css/data-sync/class-data-sync-schema.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Data_Sync_Actions\\Regenerate_CSS' => $baseDir . '/app/lib/critical-css/data-sync-actions/class-regenerate-css.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Data_Sync_Actions\\Set_Provider_CSS' => $baseDir . '/app/lib/critical-css/data-sync-actions/class-set-provider-css.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Data_Sync_Actions\\Set_Provider_Error_Dismissed' => $baseDir . '/app/lib/critical-css/data-sync-actions/class-set-provider-error-dismissed.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Data_Sync_Actions\\Set_Provider_Errors' => $baseDir . '/app/lib/critical-css/data-sync-actions/class-set-provider-errors.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Display_Critical_CSS' => $baseDir . '/app/lib/critical-css/class-display-critical-css.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Generator' => $baseDir . '/app/lib/critical-css/class-generator.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Regenerate' => $baseDir . '/app/lib/critical-css/class-regenerate.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Providers\\Archive_Provider' => $baseDir . '/app/lib/critical-css/source-providers/providers/class-archive-provider.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Providers\\Cornerstone_Provider' => $baseDir . '/app/lib/critical-css/source-providers/providers/class-cornerstone-provider.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Providers\\Post_ID_Provider' => $baseDir . '/app/lib/critical-css/source-providers/providers/class-post-id-provider.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Providers\\Provider' => $baseDir . '/app/lib/critical-css/source-providers/providers/class-provider.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Providers\\Singular_Post_Provider' => $baseDir . '/app/lib/critical-css/source-providers/providers/class-singular-post-provider.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Providers\\Taxonomy_Provider' => $baseDir . '/app/lib/critical-css/source-providers/providers/class-taxonomy-provider.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Providers\\WP_Core_Provider' => $baseDir . '/app/lib/critical-css/source-providers/providers/class-wp-core-provider.php',
    'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Source_Providers' => $baseDir . '/app/lib/critical-css/source-providers/class-source-providers.php',
    'Automattic\\Jetpack_Boost\\Lib\\Debug' => $baseDir . '/app/lib/class-debug.php',
    'Automattic\\Jetpack_Boost\\Lib\\Environment_Change_Detector' => $baseDir . '/app/lib/class-environment-change-detector.php',
    'Automattic\\Jetpack_Boost\\Lib\\Minify' => $baseDir . '/app/lib/class-minify.php',
    'Automattic\\Jetpack_Boost\\Lib\\Minify\\Cleanup_Stored_Paths' => $baseDir . '/app/lib/minify/class-cleanup-stored-paths.php',
    'Automattic\\Jetpack_Boost\\Lib\\Minify\\Concatenate_CSS' => $baseDir . '/app/lib/minify/class-concatenate-css.php',
    'Automattic\\Jetpack_Boost\\Lib\\Minify\\Concatenate_JS' => $baseDir . '/app/lib/minify/class-concatenate-js.php',
    'Automattic\\Jetpack_Boost\\Lib\\Minify\\Config' => $baseDir . '/app/lib/minify/class-config.php',
    'Automattic\\Jetpack_Boost\\Lib\\Minify\\Dependency_Path_Mapping' => $baseDir . '/app/lib/minify/class-dependency-path-mapping.php',
    'Automattic\\Jetpack_Boost\\Lib\\Minify\\File_Paths' => $baseDir . '/app/lib/minify/class-file-paths.php',
    'Automattic\\Jetpack_Boost\\Lib\\Minify\\Utils' => $baseDir . '/app/lib/minify/class-utils.php',
    'Automattic\\Jetpack_Boost\\Lib\\My_Jetpack' => $baseDir . '/app/lib/class-my-jetpack.php',
    'Automattic\\Jetpack_Boost\\Lib\\Nonce' => $baseDir . '/app/lib/class-nonce.php',
    'Automattic\\Jetpack_Boost\\Lib\\Output_Filter' => $baseDir . '/app/lib/class-output-filter.php',
    'Automattic\\Jetpack_Boost\\Lib\\Premium_Features' => $baseDir . '/app/lib/class-premium-features.php',
    'Automattic\\Jetpack_Boost\\Lib\\Premium_Pricing' => $baseDir . '/app/lib/class-premium-pricing.php',
    'Automattic\\Jetpack_Boost\\Lib\\Setup' => $baseDir . '/app/lib/class-setup.php',
    'Automattic\\Jetpack_Boost\\Lib\\Site_Health' => $baseDir . '/app/lib/class-site-health.php',
    'Automattic\\Jetpack_Boost\\Lib\\Site_Urls' => $baseDir . '/app/lib/class-site-urls.php',
    'Automattic\\Jetpack_Boost\\Lib\\Status' => $baseDir . '/app/lib/class-status.php',
    'Automattic\\Jetpack_Boost\\Lib\\Storage_Post_Type' => $baseDir . '/app/lib/class-storage-post-type.php',
    'Automattic\\Jetpack_Boost\\Lib\\Super_Cache_Config_Compatibility' => $baseDir . '/app/lib/class-super-cache-config-compatibility.php',
    'Automattic\\Jetpack_Boost\\Lib\\Super_Cache_Tracking' => $baseDir . '/app/lib/class-super-cache-tracking.php',
    'Automattic\\Jetpack_Boost\\Modules\\Features_Index' => $baseDir . '/app/modules/class-features-index.php',
    'Automattic\\Jetpack_Boost\\Modules\\Image_Guide\\Image_Guide' => $baseDir . '/app/modules/image-guide/class-image-guide.php',
    'Automattic\\Jetpack_Boost\\Modules\\Image_Guide\\Image_Guide_Proxy' => $baseDir . '/app/modules/image-guide/class-image-guide-proxy.php',
    'Automattic\\Jetpack_Boost\\Modules\\Image_Size_Analysis\\Data_Sync\\Data_Sync_Schema' => $baseDir . '/app/modules/image-size-analysis/data-sync/class-data-sync-schema.php',
    'Automattic\\Jetpack_Boost\\Modules\\Image_Size_Analysis\\Data_Sync\\Image_Size_Analysis_Entry' => $baseDir . '/app/modules/image-size-analysis/data-sync/class-image-size-analysis-entry.php',
    'Automattic\\Jetpack_Boost\\Modules\\Image_Size_Analysis\\Data_Sync\\Image_Size_Analysis_Summary' => $baseDir . '/app/modules/image-size-analysis/data-sync/class-image-size-analysis-summary.php',
    'Automattic\\Jetpack_Boost\\Modules\\Image_Size_Analysis\\Data_Sync\\Image_Size_Analysis_UI_State' => $baseDir . '/app/modules/image-size-analysis/data-sync/class-image-size-analysis-ui-state.php',
    'Automattic\\Jetpack_Boost\\Modules\\Image_Size_Analysis\\Image_Size_Analysis' => $baseDir . '/app/modules/image-size-analysis/class-image-size-analysis.php',
    'Automattic\\Jetpack_Boost\\Modules\\Image_Size_Analysis\\Image_Size_Analysis_Fixer' => $baseDir . '/app/modules/image-size-analysis/class-image-size-analysis-fixer.php',
    'Automattic\\Jetpack_Boost\\Modules\\Module' => $baseDir . '/app/modules/class-module.php',
    'Automattic\\Jetpack_Boost\\Modules\\Modules_Setup' => $baseDir . '/app/modules/class-modules-setup.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Cloud_CSS\\Cloud_CSS' => $baseDir . '/app/modules/optimizations/cloud-css/class-cloud-css.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Cloud_CSS\\Cloud_CSS_Followup' => $baseDir . '/app/modules/optimizations/cloud-css/class-cloud-css-followup.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Critical_CSS\\CSS_Proxy' => $baseDir . '/app/modules/optimizations/critical-css/class-css-proxy.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Critical_CSS\\Critical_CSS' => $baseDir . '/app/modules/optimizations/critical-css/class-critical-css.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Image_CDN\\Image_CDN' => $baseDir . '/app/modules/optimizations/image-cdn/class-image-cdn.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Image_CDN\\Liar' => $baseDir . '/app/modules/optimizations/image-cdn/class-liar.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Image_CDN\\Quality_Settings' => $baseDir . '/app/modules/optimizations/image-cdn/class-quality-settings.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_Analyzer' => $baseDir . '/app/modules/optimizations/lcp/class-lcp-analyzer.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_Invalidator' => $baseDir . '/app/modules/optimizations/lcp/class-lcp-invalidator.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_Optimization_Util' => $baseDir . '/app/modules/optimizations/lcp/class-lcp-optimization-util.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_Optimize_Bg_Image' => $baseDir . '/app/modules/optimizations/lcp/class-lcp-optimize-bg-image.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_Optimize_Img_Tag' => $baseDir . '/app/modules/optimizations/lcp/class-lcp-optimize-img-tag.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_State' => $baseDir . '/app/modules/optimizations/lcp/class-lcp-state.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_Storage' => $baseDir . '/app/modules/optimizations/lcp/class-lcp-storage.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_Utils' => $baseDir . '/app/modules/optimizations/lcp/class-lcp-utils.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\Lcp' => $baseDir . '/app/modules/optimizations/lcp/class-lcp.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\Optimize_LCP_Endpoint' => $baseDir . '/app/modules/optimizations/lcp/class-optimize-lcp-endpoint.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Minify\\Minify_CSS' => $baseDir . '/app/modules/optimizations/minify/class-minify-css.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Minify\\Minify_Common' => $baseDir . '/app/modules/optimizations/minify/class-minify-common.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Minify\\Minify_JS' => $baseDir . '/app/modules/optimizations/minify/class-minify-js.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Cache_Preload' => $baseDir . '/app/modules/optimizations/page-cache/class-cache-preload.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Data_Sync\\Page_Cache_Entry' => $baseDir . '/app/modules/optimizations/page-cache/data-sync/class-page-cache-entry.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Data_Sync_Actions\\Clear_Page_Cache' => $baseDir . '/app/modules/optimizations/page-cache/data-sync-actions/class-clear-page-cache.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Data_Sync_Actions\\Deactivate_WPSC' => $baseDir . '/app/modules/optimizations/page-cache/data-sync-actions/class-deactivate-wpsc.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Data_Sync_Actions\\Run_Setup' => $baseDir . '/app/modules/optimizations/page-cache/data-sync-actions/class-run-setup.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Garbage_Collection' => $baseDir . '/app/modules/optimizations/page-cache/class-garbage-collection.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Page_Cache' => $baseDir . '/app/modules/optimizations/page-cache/class-page-cache.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Page_Cache_Setup' => $baseDir . '/app/modules/optimizations/page-cache/class-page-cache-setup.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Boost_Cache' => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/class-boost-cache.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Boost_Cache_Error' => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/class-boost-cache-error.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Boost_Cache_Settings' => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/class-boost-cache-settings.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Boost_Cache_Utils' => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/class-boost-cache-utils.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Filesystem_Utils' => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/class-filesystem-utils.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Logger' => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/class-logger.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Path_Actions\\Filter_Older' => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/path-actions/class-filter-older.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Path_Actions\\Path_Action' => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/path-actions/interface-path-action.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Path_Actions\\Rebuild_File' => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/path-actions/class-rebuild-file.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Path_Actions\\Simple_Delete' => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/path-actions/class-simple-delete.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Request' => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/class-request.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Storage\\File_Storage' => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/storage/class-file-storage.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Storage\\Storage' => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/storage/interface-storage.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Render_Blocking_JS\\Render_Blocking_JS' => $baseDir . '/app/modules/optimizations/render-blocking-js/class-render-blocking-js.php',
    'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Speculation_Rules\\Speculation_Rules' => $baseDir . '/app/modules/optimizations/speculation-rules/class-speculation-rules.php',
    'Automattic\\Jetpack_Boost\\Modules\\Performance_History\\Performance_History' => $baseDir . '/app/modules/performance-history/class-performance-history.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Contracts\\Endpoint' => $baseDir . '/app/rest-api/contracts/interface-endpoint.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Contracts\\Has_Always_Available_Endpoints' => $baseDir . '/app/rest-api/contracts/interface-has-always-available-endpoints.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Contracts\\Has_Endpoints' => $baseDir . '/app/rest-api/contracts/interface-has-endpoints.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Contracts\\Permission' => $baseDir . '/app/rest-api/contracts/interface-permission.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\Image_Analysis_Action_Fix' => $baseDir . '/app/modules/image-size-analysis/data-sync/class-image-analysis-action-fix.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\Image_Size_Analysis_Summary_Action_Paginate' => $baseDir . '/app/modules/image-size-analysis/data-sync/class-image-size-analysis-summary-action-paginate.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\Image_Size_Analysis_Summary_Action_Start' => $baseDir . '/app/modules/image-size-analysis/data-sync/class-image-size-analysis-summary-action-start.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\List_Cornerstone_Pages' => $baseDir . '/app/rest-api/endpoints/class-list-cornerstone-pages.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\List_LCP_Analysis' => $baseDir . '/app/rest-api/endpoints/class-list-lcp-analysis.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\List_Site_Urls' => $baseDir . '/app/rest-api/endpoints/class-list-site-urls.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\List_Source_Providers' => $baseDir . '/app/rest-api/endpoints/class-list-source-providers.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\Update_Cloud_CSS' => $baseDir . '/app/rest-api/endpoints/class-update-cloud-css.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\Update_LCP' => $baseDir . '/app/rest-api/endpoints/class-update-lcp.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Permissions\\Current_User_Admin' => $baseDir . '/app/rest-api/permissions/class-current-user-admin.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Permissions\\Nonce' => $baseDir . '/app/rest-api/permissions/class-nonce.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Permissions\\Signed_With_Blog_Token' => $baseDir . '/app/rest-api/permissions/class-signed-with-blog-token.php',
    'Automattic\\Jetpack_Boost\\REST_API\\REST_API' => $baseDir . '/app/rest-api/class-rest-api.php',
    'Automattic\\Jetpack_Boost\\REST_API\\Route' => $baseDir . '/app/rest-api/class-route.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Jetpack_IXR_Client' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-ixr-client.php',
    'Jetpack_IXR_ClientMulticall' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-ixr-clientmulticall.php',
    'Jetpack_Options' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-options.php',
    'Jetpack_Signature' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-signature.php',
    'Jetpack_Tracks_Client' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-tracks-client.php',
    'Jetpack_Tracks_Event' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-tracks-event.php',
    'Jetpack_XMLRPC_Server' => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-xmlrpc-server.php',
    'MatthiasMullie\\Minify\\CSS' => $vendorDir . '/matthiasmullie/minify/src/CSS.php',
    'MatthiasMullie\\Minify\\Exception' => $vendorDir . '/matthiasmullie/minify/src/Exception.php',
    'MatthiasMullie\\Minify\\Exceptions\\BasicException' => $vendorDir . '/matthiasmullie/minify/src/Exceptions/BasicException.php',
    'MatthiasMullie\\Minify\\Exceptions\\FileImportException' => $vendorDir . '/matthiasmullie/minify/src/Exceptions/FileImportException.php',
    'MatthiasMullie\\Minify\\Exceptions\\IOException' => $vendorDir . '/matthiasmullie/minify/src/Exceptions/IOException.php',
    'MatthiasMullie\\Minify\\Exceptions\\PatternMatchException' => $vendorDir . '/matthiasmullie/minify/src/Exceptions/PatternMatchException.php',
    'MatthiasMullie\\Minify\\JS' => $vendorDir . '/matthiasmullie/minify/src/JS.php',
    'MatthiasMullie\\Minify\\Minify' => $vendorDir . '/matthiasmullie/minify/src/Minify.php',
    'MatthiasMullie\\PathConverter\\Converter' => $vendorDir . '/matthiasmullie/path-converter/src/Converter.php',
    'MatthiasMullie\\PathConverter\\ConverterInterface' => $vendorDir . '/matthiasmullie/path-converter/src/ConverterInterface.php',
    'MatthiasMullie\\PathConverter\\NoConverter' => $vendorDir . '/matthiasmullie/path-converter/src/NoConverter.php',
    'WP_Speculation_Rules' => $baseDir . '/app/modules/optimizations/speculation-rules/class-wp-speculation-rules.php',
);
