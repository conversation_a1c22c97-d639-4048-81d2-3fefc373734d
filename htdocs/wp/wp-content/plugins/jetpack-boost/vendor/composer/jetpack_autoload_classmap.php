<?php

// This file `jetpack_autoload_classmap.php` was auto generated by automattic/jetpack-autoloader.

$vendorDir = dirname(__DIR__);
$baseDir   = dirname($vendorDir);

return array(
	'Autoloader' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader.php'
	),
	'Autoloader_Handler' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader-handler.php'
	),
	'Autoloader_Locator' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader-locator.php'
	),
	'Automattic\\Jetpack\\A8c_Mc_Stats' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-a8c-mc-stats/src/class-a8c-mc-stats.php'
	),
	'Automattic\\Jetpack\\Admin_UI\\Admin_Menu' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-admin-ui/src/class-admin-menu.php'
	),
	'Automattic\\Jetpack\\Assets' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-assets/src/class-assets.php'
	),
	'Automattic\\Jetpack\\Assets\\Logo' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-logo/src/class-logo.php'
	),
	'Automattic\\Jetpack\\Assets\\Script_Data' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-assets/src/class-script-data.php'
	),
	'Automattic\\Jetpack\\Assets\\Semver' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-assets/src/class-semver.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadFileWriter' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadFileWriter.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadGenerator' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadGenerator.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadProcessor' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadProcessor.php'
	),
	'Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/CustomAutoloaderPlugin.php'
	),
	'Automattic\\Jetpack\\Autoloader\\ManifestGenerator' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/ManifestGenerator.php'
	),
	'Automattic\\Jetpack\\Automatic_Install_Skin' => array(
		'version' => '0.5.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-plugins-installer/src/class-automatic-install-skin.php'
	),
	'Automattic\\Jetpack\\Boost\\App\\Contracts\\Is_Dev_Feature' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/contracts/interface-is-dev-feature.php'
	),
	'Automattic\\Jetpack\\Boost_Core\\Contracts\\Boost_API_Client' => array(
		'version' => '0.3.11.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/contracts/boost-api-client.php'
	),
	'Automattic\\Jetpack\\Boost_Core\\Lib\\Boost_API' => array(
		'version' => '0.3.11.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-boost-api.php'
	),
	'Automattic\\Jetpack\\Boost_Core\\Lib\\Cacheable' => array(
		'version' => '0.3.11.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-cacheable.php'
	),
	'Automattic\\Jetpack\\Boost_Core\\Lib\\Transient' => array(
		'version' => '0.3.11.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-transient.php'
	),
	'Automattic\\Jetpack\\Boost_Core\\Lib\\Url' => array(
		'version' => '0.3.11.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-url.php'
	),
	'Automattic\\Jetpack\\Boost_Core\\Lib\\Utils' => array(
		'version' => '0.3.11.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-utils.php'
	),
	'Automattic\\Jetpack\\Boost_Core\\Lib\\WPCOM_Boost_API_Client' => array(
		'version' => '0.3.11.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-wpcom-boost-api-client.php'
	),
	'Automattic\\Jetpack\\Boost_Speed_Score\\Jetpack_Boost_Modules' => array(
		'version' => '0.4.10.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-jetpack-boost-modules.php'
	),
	'Automattic\\Jetpack\\Boost_Speed_Score\\Speed_Score' => array(
		'version' => '0.4.10.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-speed-score.php'
	),
	'Automattic\\Jetpack\\Boost_Speed_Score\\Speed_Score_Graph_History_Request' => array(
		'version' => '0.4.10.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-speed-score-graph-history-request.php'
	),
	'Automattic\\Jetpack\\Boost_Speed_Score\\Speed_Score_History' => array(
		'version' => '0.4.10.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-speed-score-history.php'
	),
	'Automattic\\Jetpack\\Boost_Speed_Score\\Speed_Score_Request' => array(
		'version' => '0.4.10.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-speed-score-request.php'
	),
	'Automattic\\Jetpack\\Composer\\Manager' => array(
		'version' => '4.0.5.0',
		'path'    => $vendorDir . '/automattic/jetpack-composer-plugin/src/class-manager.php'
	),
	'Automattic\\Jetpack\\Composer\\Plugin' => array(
		'version' => '4.0.5.0',
		'path'    => $vendorDir . '/automattic/jetpack-composer-plugin/src/class-plugin.php'
	),
	'Automattic\\Jetpack\\Config' => array(
		'version' => '3.1.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-config/src/class-config.php'
	),
	'Automattic\\Jetpack\\Connection\\Authorize_Json_Api' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-authorize-json-api.php'
	),
	'Automattic\\Jetpack\\Connection\\Client' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-client.php'
	),
	'Automattic\\Jetpack\\Connection\\Connection_Assets' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-connection-assets.php'
	),
	'Automattic\\Jetpack\\Connection\\Connection_Notice' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-connection-notice.php'
	),
	'Automattic\\Jetpack\\Connection\\Error_Handler' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-error-handler.php'
	),
	'Automattic\\Jetpack\\Connection\\Initial_State' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-initial-state.php'
	),
	'Automattic\\Jetpack\\Connection\\Manager' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-manager.php'
	),
	'Automattic\\Jetpack\\Connection\\Manager_Interface' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/interface-manager.php'
	),
	'Automattic\\Jetpack\\Connection\\Nonce_Handler' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-nonce-handler.php'
	),
	'Automattic\\Jetpack\\Connection\\Package_Version' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-package-version.php'
	),
	'Automattic\\Jetpack\\Connection\\Package_Version_Tracker' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-package-version-tracker.php'
	),
	'Automattic\\Jetpack\\Connection\\Plugin' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-plugin.php'
	),
	'Automattic\\Jetpack\\Connection\\Plugin_Storage' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-plugin-storage.php'
	),
	'Automattic\\Jetpack\\Connection\\REST_Connector' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-rest-connector.php'
	),
	'Automattic\\Jetpack\\Connection\\Rest_Authentication' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-rest-authentication.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-sso.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO\\Force_2FA' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-force-2fa.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO\\Helpers' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-helpers.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO\\Notices' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-notices.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO\\User_Admin' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-user-admin.php'
	),
	'Automattic\\Jetpack\\Connection\\Secrets' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-secrets.php'
	),
	'Automattic\\Jetpack\\Connection\\Server_Sandbox' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-server-sandbox.php'
	),
	'Automattic\\Jetpack\\Connection\\Tokens' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-tokens.php'
	),
	'Automattic\\Jetpack\\Connection\\Tokens_Locks' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-tokens-locks.php'
	),
	'Automattic\\Jetpack\\Connection\\Traits\\WPCOM_REST_API_Proxy_Request' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/traits/trait-wpcom-rest-api-proxy-request.php'
	),
	'Automattic\\Jetpack\\Connection\\Urls' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-urls.php'
	),
	'Automattic\\Jetpack\\Connection\\User_Account_Status' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-user-account-status.php'
	),
	'Automattic\\Jetpack\\Connection\\Users_Connection_Admin' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-users-connection-admin.php'
	),
	'Automattic\\Jetpack\\Connection\\Utils' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-utils.php'
	),
	'Automattic\\Jetpack\\Connection\\Webhooks' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-webhooks.php'
	),
	'Automattic\\Jetpack\\Connection\\Webhooks\\Authorize_Redirect' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/webhooks/class-authorize-redirect.php'
	),
	'Automattic\\Jetpack\\Connection\\XMLRPC_Async_Call' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-xmlrpc-async-call.php'
	),
	'Automattic\\Jetpack\\Connection\\XMLRPC_Connector' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-xmlrpc-connector.php'
	),
	'Automattic\\Jetpack\\Constants' => array(
		'version' => '3.0.8.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-constants/src/class-constants.php'
	),
	'Automattic\\Jetpack\\CookieState' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-cookiestate.php'
	),
	'Automattic\\Jetpack\\Current_Plan' => array(
		'version' => '0.9.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-plans/src/class-current-plan.php'
	),
	'Automattic\\Jetpack\\Device_Detection' => array(
		'version' => '3.0.9.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-device-detection/src/class-device-detection.php'
	),
	'Automattic\\Jetpack\\Device_Detection\\User_Agent_Info' => array(
		'version' => '3.0.9.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-device-detection/src/class-user-agent-info.php'
	),
	'Automattic\\Jetpack\\Errors' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-errors.php'
	),
	'Automattic\\Jetpack\\ExPlat' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-explat/src/class-explat.php'
	),
	'Automattic\\Jetpack\\ExPlat\\REST_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-explat/src/class-rest-controller.php'
	),
	'Automattic\\Jetpack\\Files' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-files.php'
	),
	'Automattic\\Jetpack\\Heartbeat' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-heartbeat.php'
	),
	'Automattic\\Jetpack\\IP\\Utils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-ip/src/class-utils.php'
	),
	'Automattic\\Jetpack\\IdentityCrisis\\Exception' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-exception.php'
	),
	'Automattic\\Jetpack\\IdentityCrisis\\REST_Endpoints' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-rest-endpoints.php'
	),
	'Automattic\\Jetpack\\IdentityCrisis\\UI' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-ui.php'
	),
	'Automattic\\Jetpack\\IdentityCrisis\\URL_Secret' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-url-secret.php'
	),
	'Automattic\\Jetpack\\Identity_Crisis' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-identity-crisis.php'
	),
	'Automattic\\Jetpack\\Image_CDN\\Image_CDN' => array(
		'version' => '0.7.14.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-image-cdn/src/class-image-cdn.php'
	),
	'Automattic\\Jetpack\\Image_CDN\\Image_CDN_Core' => array(
		'version' => '0.7.14.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-image-cdn/src/class-image-cdn-core.php'
	),
	'Automattic\\Jetpack\\Image_CDN\\Image_CDN_Image' => array(
		'version' => '0.7.14.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-image-cdn/src/class-image-cdn-image.php'
	),
	'Automattic\\Jetpack\\Image_CDN\\Image_CDN_Image_Sizes' => array(
		'version' => '0.7.14.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-image-cdn/src/class-image-cdn-image-sizes.php'
	),
	'Automattic\\Jetpack\\Image_CDN\\Image_CDN_Setup' => array(
		'version' => '0.7.14.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-image-cdn/src/class-image-cdn-setup.php'
	),
	'Automattic\\Jetpack\\JITMS\\JITM' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-jitm/src/class-jitm.php'
	),
	'Automattic\\Jetpack\\JITMS\\Post_Connection_JITM' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-jitm/src/class-post-connection-jitm.php'
	),
	'Automattic\\Jetpack\\JITMS\\Pre_Connection_JITM' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-jitm/src/class-pre-connection-jitm.php'
	),
	'Automattic\\Jetpack\\JITMS\\Rest_Api_Endpoints' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-jitm/src/class-rest-api-endpoints.php'
	),
	'Automattic\\Jetpack\\Licensing' => array(
		'version' => '3.0.9.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-licensing/src/class-licensing.php'
	),
	'Automattic\\Jetpack\\Licensing\\Endpoints' => array(
		'version' => '3.0.9.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-licensing/src/class-endpoints.php'
	),
	'Automattic\\Jetpack\\Modules' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-modules.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Activitylog' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-activitylog.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Historically_Active_Modules' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-historically-active-modules.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Hybrid_Product' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-hybrid-product.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Initializer' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-initializer.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Jetpack_Manage' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-jetpack-manage.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Module_Product' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-module-product.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Product' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-product.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-products.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Anti_Spam' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-anti-spam.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Backup' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-backup.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Boost' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-boost.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Complete' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-complete.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Creator' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-creator.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Crm' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-crm.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Extras' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-extras.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Growth' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-growth.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Jetpack_Ai' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-jetpack-ai.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Newsletter' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-newsletter.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Protect' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-protect.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Related_Posts' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-related-posts.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Scan' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-scan.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Search' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-search.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Search_Stats' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-search-stats.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Security' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-security.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Site_Accelerator' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-site-accelerator.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Social' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-social.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Starter' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-starter.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Stats' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-stats.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Videopress' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-videopress.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\REST_AI' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-ai.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\REST_Products' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-products.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\REST_Purchases' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-purchases.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\REST_Recommendations_Evaluation' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-recommendations-evaluation.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\REST_Zendesk_Chat' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-zendesk-chat.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Red_Bubble_Notifications' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-red-bubble-notifications.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Wpcom_Products' => array(
		'version' => '5.20.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-wpcom-products.php'
	),
	'Automattic\\Jetpack\\Partner' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-partner.php'
	),
	'Automattic\\Jetpack\\Partner_Coupon' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-partner-coupon.php'
	),
	'Automattic\\Jetpack\\Password_Checker' => array(
		'version' => '0.4.8.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-password-checker/src/class-password-checker.php'
	),
	'Automattic\\Jetpack\\Paths' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-paths.php'
	),
	'Automattic\\Jetpack\\Plans' => array(
		'version' => '0.9.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-plans/src/class-plans.php'
	),
	'Automattic\\Jetpack\\Plugin_Deactivation\\Deactivation_Handler' => array(
		'version' => '0.3.14.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-plugin-deactivation/src/class-deactivation-handler.php'
	),
	'Automattic\\Jetpack\\Plugins_Installer' => array(
		'version' => '0.5.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-plugins-installer/src/class-plugins-installer.php'
	),
	'Automattic\\Jetpack\\Protect_Models' => array(
		'version' => '0.6.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-protect-models.php'
	),
	'Automattic\\Jetpack\\Protect_Models\\Extension_Model' => array(
		'version' => '0.6.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-extension-model.php'
	),
	'Automattic\\Jetpack\\Protect_Models\\History_Model' => array(
		'version' => '0.6.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-history-model.php'
	),
	'Automattic\\Jetpack\\Protect_Models\\Status_Model' => array(
		'version' => '0.6.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-status-model.php'
	),
	'Automattic\\Jetpack\\Protect_Models\\Threat_Model' => array(
		'version' => '0.6.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-threat-model.php'
	),
	'Automattic\\Jetpack\\Protect_Models\\Vulnerability_Model' => array(
		'version' => '0.6.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-vulnerability-model.php'
	),
	'Automattic\\Jetpack\\Protect_Status\\Plan' => array(
		'version' => '0.6.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-plan.php'
	),
	'Automattic\\Jetpack\\Protect_Status\\Protect_Status' => array(
		'version' => '0.6.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-protect-status.php'
	),
	'Automattic\\Jetpack\\Protect_Status\\REST_Controller' => array(
		'version' => '0.6.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-rest-controller.php'
	),
	'Automattic\\Jetpack\\Protect_Status\\Scan_Status' => array(
		'version' => '0.6.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-scan-status.php'
	),
	'Automattic\\Jetpack\\Protect_Status\\Status' => array(
		'version' => '0.6.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-status.php'
	),
	'Automattic\\Jetpack\\Redirect' => array(
		'version' => '3.0.8.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-redirect/src/class-redirect.php'
	),
	'Automattic\\Jetpack\\Roles' => array(
		'version' => '3.0.8.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-roles/src/class-roles.php'
	),
	'Automattic\\Jetpack\\Schema\\Modifiers\\Modifier_Fallback' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/modifiers/class-modifier-fallback.php'
	),
	'Automattic\\Jetpack\\Schema\\Parser' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/interface-parser.php'
	),
	'Automattic\\Jetpack\\Schema\\Schema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/class-schema.php'
	),
	'Automattic\\Jetpack\\Schema\\Schema_Context' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/class-schema-context.php'
	),
	'Automattic\\Jetpack\\Schema\\Schema_Error' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/class-schema-error.php'
	),
	'Automattic\\Jetpack\\Schema\\Schema_Parser' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/class-schema-parser.php'
	),
	'Automattic\\Jetpack\\Schema\\Types\\Type_Any' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-any.php'
	),
	'Automattic\\Jetpack\\Schema\\Types\\Type_Any_JSON' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-any-json.php'
	),
	'Automattic\\Jetpack\\Schema\\Types\\Type_Array' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-array.php'
	),
	'Automattic\\Jetpack\\Schema\\Types\\Type_Assoc_Array' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-assoc-array.php'
	),
	'Automattic\\Jetpack\\Schema\\Types\\Type_Boolean' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-boolean.php'
	),
	'Automattic\\Jetpack\\Schema\\Types\\Type_Enum' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-enum.php'
	),
	'Automattic\\Jetpack\\Schema\\Types\\Type_Float' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-float.php'
	),
	'Automattic\\Jetpack\\Schema\\Types\\Type_Literal' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-literal.php'
	),
	'Automattic\\Jetpack\\Schema\\Types\\Type_Number' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-number.php'
	),
	'Automattic\\Jetpack\\Schema\\Types\\Type_String' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-string.php'
	),
	'Automattic\\Jetpack\\Schema\\Types\\Type_Void' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/types/class-type-void.php'
	),
	'Automattic\\Jetpack\\Schema\\Utils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-schema/src/class-utils.php'
	),
	'Automattic\\Jetpack\\Status' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-status.php'
	),
	'Automattic\\Jetpack\\Status\\Cache' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-cache.php'
	),
	'Automattic\\Jetpack\\Status\\Host' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-host.php'
	),
	'Automattic\\Jetpack\\Status\\Request' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-request.php'
	),
	'Automattic\\Jetpack\\Status\\Visitor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-visitor.php'
	),
	'Automattic\\Jetpack\\Sync\\Actions' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-actions.php'
	),
	'Automattic\\Jetpack\\Sync\\Codec_Interface' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/interface-codec.php'
	),
	'Automattic\\Jetpack\\Sync\\Data_Settings' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-data-settings.php'
	),
	'Automattic\\Jetpack\\Sync\\Dedicated_Sender' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-dedicated-sender.php'
	),
	'Automattic\\Jetpack\\Sync\\Default_Filter_Settings' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-default-filter-settings.php'
	),
	'Automattic\\Jetpack\\Sync\\Defaults' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-defaults.php'
	),
	'Automattic\\Jetpack\\Sync\\Functions' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-functions.php'
	),
	'Automattic\\Jetpack\\Sync\\Health' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-health.php'
	),
	'Automattic\\Jetpack\\Sync\\JSON_Deflate_Array_Codec' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-json-deflate-array-codec.php'
	),
	'Automattic\\Jetpack\\Sync\\Listener' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-listener.php'
	),
	'Automattic\\Jetpack\\Sync\\Lock' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-lock.php'
	),
	'Automattic\\Jetpack\\Sync\\Main' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-main.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-modules.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Attachments' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-attachments.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Callables' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-callables.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Comments' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-comments.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Constants' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-constants.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Full_Sync' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-full-sync.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Full_Sync_Immediately' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-full-sync-immediately.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Import' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-import.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Menus' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-menus.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Meta' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-meta.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Module' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-module.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Network_Options' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-network-options.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Options' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-options.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Plugins' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-plugins.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Posts' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-posts.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Protect' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-protect.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Search' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-search.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Stats' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-stats.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Term_Relationships' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-term-relationships.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Terms' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-terms.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Themes' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-themes.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Updates' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-updates.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Users' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-users.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\WP_Super_Cache' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-wp-super-cache.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\WooCommerce' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-woocommerce.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\WooCommerce_HPOS_Orders' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-woocommerce-hpos-orders.php'
	),
	'Automattic\\Jetpack\\Sync\\Package_Version' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-package-version.php'
	),
	'Automattic\\Jetpack\\Sync\\Queue' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-queue.php'
	),
	'Automattic\\Jetpack\\Sync\\Queue\\Queue_Storage_Options' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/sync-queue/class-queue-storage-options.php'
	),
	'Automattic\\Jetpack\\Sync\\Queue\\Queue_Storage_Table' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/sync-queue/class-queue-storage-table.php'
	),
	'Automattic\\Jetpack\\Sync\\Queue_Buffer' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-queue-buffer.php'
	),
	'Automattic\\Jetpack\\Sync\\REST_Endpoints' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-rest-endpoints.php'
	),
	'Automattic\\Jetpack\\Sync\\REST_Sender' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-rest-sender.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-replicastore.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/replicastore/class-table-checksum.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum_Usermeta' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/replicastore/class-table-checksum-usermeta.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum_Users' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/replicastore/class-table-checksum-users.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore_Interface' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/interface-replicastore.php'
	),
	'Automattic\\Jetpack\\Sync\\Sender' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-sender.php'
	),
	'Automattic\\Jetpack\\Sync\\Server' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-server.php'
	),
	'Automattic\\Jetpack\\Sync\\Settings' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-settings.php'
	),
	'Automattic\\Jetpack\\Sync\\Simple_Codec' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-simple-codec.php'
	),
	'Automattic\\Jetpack\\Sync\\Users' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-users.php'
	),
	'Automattic\\Jetpack\\Sync\\Utils' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-utils.php'
	),
	'Automattic\\Jetpack\\Terms_Of_Service' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-terms-of-service.php'
	),
	'Automattic\\Jetpack\\Tracking' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-tracking.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Data_Sync_Action' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-data-sync-action.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Data_Sync_Entry' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-data-sync-entry.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Entry_Can_Delete' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-entry-can-delete.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Entry_Can_Get' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-entry-can-get.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Entry_Can_Merge' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-entry-can-merge.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Entry_Can_Set' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-entry-can-set.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Entry_Has_Custom_Endpoints' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-entry-custom-endpoint.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Contracts\\Lazy_Entry' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/contracts/interface-lazy-entry.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\DS_Utils' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/class-ds-utils.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Data_Sync' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/class-data-sync.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Data_Sync_Entry_Adapter' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/class-data-sync-entry-adapter.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Data_Sync_Option' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/class-data-sync-option.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Data_Sync_Readonly' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/class-data-sync-readonly.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Endpoints\\Action_Endpoint' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/endpoints/class-action-endpoint.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Endpoints\\Authenticated_Nonce' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/endpoints/class-authenticated-nonce.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Endpoints\\Endpoint' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/endpoints/class-endpoint.php'
	),
	'Automattic\\Jetpack\\WP_JS_Data_Sync\\Registry' => array(
		'version' => '0.6.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-wp-js-data-sync/src/class-registry.php'
	),
	'Automattic\\Jetpack_Boost\\Admin\\Admin' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/admin/class-admin.php'
	),
	'Automattic\\Jetpack_Boost\\Admin\\Config' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/admin/class-config.php'
	),
	'Automattic\\Jetpack_Boost\\Admin\\Regenerate_Admin_Notice' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/admin/class-regenerate-admin-notice.php'
	),
	'Automattic\\Jetpack_Boost\\Contracts\\Changes_Output_After_Activation' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/contracts/interface-changes-output-after-activation.php'
	),
	'Automattic\\Jetpack_Boost\\Contracts\\Changes_Output_On_Activation' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/contracts/interface-changes-output-on-activation.php'
	),
	'Automattic\\Jetpack_Boost\\Contracts\\Feature' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/contracts/interface-feature.php'
	),
	'Automattic\\Jetpack_Boost\\Contracts\\Has_Activate' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/contracts/interface-has-activate.php'
	),
	'Automattic\\Jetpack_Boost\\Contracts\\Has_Data_Sync' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/contracts/interface-has-data-sync.php'
	),
	'Automattic\\Jetpack_Boost\\Contracts\\Has_Deactivate' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/contracts/interface-has-deactivate.php'
	),
	'Automattic\\Jetpack_Boost\\Contracts\\Has_Setup' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/contracts/interface-has-setup.php'
	),
	'Automattic\\Jetpack_Boost\\Contracts\\Has_Slug' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/contracts/interface-has-slug.php'
	),
	'Automattic\\Jetpack_Boost\\Contracts\\Is_Always_On' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/contracts/interface-is-always-on.php'
	),
	'Automattic\\Jetpack_Boost\\Contracts\\Needs_To_Be_Ready' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/contracts/interface-needs-to-be-ready.php'
	),
	'Automattic\\Jetpack_Boost\\Contracts\\Needs_Website_To_Be_Public' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/contracts/interface-needs-website-to-be-public.php'
	),
	'Automattic\\Jetpack_Boost\\Contracts\\Optimization' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/contracts/interface-optimization.php'
	),
	'Automattic\\Jetpack_Boost\\Contracts\\Sub_Feature' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/contracts/interface-sub-feature.php'
	),
	'Automattic\\Jetpack_Boost\\Data_Sync\\Cornerstone_Pages_Entry' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/data-sync/class-cornerstone-pages-entry.php'
	),
	'Automattic\\Jetpack_Boost\\Data_Sync\\Critical_CSS_Meta_Entry' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/data-sync/class-critical-css-meta-entry.php'
	),
	'Automattic\\Jetpack_Boost\\Data_Sync\\Getting_Started_Entry' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/data-sync/class-getting-started-entry.php'
	),
	'Automattic\\Jetpack_Boost\\Data_Sync\\Mergeable_Array_Entry' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/data-sync/class-mergeable-array-entry.php'
	),
	'Automattic\\Jetpack_Boost\\Data_Sync\\Minify_Excludes_State_Entry' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/data-sync/class-minify-excludes-state-entry.php'
	),
	'Automattic\\Jetpack_Boost\\Data_Sync\\Modules_State_Entry' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/data-sync/class-modules-state-entry.php'
	),
	'Automattic\\Jetpack_Boost\\Data_Sync\\Performance_History_Entry' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/data-sync/class-performance-history-entry.php'
	),
	'Automattic\\Jetpack_Boost\\Jetpack_Boost' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/class-jetpack-boost.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Analytics' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-analytics.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Assets' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-assets.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Boost_Health' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-boost-health.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\CLI' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-cli.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Cache_Compatibility' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-cache-compatibility.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Collection' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-collection.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Connection' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-connection.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Cornerstone\\Cornerstone_Pages' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/cornerstone/class-cornerstone-pages.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Cornerstone\\Cornerstone_Utils' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/cornerstone/class-cornerstone-utils.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Admin_Bar_Compatibility' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/class-admin-bar-compatibility.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Critical_CSS_Invalidator' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/class-critical-css-invalidator.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Critical_CSS_State' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/class-critical-css-state.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Critical_CSS_Storage' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/class-critical-css-storage.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Data_Sync\\Data_Sync_Schema' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/data-sync/class-data-sync-schema.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Data_Sync_Actions\\Regenerate_CSS' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/data-sync-actions/class-regenerate-css.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Data_Sync_Actions\\Set_Provider_CSS' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/data-sync-actions/class-set-provider-css.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Data_Sync_Actions\\Set_Provider_Error_Dismissed' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/data-sync-actions/class-set-provider-error-dismissed.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Data_Sync_Actions\\Set_Provider_Errors' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/data-sync-actions/class-set-provider-errors.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Display_Critical_CSS' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/class-display-critical-css.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Generator' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/class-generator.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Regenerate' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/class-regenerate.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Providers\\Archive_Provider' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/source-providers/providers/class-archive-provider.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Providers\\Cornerstone_Provider' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/source-providers/providers/class-cornerstone-provider.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Providers\\Post_ID_Provider' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/source-providers/providers/class-post-id-provider.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Providers\\Provider' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/source-providers/providers/class-provider.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Providers\\Singular_Post_Provider' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/source-providers/providers/class-singular-post-provider.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Providers\\Taxonomy_Provider' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/source-providers/providers/class-taxonomy-provider.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Providers\\WP_Core_Provider' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/source-providers/providers/class-wp-core-provider.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Critical_CSS\\Source_Providers\\Source_Providers' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/critical-css/source-providers/class-source-providers.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Debug' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-debug.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Environment_Change_Detector' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-environment-change-detector.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Minify' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-minify.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Minify\\Cleanup_Stored_Paths' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/minify/class-cleanup-stored-paths.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Minify\\Concatenate_CSS' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/minify/class-concatenate-css.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Minify\\Concatenate_JS' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/minify/class-concatenate-js.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Minify\\Config' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/minify/class-config.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Minify\\Dependency_Path_Mapping' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/minify/class-dependency-path-mapping.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Minify\\File_Paths' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/minify/class-file-paths.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Minify\\Utils' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/minify/class-utils.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\My_Jetpack' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-my-jetpack.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Nonce' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-nonce.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Output_Filter' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-output-filter.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Premium_Features' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-premium-features.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Premium_Pricing' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-premium-pricing.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Setup' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-setup.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Site_Health' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-site-health.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Site_Urls' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-site-urls.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Status' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-status.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Storage_Post_Type' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-storage-post-type.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Super_Cache_Config_Compatibility' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-super-cache-config-compatibility.php'
	),
	'Automattic\\Jetpack_Boost\\Lib\\Super_Cache_Tracking' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/lib/class-super-cache-tracking.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Features_Index' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/class-features-index.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Image_Guide\\Image_Guide' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/image-guide/class-image-guide.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Image_Guide\\Image_Guide_Proxy' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/image-guide/class-image-guide-proxy.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Image_Size_Analysis\\Data_Sync\\Data_Sync_Schema' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/image-size-analysis/data-sync/class-data-sync-schema.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Image_Size_Analysis\\Data_Sync\\Image_Size_Analysis_Entry' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/image-size-analysis/data-sync/class-image-size-analysis-entry.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Image_Size_Analysis\\Data_Sync\\Image_Size_Analysis_Summary' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/image-size-analysis/data-sync/class-image-size-analysis-summary.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Image_Size_Analysis\\Data_Sync\\Image_Size_Analysis_UI_State' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/image-size-analysis/data-sync/class-image-size-analysis-ui-state.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Image_Size_Analysis\\Image_Size_Analysis' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/image-size-analysis/class-image-size-analysis.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Image_Size_Analysis\\Image_Size_Analysis_Fixer' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/image-size-analysis/class-image-size-analysis-fixer.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Module' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/class-module.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Modules_Setup' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/class-modules-setup.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Cloud_CSS\\Cloud_CSS' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/cloud-css/class-cloud-css.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Cloud_CSS\\Cloud_CSS_Followup' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/cloud-css/class-cloud-css-followup.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Critical_CSS\\CSS_Proxy' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/critical-css/class-css-proxy.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Critical_CSS\\Critical_CSS' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/critical-css/class-critical-css.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Image_CDN\\Image_CDN' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/image-cdn/class-image-cdn.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Image_CDN\\Liar' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/image-cdn/class-liar.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Image_CDN\\Quality_Settings' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/image-cdn/class-quality-settings.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_Analyzer' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/lcp/class-lcp-analyzer.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_Invalidator' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/lcp/class-lcp-invalidator.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_Optimization_Util' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/lcp/class-lcp-optimization-util.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_Optimize_Bg_Image' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/lcp/class-lcp-optimize-bg-image.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_Optimize_Img_Tag' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/lcp/class-lcp-optimize-img-tag.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_State' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/lcp/class-lcp-state.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_Storage' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/lcp/class-lcp-storage.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\LCP_Utils' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/lcp/class-lcp-utils.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\Lcp' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/lcp/class-lcp.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Lcp\\Optimize_LCP_Endpoint' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/lcp/class-optimize-lcp-endpoint.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Minify\\Minify_CSS' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/minify/class-minify-css.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Minify\\Minify_Common' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/minify/class-minify-common.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Minify\\Minify_JS' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/minify/class-minify-js.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Cache_Preload' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/class-cache-preload.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Data_Sync\\Page_Cache_Entry' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/data-sync/class-page-cache-entry.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Data_Sync_Actions\\Clear_Page_Cache' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/data-sync-actions/class-clear-page-cache.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Data_Sync_Actions\\Deactivate_WPSC' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/data-sync-actions/class-deactivate-wpsc.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Data_Sync_Actions\\Run_Setup' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/data-sync-actions/class-run-setup.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Garbage_Collection' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/class-garbage-collection.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Page_Cache' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/class-page-cache.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Page_Cache_Setup' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/class-page-cache-setup.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Boost_Cache' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/class-boost-cache.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Boost_Cache_Error' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/class-boost-cache-error.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Boost_Cache_Settings' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/class-boost-cache-settings.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Boost_Cache_Utils' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/class-boost-cache-utils.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Filesystem_Utils' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/class-filesystem-utils.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Logger' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/class-logger.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Path_Actions\\Filter_Older' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/path-actions/class-filter-older.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Path_Actions\\Path_Action' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/path-actions/interface-path-action.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Path_Actions\\Rebuild_File' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/path-actions/class-rebuild-file.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Path_Actions\\Simple_Delete' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/path-actions/class-simple-delete.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Request' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/class-request.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Storage\\File_Storage' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/storage/class-file-storage.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Page_Cache\\Pre_WordPress\\Storage\\Storage' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/page-cache/pre-wordpress/storage/interface-storage.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Render_Blocking_JS\\Render_Blocking_JS' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/render-blocking-js/class-render-blocking-js.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Optimizations\\Speculation_Rules\\Speculation_Rules' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/speculation-rules/class-speculation-rules.php'
	),
	'Automattic\\Jetpack_Boost\\Modules\\Performance_History\\Performance_History' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/performance-history/class-performance-history.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Contracts\\Endpoint' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/contracts/interface-endpoint.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Contracts\\Has_Always_Available_Endpoints' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/contracts/interface-has-always-available-endpoints.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Contracts\\Has_Endpoints' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/contracts/interface-has-endpoints.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Contracts\\Permission' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/contracts/interface-permission.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\Image_Analysis_Action_Fix' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/image-size-analysis/data-sync/class-image-analysis-action-fix.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\Image_Size_Analysis_Summary_Action_Paginate' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/image-size-analysis/data-sync/class-image-size-analysis-summary-action-paginate.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\Image_Size_Analysis_Summary_Action_Start' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/image-size-analysis/data-sync/class-image-size-analysis-summary-action-start.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\List_Cornerstone_Pages' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/endpoints/class-list-cornerstone-pages.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\List_LCP_Analysis' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/endpoints/class-list-lcp-analysis.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\List_Site_Urls' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/endpoints/class-list-site-urls.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\List_Source_Providers' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/endpoints/class-list-source-providers.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\Update_Cloud_CSS' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/endpoints/class-update-cloud-css.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Endpoints\\Update_LCP' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/endpoints/class-update-lcp.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Permissions\\Current_User_Admin' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/permissions/class-current-user-admin.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Permissions\\Nonce' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/permissions/class-nonce.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Permissions\\Signed_With_Blog_Token' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/permissions/class-signed-with-blog-token.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\REST_API' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/class-rest-api.php'
	),
	'Automattic\\Jetpack_Boost\\REST_API\\Route' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/rest-api/class-route.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Admin\\Config_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/admin/Config_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Base_TestCase' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/Base_TestCase.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Image_Size_Analysis_Deprecation_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/Image_Size_Analysis_Deprecation_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Jetpack_Boost_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/Jetpack_Boost_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Lib\\Analytics_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/lib/Analytics_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Lib\\Cache_Compatibility_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/lib/Cache_Compatibility_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Lib\\Cornerstone\\Cornerstone_Pages_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/lib/cornerstone/Cornerstone_Pages_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Lib\\Critical_CSS\\Display_Critical_CSS_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/lib/critical-css/Display_Critical_CSS_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Lib\\Minify\\Functions_Service_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/lib/minify/Functions_Service_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Lib\\Minify_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/lib/Minify_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Lib\\Mocks\\Mock_Premium_Features' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/lib/mocks/class-mock-premium-features.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Modules\\Features_Index_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/modules/Features_Index_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Modules\\Force_Disabled_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/modules/Force_Disabled_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Modules\\Module_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/modules/Module_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Modules\\Optimizations\\Page_Cache\\Cache_Preload_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/modules/optimizations/page-cache/Cache_Preload_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Modules\\Optimizations\\Page_Cache\\Filesystem_Utils_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/modules/optimizations/page-cache/Filesystem_Utils_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Modules\\Optimizations\\Page_Cache\\Path_Actions_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/modules/optimizations/page-cache/Path_Actions_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Modules\\Optimizations\\Speculation_Rules\\Speculation_Rules_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/modules/optimizations/speculation-rules/Speculation_Rules_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\My_Jetpack_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/My_Jetpack_Test.php'
	),
	'Automattic\\Jetpack_Boost\\Tests\\Super_Cache_Compatibility_Checker_Test' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/tests/php/Super_Cache_Compatibility_Checker_Test.php'
	),
	'Container' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-container.php'
	),
	'Hook_Manager' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-hook-manager.php'
	),
	'Jetpack_IXR_Client' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-ixr-client.php'
	),
	'Jetpack_IXR_ClientMulticall' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-ixr-clientmulticall.php'
	),
	'Jetpack_Options' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-options.php'
	),
	'Jetpack_Signature' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-signature.php'
	),
	'Jetpack_Tracks_Client' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-tracks-client.php'
	),
	'Jetpack_Tracks_Event' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-tracks-event.php'
	),
	'Jetpack_XMLRPC_Server' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-xmlrpc-server.php'
	),
	'Latest_Autoloader_Guard' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-latest-autoloader-guard.php'
	),
	'Manifest_Reader' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-manifest-reader.php'
	),
	'MatthiasMullie\\Minify\\CSS' => array(
		'version' => '1.3.75.0',
		'path'    => $vendorDir . '/matthiasmullie/minify/src/CSS.php'
	),
	'MatthiasMullie\\Minify\\Exception' => array(
		'version' => '1.3.75.0',
		'path'    => $vendorDir . '/matthiasmullie/minify/src/Exception.php'
	),
	'MatthiasMullie\\Minify\\Exceptions\\BasicException' => array(
		'version' => '1.3.75.0',
		'path'    => $vendorDir . '/matthiasmullie/minify/src/Exceptions/BasicException.php'
	),
	'MatthiasMullie\\Minify\\Exceptions\\FileImportException' => array(
		'version' => '1.3.75.0',
		'path'    => $vendorDir . '/matthiasmullie/minify/src/Exceptions/FileImportException.php'
	),
	'MatthiasMullie\\Minify\\Exceptions\\IOException' => array(
		'version' => '1.3.75.0',
		'path'    => $vendorDir . '/matthiasmullie/minify/src/Exceptions/IOException.php'
	),
	'MatthiasMullie\\Minify\\Exceptions\\PatternMatchException' => array(
		'version' => '1.3.75.0',
		'path'    => $vendorDir . '/matthiasmullie/minify/src/Exceptions/PatternMatchException.php'
	),
	'MatthiasMullie\\Minify\\JS' => array(
		'version' => '1.3.75.0',
		'path'    => $vendorDir . '/matthiasmullie/minify/src/JS.php'
	),
	'MatthiasMullie\\Minify\\Minify' => array(
		'version' => '1.3.75.0',
		'path'    => $vendorDir . '/matthiasmullie/minify/src/Minify.php'
	),
	'MatthiasMullie\\PathConverter\\Converter' => array(
		'version' => '1.1.3.0',
		'path'    => $vendorDir . '/matthiasmullie/path-converter/src/Converter.php'
	),
	'MatthiasMullie\\PathConverter\\ConverterInterface' => array(
		'version' => '1.1.3.0',
		'path'    => $vendorDir . '/matthiasmullie/path-converter/src/ConverterInterface.php'
	),
	'MatthiasMullie\\PathConverter\\NoConverter' => array(
		'version' => '1.1.3.0',
		'path'    => $vendorDir . '/matthiasmullie/path-converter/src/NoConverter.php'
	),
	'PHP_Autoloader' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-php-autoloader.php'
	),
	'Path_Processor' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-path-processor.php'
	),
	'Plugin_Locator' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-plugin-locator.php'
	),
	'Plugins_Handler' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-plugins-handler.php'
	),
	'Shutdown_Handler' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-shutdown-handler.php'
	),
	'Version_Loader' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-version-loader.php'
	),
	'Version_Selector' => array(
		'version' => '5.0.8',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-version-selector.php'
	),
	'WP_Speculation_Rules' => array(
		'version' => '4.2.1.0',
		'path'    => $baseDir . '/app/modules/optimizations/speculation-rules/class-wp-speculation-rules.php'
	),
);
