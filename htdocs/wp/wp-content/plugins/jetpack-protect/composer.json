{"name": "automattic/jetpack-protect", "description": "Social plugin", "type": "wordpress-plugin", "license": "GPL-2.0-or-later", "require": {"ext-json": "*", "automattic/jetpack-assets": "^4.3.1", "automattic/jetpack-admin-ui": "^0.5.10", "automattic/jetpack-autoloader": "^5.0.9", "automattic/jetpack-composer-plugin": "^4.0.5", "automattic/jetpack-config": "^3.1.1", "automattic/jetpack-my-jetpack": "^5.21.0", "automattic/jetpack-plugins-installer": "^0.5.6", "automattic/jetpack-sync": "^4.16.0", "automattic/jetpack-transport-helper": "^0.3.2", "automattic/jetpack-plans": "^0.9.0", "automattic/jetpack-waf": "^0.27.1", "automattic/jetpack-status": "^6.0.0", "automattic/jetpack-protect-status": "^0.7.0", "automattic/jetpack-account-protection": "^0.2.6"}, "require-dev": {"yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3"}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": ["php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\""], "test-php": ["@composer phpunit"], "build-development": ["pnpm run build"], "build-production": ["pnpm run build-production-concurrently"], "watch": ["Composer\\Config::disableProcessTimeout", "pnpm run watch"]}, "repositories": [], "minimum-stability": "dev", "prefer-stable": true, "extra": {"mirror-repo": "Automattic/jetpack-protect-plugin", "autorelease": true, "autotagger": {"v": false}, "release-branch-prefix": "protect", "wp-plugin-slug": "jetpack-protect", "wp-svn-autopublish": true, "version-constants": {"JETPACK_PROTECT_VERSION": "jetpack-protect.php"}}, "config": {"allow-plugins": {"roots/wordpress-core-installer": true, "automattic/jetpack-autoloader": true, "automattic/jetpack-composer-plugin": true}, "autoloader-suffix": "c4802e05bbcf59fd3b6350e8d3e5482c_protectⓥ4_3_0"}}