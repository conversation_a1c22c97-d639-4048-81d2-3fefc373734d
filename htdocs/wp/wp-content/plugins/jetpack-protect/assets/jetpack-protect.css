@font-face {
	font-family: jetpack-protect;
	src: url(fonts/jetpack-protect.eot?31wpn);
	src:
		url(fonts/jetpack-protect.eot?31wpn#iefix) format("embedded-opentype"),
		url(fonts/jetpack-protect.ttf?31wpn) format("truetype"),
		url(fonts/jetpack-protect.woff?31wpn) format("woff"),
		url(fonts/jetpack-protect.svg?31wpn#jetpack-protect) format("svg");
	font-weight: 400;
	font-style: normal;
	font-display: block;
}

[class^="jp-protect-icon"],
[class*=" jp-protect-icon"] {

	/* Use !important to avoid issues with browser extensions that change fonts. */
	/* stylelint-disable-next-line font-family-no-missing-generic-family-keyword
	-- this is a one-off icon font. */
	font-family: jetpack-protect !important;
	speak: never;
	font-style: normal;
	font-weight: 400;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.jp-protect-icon::before {
	content: "\e900";
}
