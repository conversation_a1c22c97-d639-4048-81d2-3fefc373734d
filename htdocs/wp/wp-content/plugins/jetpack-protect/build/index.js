(()=>{var e={1113:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(6087);const n=(0,r.forwardRef)((function({icon:e,size:t=24,...s},n){return(0,r.cloneElement)(e,{width:t,height:t,...s,ref:n})}))},1797:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.<PERSON>,{d:"m14.5 6.5-1 1 3.7 3.7H4v1.6h13.2l-3.7 3.7 1 1 5.6-5.5z"})})},3751:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"M12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4ZM12.75 8V13H11.25V8H12.75ZM12.75 14.5V16H11.25V14.5H12.75Z"})})},9648:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{fillRule:"evenodd",d:"M11.25 5h1.5v15h-1.5V5zM6 10h1.5v10H6V10zm12 4h-1.5v6H18v-6z",clipRule:"evenodd"})})},3883:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})})},4969:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)(r.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})})},8888:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})})},9115:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"})})},8248:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)(r.Path,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})})},1249:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})})},991:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"m13.06 12 6.47-6.47-1.06-1.06L12 10.94 5.53 4.47 4.47 5.53 10.94 12l-6.47 6.47 1.06 1.06L12 13.06l6.47 6.47 1.06-1.06L13.06 12Z"})})},4314:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)(r.Path,{d:"M20.8 10.7l-4.3-4.3-1.1 1.1 4.3 4.3c.1.1.1.3 0 .4l-4.3 4.3 1.1 1.1 4.3-4.3c.7-.8.7-1.9 0-2.6zM4.2 11.8l4.3-4.3-1-1-4.3 4.3c-.7.7-.7 1.8 0 2.5l4.3 4.3 1.1-1.1-4.3-4.3c-.2-.1-.2-.3-.1-.4z"})})},2072:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)(r.Path,{d:"M17.2 10.9c-.5-1-1.2-2.1-2.1-3.2-.6-.9-1.3-1.7-2.1-2.6L12 4l-1 1.1c-.6.9-1.3 1.7-2 2.6-.8 1.2-1.5 2.3-2 3.2-.6 1.2-1 2.2-1 3 0 3.4 2.7 6.1 6.1 6.1s6.1-2.7 6.1-6.1c0-.8-.3-1.8-1-3zm-5.1 7.6c-2.5 0-4.6-2.1-4.6-4.6 0-.3.1-1 .8-2.3.5-.9 1.1-1.9 2-3.1.7-.9 1.3-1.7 1.8-2.3.7.8 1.3 1.6 1.8 2.3.8 1.1 1.5 2.2 2 3.1.7 1.3.8 2 .8 2.3 0 2.5-2.1 4.6-4.6 4.6z"})})},3512:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})})},1651:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"m3 5c0-1.10457.89543-2 2-2h13.5c1.1046 0 2 .89543 2 2v13.5c0 1.1046-.8954 2-2 2h-13.5c-1.10457 0-2-.8954-2-2zm2-.5h6v6.5h-6.5v-6c0-.27614.22386-.5.5-.5zm-.5 8v6c0 .2761.22386.5.5.5h6v-6.5zm8 0v6.5h6c.2761 0 .5-.2239.5-.5v-6zm0-8v6.5h6.5v-6c0-.27614-.2239-.5-.5-.5z",fillRule:"evenodd",clipRule:"evenodd"})})},9783:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)(r.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M5.5 12a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0ZM12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16Zm.75 4v1.5h-1.5V8h1.5Zm0 8v-5h-1.5v5h1.5Z"})})},435:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"M10.5 4v4h3V4H15v4h1.5a1 1 0 011 1v4l-3 4v2a1 1 0 01-1 1h-3a1 1 0 01-1-1v-2l-3-4V9a1 1 0 011-1H9V4h1.5zm.5 12.5v2h2v-2l3-4v-3H8v3l3 4z"})})},5302:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,n.jsx)(r.Path,{d:"M12 3.176l6.75 3.068v4.574c0 3.9-2.504 7.59-6.035 8.755a2.283 2.283 0 01-1.43 0c-3.53-1.164-6.035-4.856-6.035-8.755V6.244L12 3.176zM6.75 7.21v3.608c0 3.313 2.145 6.388 5.005 ************.331.053.49 0 2.86-.942 5.005-4.017 5.005-7.33V7.21L12 4.824 6.75 7.21z",fillRule:"evenodd",clipRule:"evenodd"})})},4648:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),n=s(790);const a=(0,n.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24",children:(0,n.jsx)(r.Path,{d:"M20 10c0-5.51-4.49-10-10-10C4.48 0 0 4.49 0 10c0 5.52 4.48 10 10 10 5.51 0 10-4.48 10-10zM7.78 15.37L4.37 6.22c.55-.02 1.17-.08 1.17-.08.5-.06.44-1.13-.06-1.11 0 0-1.45.11-2.37.11-.18 0-.37 0-.58-.01C4.12 2.69 6.87 1.11 10 1.11c2.33 0 4.45.87 6.05 2.34-.68-.11-1.65.39-1.65 1.58 0 .74.45 1.36.9 *********.55 1.36.55 2.46 0 1.49-1.4 5-1.4 5l-3.03-8.37c.54-.02.82-.17.82-.17.5-.05.44-1.25-.06-1.22 0 0-1.44.12-2.38.12-.87 0-2.33-.12-2.33-.12-.5-.03-.56 1.2-.06 1.22l.92.08 1.26 3.41zM17.41 10c.24-.64.74-1.87.43-4.25.7 1.29 1.05 2.71 1.05 4.25 0 3.29-1.73 6.24-4.4 7.78.97-2.59 1.94-5.2 2.92-7.78zM6.1 18.09C3.12 16.65 1.11 13.53 1.11 10c0-1.3.23-2.48.72-3.59C3.25 10.3 4.67 14.2 6.1 18.09zm4.03-6.63l2.58 6.98c-.86.29-1.76.45-2.71.45-.79 0-1.57-.11-2.29-.33.81-2.38 1.62-4.74 2.42-7.1z"})})},6185:e=>{"use strict";function t(e){return e&&"object"==typeof e?n(e)||a(e)?e:r(e)?function(e,t){if(e.map)return e.map(t);for(var s=[],r=0;r<e.length;r++)s.push(t(e[r],r));return s}(e,t):function(e,t,s){if(e.reduce)return e.reduce(t,s);for(var r=0;r<e.length;r++)s=t(s,e[r],r);return s}(o(e),(function(r,n){return r[s(n)]=t(e[n]),r}),{}):e}function s(e){return e.replace(/[_.-](\w|$)/g,(function(e,t){return t.toUpperCase()}))}e.exports=function(e){return"string"==typeof e?s(e):t(e)};var r=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},n=function(e){return"[object Date]"===Object.prototype.toString.call(e)},a=function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},i=Object.prototype.hasOwnProperty,o=Object.keys||function(e){var t=[];for(var s in e)i.call(e,s)&&t.push(s);return t}},3172:(e,t)=>{"use strict";const s=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,n=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a=/^[\u0020-\u003A\u003D-\u007E]*$/,i=Object.prototype.toString,o=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function c(e,t,s){do{const s=e.charCodeAt(t);if(32!==s&&9!==s)return t}while(++t<s);return s}function l(e,t,s){for(;t>s;){const s=e.charCodeAt(--t);if(32!==s&&9!==s)return t+1}return s}function d(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},6941:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,n=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(r++,"%c"===e&&(n=r))})),t.splice(n,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")||t.storage.getItem("DEBUG")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(3212)(t);const{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},3212:(e,t,s)=>{e.exports=function(e){function t(e){let s,n,a,i=null;function o(...e){if(!o.enabled)return;const r=o,n=Number(new Date),a=n-(s||n);r.diff=a,r.prev=s,r.curr=n,s=n,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((s,n)=>{if("%%"===s)return"%";i++;const a=t.formatters[n];if("function"==typeof a){const t=e[i];s=a.call(r,t),e.splice(i,1),i--}return s})),t.formatArgs.call(r,e);(r.log||t.log).apply(r,e)}return o.namespace=e,o.useColors=t.useColors(),o.color=t.selectColor(e),o.extend=r,o.destroy=t.destroy,Object.defineProperty(o,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(n!==t.namespaces&&(n=t.namespaces,a=t.enabled(e)),a),set:e=>{i=e}}),"function"==typeof t.init&&t.init(o),o}function r(e,s){const r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function n(e,t){let s=0,r=0,n=-1,a=0;for(;s<e.length;)if(r<t.length&&(t[r]===e[s]||"*"===t[r]))"*"===t[r]?(n=r,a=s,r++):(s++,r++);else{if(-1===n)return!1;r=n+1,a++,s=a}for(;r<t.length&&"*"===t[r];)r++;return r===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const s=("string"==typeof e?e:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const e of s)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const s of t.skips)if(n(e,s))return!1;for(const s of t.names)if(n(e,s))return!0;return!1},t.humanize=s(4997),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((s=>{t[s]=e[s]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t),s|=0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},3060:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={wrapper:"GqFcAwJvIrg1v7f6QUfw",header:"OENx8kmm62tkWGukzP2S",title:"KnqJLKwSceJTwFJrPGHq","close-button":"PJU0_yA9jNf7ao0jhHut",footer:"rrORM3zqHfGvqiPduEXY",steps:"Q7fUcDUGhteXEr18rZPC","action-button":"S5LAeskUiEQ0JlPhr0Ze"}},532:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={"admin-page":"sexr0jUxC1jVixdKiDnC",background:"vKQ11sLeAM45M04P1ccj","admin-page-header":"iWGAhN9gOB48g0jEO1OQ","sandbox-domain-badge":"JOYmuxQjG4FArIIUxJfA"}},2625:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={section:"cAbGtJDGgLubucBnz7vM"}},5061:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={"section-hero":"vMa4i_Dza2t5Zi_Bw9Nf"}},8912:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={badge:"VLE2X0rP3Ug4QHebdiJd","is-success":"hEfxH6nau4tQkeE4BNms","is-warning":"Snl4I6aKbXFIrcWaSIC0","is-danger":"zvZFUdy480Xv3IHYbX6a"}},4659:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={button:"zI5tJ_qhWE6Oe6Lk75GY","is-icon-button":"tuBt2DLqimiImoqVzPqo",small:"Na39I683LAaSA99REg14",normal:"ipS7tKy9GntCS4R3vekF",icon:"paGLQwtPEaJmtArCcmyK",regular:"lZAo6_oGfclXOO9CC6Rd","full-width":"xJDOiJxTt0R_wSl8Ipz_",loading:"q_tVWqMjl39RcY6WtQA6","external-icon":"CDuBjJp_8jxzx5j6Nept"}},4178:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={cut:"msOlyh2T7D6uhbM6AROg",icon:"cPN7USVqSBpxUswfDtUZ",cta:"EmnJAyEzzn1QpA8HtypY",iconContainer:"vV7YZikAz0oHYsuvtxMq",description:"T1YaMupeZmBIpXZHY9EZ"}},553:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={"details-viewer":"b5MHxKCUSavCp6Z2jYOj","details-viewer__item":"mVURFnfQR9hyMfjby9HD","details-viewer__key":"ETnF0XmLj2eAfGOjbI3y","details-viewer__value":"E7P75A5A9gLkE_FPA3Tt"}},2904:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={"diff-viewer":"HPsJA6B0f3Bd2tZgEr7v","diff-viewer__filename":"WA0OogO0bZSYbqUPL9aa","diff-viewer__file":"Z1NxA7aDh5LEYjbLLKbF","diff-viewer__line-numbers":"aeQS3Qb4g1FwDY0HaZKb","diff-viewer__lines":"ESXyHLdGmRLZMv7_Ei9d"}},5618:()=>{},4984:()=>{},9723:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={"star-icon":"cuoSlhSNrqf1dozY22Xb",jetpack:"lAIiifeLMmZAPlQ9n9ZR","checkmark-icon":"JLquNpQVlysAamuh5lJO",socialIcon:"cbOwD8Y4tFjwimmtchQI",bluesky:"aLWBKY0yRghEk7tNCgK3",facebook:"aHOlEBGD5EA8NKRw3xTw",twitter:"af4Y_zItXvLAOEoSDPSv",linkedin:"f68aqF3XSD1OBvXR1get",tumblr:"xFI0dt3UiXRlRQdqPWkx",google:"q7JEoyymveP6kF747M43",mastodon:"DKOBOTVmTLbh26gUH_73",nextdoor:"n5XodNsuMfMAAvqHFmbw",instagram:"cL3m0xBYTYhIKI7lCqDB",whatsapp:"fftumuc_lJ6v0tq4UMVR",threads:"inzgC27qxdt7hSdhTWRI"}},2997:()=>{},9130:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)"}},9750:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)",smcols:"4",mdcols:"8",lgcols:"12","col-sm-1":"RuVLl3q4lxTQa3wbhBJB","col-sm-1-start":"f9LZTRG4MMK42rS89afW","col-sm-1-end":"bHe_zKxjjpUwHw_MdYE1","col-sm-2":"QZbNrOqE2aNSn50xVhpU","col-sm-2-start":"ev7W3z7zVYPeHAlYqZjf","col-sm-2-end":"NJWd1m_e7lOiPYru2ZMP","col-sm-3":"Xc6nt1Qc1DI0Z2A3gt1r","col-sm-3-start":"UIcN_GXiPRoIsin8Kohg","col-sm-3-end":"GRKCyqb5LufCSCgykKFc","col-sm-4":"i_qTq8gqhhC3vIUepVRB","col-sm-4-start":"G3qaZ3Jpbvam_1XvGxgc","col-sm-4-end":"VRCNYKZtO9zukEwmgP1y","col-md-1":"tRm008K_WJL79WoNZTNL","col-md-1-start":"l5T2P_bgKts4tdaRkS1d","col-md-1-end":"zOCxfLZpF6BlgC7a_Yq1","col-md-2":"F80DdgVn0m5OpvtSQWka","col-md-2-start":"oI1c7JYfiJtMQHbhngtU","col-md-2-end":"pMQtA_4jh1_1lVknqEP5","col-md-3":"VenqMpdgyKQVUNNQcfqd","col-md-3-start":"seNYL99uoczf9V4MxBxT","col-md-3-end":"YKfF1HFhI9KygA5l3b2J","col-md-4":"yAi0Cv1xDWkoqsaUhvhR","col-md-4-start":"ubhnyZOnkgxNhh6XtVWv","col-md-4-end":"RGOPGQbWMJ9Ei5oFxS7X","col-md-5":"Sz1E2aWbX483ijdi6yge","col-md-5-start":"tku6_bRYrX9tMbgYGmIl","col-md-5-end":"b5JHttOhSEcI1WBlqAjk","col-md-6":"FboSx5MoKTAWbxXyYlCw","col-md-6-start":"Jhs8yEEmodG30edbJvag","col-md-6-end":"IpzbbKVqEqPcfIGkXkwt","col-md-7":"mhCPwfAZ4Kmm_empzJAq","col-md-7-start":"x034ilrJF7rO9UJB2rI1","col-md-7-end":"Wt8t2e16viRrOJ1lLA5v","col-md-8":"S6pIrEy9AMLKx9bgh_Ae","col-md-8-start":"kEfI4tGyuWfHTlRnvIab","col-md-8-end":"PUzX4RRsKq1dnsz3gebS","col-lg-1":"X_pdcLJikd8LS_YAdJlB","col-lg-1-start":"tl936d14Huby4khYp05X","col-lg-1-end":"hnge0LnR69d3NXEtEE1t","col-lg-2":"fj0NUMuyZQcPNgKcjp5Z","col-lg-2-start":"R2ncBX7a2NigdYCcV1OX","col-lg-2-end":"t8vMSDVYno9k9itRwnXb","col-lg-3":"wsDuEN2GqHx6qzo8dUdk","col-lg-3-start":"cIEVPUweWtLBy3xaXnMx","col-lg-3-end":"fajUWBwu1m2B479j3jmz","col-lg-4":"YR0c7fQTgMkDdWzwSyLp","col-lg-4-start":"xlwp8BmplxkKNMI7gamo","col-lg-4-end":"_C4O1w9DUqx1m3gPf8aA","col-lg-5":"Z54F1hAErckAIrKlxnXW","col-lg-5-start":"ezSDWkRHmKSxDJXxuiOH","col-lg-5-end":"T0ChoeAjGJjkkNrYhD4g","col-lg-6":"qtMoMPF6yHvGJnWHSsde","col-lg-6-start":"gdoywN5VPiWERfIBqkph","col-lg-6-end":"wUev_VH5uf_pwFFlbnAU","col-lg-7":"egIPDFJsOpownTClq9XP","col-lg-7-start":"yGhp9yoAW7k0kQik9AB7","col-lg-7-end":"SJ43U9mR5wUg5V2qBeQA","col-lg-8":"cTuyHfMwSUJxN_HdIEgd","col-lg-8-start":"smCr8DaIagcumdvdldiK","col-lg-8-end":"T03NHzQJvzwL6wAfIiTL","col-lg-9":"pMvxM3RJGjqyNdf9qg1Y","col-lg-9-start":"iIVpNRwEnQ_JI5gpp9EN","col-lg-9-end":"ZbQ4u4vGSX5rJOje4uGL","col-lg-10":"gKb5wuIDAlKGbrjK2vxy","col-lg-10-start":"Z7pINdImE2WJiYnZBTqm","col-lg-10-end":"ZTxp6qpvwurMdOnLLSz1","col-lg-11":"NnQTlbfnxPDR6cQ7rygg","col-lg-11-start":"O137wZd6Yl0olSA9PsXR","col-lg-11-end":"zf2OJtQ2MPz6SDoh6CB0","col-lg-12":"U3H6UHW6HqRt9hdzVg3O","col-lg-12-start":"zynnNeS_ZBTxABcVpUQH","col-lg-12-end":"vI8tltFZtFUNAy9Iag9s"}},9713:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)",container:"SqdhUZkXCRuIpErj1B3z",fluid:"OZC_9a1LhpWF9dv15Gdh"}},4456:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={"marked-lines":"SunkAzopVEoBZbCUFkpe","marked-lines__marked-line":"s6eJjt_RVhQ8mEG1qQYb","marked-lines__line-numbers":"cW2860S1RCjdsufX8ds_","marked-lines__line-number":"hU_xmjv3sGJgIvV_26Ik","marked-lines__lines":"O5ZaHoo1pFRkk_GUUczG","marked-lines__line":"UoS_E4HwI88HfDMpNtvB","marked-lines__mark":"ZotWIwUISq6XCpIyD8k7"}},5355:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={container:"VHYulMcpzbr10HWR0iSE","icon-wrapper":"FGpSkMCiIHQjszcV0dbn","close-button":"KoWZcCwhW13xvkEb0QON","main-content":"smrfczkC53EaFM8OJUXs",title:"IKYRWoPwt9xOVEx1wzNS","action-bar":"qM0qY6mPYp1MPN54A3Kg","is-error":"A5YkDkkXuiYgavrY6Nux",icon:"y_IPyP1wIAOhyNaqvXJq","is-warning":"cT5rwuPMZzWvi5o6shMl","is-info":"yo0O3uvNomPsYUXFCpAS","is-success":"oZdDFf1jBLkzn5ICCC6x"}},4435:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={container:"p4qz2tkq0p9hxucJ6Qk2",table:"lbNDyXioOwvyvbALtCBm","is-viewport-large":"s2Lsn4kbm6BrS3DSndRB",card:"cLaNK_XcbTGlRQ4Tp43Q","is-primary":"CYt1X0eH1icRjhtJ28jx",header:"DAkZc1P9A3K12fjEliMg",item:"WUBuYABl8nymjs9NnCEL","last-feature":"ANtCFeb41NhA8PA3H7ZN",value:"Ql2gy_148yW8Vw5vhaKD",icon:"EAQrAnQEW1z1BfdY5gbC","icon-check":"JDSTlLoOC_4aUoH2oNM2","icon-cross":"zNdQRJ1w7BvaQOYyqzHK",popover:"lr7vbX95SKtoe7DarJcZ","popover-icon":"KRrGp2xdkeBOxLZeuQ6X",tos:"H_ZJiRVJg0LiMXPGOcmt","tos-container":"x21z_DixObRDsDaWotP1"}},8336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={container:"dovianZYLKhnbnh9I06o","price-container":"lljtQMhW7lq5tE5SDJEf","promo-label":"NubApIV1vQCRUNprfm6b",price:"dhFQXpZfMwVI8vuYHnwC","is-not-off-price":"eD7hzxFmdtG_MgmBtl_k",footer:"C64ZjjUAqJC1T2Sa7apS",legend:"UpZDGew6Ay1hPoP6eI7b",symbol:"TDiiPbuW1Z0_05u_pvcK"}},2144:()=>{},4152:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={wrapper:"VfIXL69k_I0zUU0s7LqM",horizontal:"SvvAonSzsVSo_5Hu6Goz",info:"dwXtyo4qYbpJ2b3rZL1N",label:"WxFqmtANZGuBRdR2NewQ",square:"ee2TJQm2FgpSRwchomAH",value:"mgcKzhKqFk7UOZ156QPT",icon:"h_uY7ygjuMtuY4ZWJz1R"}},4533:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={status:"bZ4b33s2MdcOjaDKBbeV",status__indicator:"UzkzDaqt2mXprJh2OXRz",status__label:"QuS0vdcr87FsiBvSSrvA","is-active":"bGx0wbQAwuY7k8P3PHVQ","is-inactive":"wR8Cz8uHgObTQavolv5y","is-error":"IzbBYGwBKhSYFUQlIMww","is-action":"Kdvp_HJf89IBrZuCaLm8","is-initializing":"hVf5vsbBWm8OFye7cy7a"}},4099:()=>{},3370:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={reset:"WQVtrU6q0L1Igcj7wCrQ","headline-medium":"UujoBFTnQNY2cWU2SIsH","headline-small":"TeGO5V_thHw5lDAm1_2M","headline-small-regular":"WolQzb2MsSgiNmLtc7_j","title-medium":"hUB0JT8p1T2Hw28N6qC8","title-medium-semi-bold":"gKZWDv5chz3_O3Syp74H","title-small":"zY2No8Ga4b8shbOQGhnv",body:"tIj0D1t8Cc892ikmgFPZ","body-small":"KdcN0BnOaVeVhyLRKqhS","body-extra-small":"dso3Rh3tl3Xv1GumBktz","body-extra-small-bold":"mQ1UlbN9u4Mg9byO8m7v",label:"PItlW5vRExLnTj4a8eLE","m-0":"TwRpPlktzxhmFVeua7P5","mx-0":"zVfqx7gyb3o9mxfGynn1","my-0":"iSHVzNiB9iVleGljaQxy","mt-0":"xqDIp6cNVr_E6RXaiPyD","mr-0":"S8EwaXk1kyPizt6x4WH2","mb-0":"ODX5Vr1TARoLFkDDFooD","ml-0":"cphJ8dCpfimnky7P2FHg","m-1":"PFgIhNxIyiSuNvQjAIYj","mx-1":"M2jKmUzDxvJjjVEPU3zn","my-1":"io15gAh8tMTNbSEfwJKk","mt-1":"rcTN5uw9xIEeMEGL3Xi_","mr-1":"CQSkybjq2TcRM1Xo9COV","mb-1":"hfqOWgq6_MEGdFE82eOY","ml-1":"I8MxZQYTbuu595yfesWA","m-2":"kQkc6rmdpvLKPkyoJtVQ","mx-2":"j6vFPxWuu4Jan2ldoxpp","my-2":"hqr39dC4H_AbactPAkCG","mt-2":"c3dQnMi16C6J6Ecy4283","mr-2":"YNZmHOuRo6hU7zzKfPdP","mb-2":"Db8lbak1_wunpPk8NwKU","ml-2":"ftsYE5J9hLzquQ0tA5dY","m-3":"Det4MHzLUW7EeDnafPzq","mx-3":"h_8EEAztC29Vve1datb5","my-3":"YXIXJ0h1k47u6hzK8KcM","mt-3":"soADBBkcIKCBXzCTuV9_","mr-3":"zSX59ziEaEWGjnpZa4uV","mb-3":"yrVTnq_WBMbejg89c2ZQ","ml-3":"UKtHPJnI2cXBWtPDm5hM","m-4":"guexok_Tqd5Tf52hRlbT","mx-4":"oS1E2KfTBZkJ3F0tN7T6","my-4":"DN1OhhXi6AoBgEdDSbGd","mt-4":"ot2kkMcYHv53hLZ4LSn0","mr-4":"A1krOZZhlQ6Sp8Cy4bly","mb-4":"pkDbXXXL32237M0hokEh","ml-4":"XXv4kDTGvEnQeuGKOPU3","m-5":"yGqHk1a57gaISwkXwXe6","mx-5":"X8cghM358X3DkXLc9aNK","my-5":"GdfSmGwHlFnN2S6xBn1f","mt-5":"yqeuzwyGQ7zG0avrGqi_","mr-5":"g9emeCkuHvYhveiJbfXO","mb-5":"Lvk3dqcyHbZ07QCRlrUQ","ml-5":"r3yQECDQ9qX0XZzXlVAg","m-6":"aQhlPwht2Cz1X_63Miw0","mx-6":"JyHb0vK3wJgpblL9s5j8","my-6":"cY2gULL1lAv6WPNIRuf3","mt-6":"NBWQ9Lwhh_fnry3lg_p7","mr-6":"yIOniNe5E40C8fWvBm5V","mb-6":"t30usboNSyqfQWIwHvT3","ml-6":"Nm_TyFkYCMhOoghoToKJ","m-7":"C4qJKoBXpgKtpmrqtEKB","mx-7":"S93Srbu6NQ_PBr7DmTiD","my-7":"fJj8k6gGJDks3crUZxOS","mt-7":"cW6D6djs7Ppm7fD7TeoV","mr-7":"DuCnqNfcxcP3Z__Yo5Ro","mb-7":"im8407m2fw5vOg7O2zsw","ml-7":"G0fbeBgvz2sh3uTP9gNl","m-8":"kvW3sBCxRxUqz1jrVMJl","mx-8":"tOjEqjLONQdkiYx_XRnw","my-8":"op5hFSx318zgxsoZZNLN","mt-8":"c9WfNHP6TFKWIfLxv52J","mr-8":"sBA75QqcqRwwYSHJh2wc","mb-8":"GpL6idrXmSOM6jB8Ohsf","ml-8":"HbtWJoQwpgGycz8dGzeT","p-0":"uxX3khU88VQ_Ah49Ejsa","px-0":"KX0FhpBKwKzs9fOUdbNz","py-0":"PfK8vKDyN32dnimlzYjz","pt-0":"emxLHRjQuJsImnPbQIzE","pr-0":"kJ8WzlpTVgdViXt8ukP9","pb-0":"tg_UIUI11VBzrTAn2AzJ","pl-0":"uczvl8kaz84oPQJ2DB2R","p-1":"o7UHPcdVK3lt7q3lqV4o","px-1":"IDqEOxvDoYrFYxELPmtX","py-1":"DdywPW2qSYlu2pt8tpO2","pt-1":"npy3hw4A5QSkDicb2CJJ","pr-1":"LgbptTApNY5NwLQvEFAt","pb-1":"WZQy2SZuZso59bUsXXyl","pl-1":"o331apInxNunbYB3SfPE","p-2":"fMPIyD9Vqki1Lrc_yJnG","px-2":"i2pMcTcdrr10IQoiSm_L","py-2":"eA702gn32kwptiI1obXH","pt-2":"o9bGieUKcYc8o0Ij9oZX","pr-2":"SwZcFez1RDqWsOFjB5iG","pb-2":"eHpLc_idmuEqeqCTvqkN","pl-2":"vU39i2B4P1fUTMB2l6Vo","p-3":"JHWNzBnE29awhdu5BEh1","px-3":"X72lGbb56L3KFzC2xQ9N","py-3":"BzfNhRG8wXdCEB5ocQ6e","pt-3":"srV0KSDC83a2fiimSMMQ","pr-3":"lUWfkmbQjCskhcNwkyCm","pb-3":"Ts0dIlc3aTSL7V4cIHis","pl-3":"CzlqQXXhX6MvorArFZ8B","p-4":"TqMPkQtR_DdZuKb5vBoV","px-4":"a7UrjhI69Vetlcj9ZVzz","py-4":"StEhBzGs2Gi5dDEkjhAv","pt-4":"FGneZfZyvYrt1dG0zcnm","pr-4":"APEH216rpdlJWgD2fHc8","pb-4":"oGwXC3ohCic9XnAj6x69","pl-4":"U6gnT9y42ViPNOcNzBwb","p-5":"IpdRLBwnHqbqFrixgbYC","px-5":"HgNeXvkBa9o3bQ5fvFZm","py-5":"tJtFZM3XfPG9v9TSDfN1","pt-5":"PdifHW45QeXYfK568uD8","pr-5":"mbLkWTTZ0Za_BBbFZ5b2","pb-5":"vVWpZpLlWrkTt0hMk8XU","pl-5":"RxfaJj5a1Nt6IavEo5Zl","p-6":"SppJULDGdnOGcjZNCYBy","px-6":"palY2nLwdoyooPUm9Hhk","py-6":"WYw1JvZC0ppLdvSAPhr_","pt-6":"YEEJ9b90ueQaPfiU8aeN","pr-6":"QE0ssnsKvWJMqlhPbY5u","pb-6":"n8yA3jHlMRyLd5UIfoND","pl-6":"tXHmxYnHzbwtfxEaG51n","p-7":"kBTsPKkO_3g_tLkj77Um","px-7":"RyhrFx6Y1FGDrGAAyaxm","py-7":"CBwRpB0bDN3iEdQPPMJO","pt-7":"vQVSq6SvWKbOMu6r4H6b","pr-7":"oBy5__aEADMsH46mrgFX","pb-7":"KVEXoJqf1s92j0JMdNmN","pl-7":"ZMXGNrNaKW3k_3TLz0Fq","p-8":"tuiR9PhkHXhGyEgzRZRI","px-8":"U7454qyWkQNa2iaSJziu","py-8":"VLYIv2GVocjuN93e8HC8","pt-8":"X1rm9DQ1zLGLfogja5Gn","pr-8":"JS7G6kAuqJo5GIuF8S5t","pb-8":"Y8F9ga1TDCMbM1lj4gUz","pl-8":"AJuyNGrI63BOWql719H8"}},3167:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={global:"_fUXxnSp5pagKBp9gSN7"}},9550:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={toggle:"gecp_0Z0owRZtj8JAYir","is-small":"qgKXEdMmHsIK84VDnLO2","no-label":"Vy6dbB4PsGKm8BDqzJ9J","is-toggling":"ykWv7fKpkVW_vpAoddu4"}},9488:()=>{"use strict"},5950:()=>{"use strict"},7709:()=>{"use strict"},2770:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={tab:"YXKeyNLeZvIdah_5_5GR",navigation:"vvXnRXxrU1kP1KsdSr4J",badge:"aDCTkUFaJEeZzYYE6qv5"}},9067:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={"header-main":"pVJWvyeviifcrLbZZxCj","header-secondary":"segmN0DtUjey3r5zVPON","heading-icon":"lolS8hq75wfk687xQjbY",subheading:"HC8zzYgv0GvkecCkpyld","connection-error-col":"XJqRbP_azFalXqUoYPtl"}},541:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={"button-group":"XqQBRzrxyMhhZvPXIHgk"}},3490:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={heading:"iN9oF7KagtrwaFKTquqW",warning:"IZTexE5bnJrB25jtwXBW","scan-navigation":"YzqaprEdVpBoBJesZixw"}},4906:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={list:"sQkG5Cu80gPaeFj3L_wa",footer:"oIl6GCMeUnzE0inXbuj9"}},1697:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={list:"RP2MVoGqaCFXPKZhhxQ1",footer:"YpEs8b7KE7fzVuXUbOEb"}},8442:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={accordion:"x8UuVEhWucPfdq0hEJg6","accordion-item":"eb6Ovfvm1yONz4mdOwbH","accordion-header":"jF9LXpXR3TxZVDw7TVXT","accordion-header-label":"lNQgAfpwdAUSOLZd8dlD","accordion-header-label-icon":"WRQwDYPdffWznX158azR","accordion-header-description":"dJx9pvlYjDnKn11n5wo8","accordion-header-button":"EKcbLuSblRc7UpzgsFXw","accordion-body":"ILMBWlMLcXc0n97IisrG","accordion-body-close":"T8XIzCNJBQy1UETN5gjU","accordion-body-open":"kWoHGiaZSbq9XJVyUOMC"}},2166:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={threat:"EqpwvErHtRi_WQkJKgi1",threat__icon:"EicvN1QaJu5OJLzdUN5A",threat__summary:"b7ynAmUwGlxK_vxyIN0y",threat__summary__label:"uSV9HYRxjL4S6zIZDlqP",threat__summary__title:"XpW8Mvw_XFEbqxUdw155",threat__severity:"Fkj60BmQ5sUo9zHcJwVv",footer:"XLzPr74ad3osCq9kxy2q"}},7633:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={inProgressAnimation:"AMcxtVDLtWB2EJJJU5NQ",inProgressAnimation__el:"vtViVIQmuI1n8HDxrkNy",inprogress:"BJ0WjZD0MlRR_Usu8HEd"}},7222:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={modal:"WtVEv641JBaLl929sZq2",modal__window:"G0aPt7WmA1rkUE0wcTno",modal__close:"VkrhWjfEIlIwYEumycbP",modal__close__icon:"yFzeV4cmWRbZKk5tIyEw"}},8065:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={navigation:"HQgjxePFDRJHamBAGcVU","navigation-item":"KtdsNIMumOoHNWMLmuAJ",clickable:"ewyV4ZGn34WDNXmK3ej2",selected:"owofT85W_XvVdb5vr1GN","navigation-item-label":"NESnw2xCLrBpP6WlYZL4","navigation-item-icon":"aohnd_y_1lrMuww8NH63","navigation-item-badge":"Rnas_2_2_eLZSYfZ347E","navigation-item-label-text":"m4_14yNRByDyO9yf9pGg","navigation-item-check-badge":"X8NRHUTBa_4wDvoHN9TT","navigation-item-info-badge":"CsuOteurQlJm4IjXqyZ0","navigation-group":"fvbewglUxEvV1o04yzzp","navigation-group-label":"DINy59MKjwiQanaoDTiV","navigation-group-content":"XoacHJljw8zRW_fkBSyg","navigation-group-list":"mXM4WZRePVTa07dhQ6MD","navigation-group-truncate":"sI3V8khLdCqxkhJQ_XnI","popover-text":"hgdxMuIvZwTZ0sfXaols","navigation-dropdown-button":"UErHS2HAh1gszGfYuVIf","navigation-dropdown-label":"lEhH1hvCcxLVFMHI8Gwv","navigation-dropdown-icon":"c57Vj4QhAHwcK_HZMxtN"}},5995:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={notice:"_aiMxmJRAw5jRPbZFX57","notice--info":"eWgNBUaexiR66ZI1Px4U","notice--floating":"ZGELG1CO333FilJXOaeI",notice__icon:"BnI88X_e8ItF1c9Uozmj","notice--success":"hRRUwTCPRpurhMwRNZkg","notice--error":"Z6q3IxY_uR1y2lAPTkVF",notice__message:"ST8sowTbBVLRPrk4ZQrn",notice__close:"lMkO08Vd8YQMfwrwaI8u"}},9810:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={accordion:"sfXsfklfkeSRtE9ivQwF","accordion-item":"ozRrKPlj1QFGUyd3oVke","accordion-header":"PyvnSHQuuUkZvVsArigy","accordion-header-label":"u9Xod5s8bCRNj9MR1Drl","accordion-header-label-icon":"IbeyulkpO9kjYJ4OkYUD","accordion-header-description":"KiCgmjHm2f0JDU3cMsgf","accordion-header-status":"kh5zuLR1zNf7KCsxhAZ5","accordion-header-status-separator":"z1z63g62c73XbcPk9DWc","accordion-header-button":"kJ8t3FAtd5VAYjk31SfA","accordion-body":"JP0IJZcYRohfgntEozjm","accordion-body-close":"y7c6zi2wjEnbW3F29fmP","accordion-body-open":"wQWiN_J0SqgduvadyGnm","icon-check":"v1fUFCBPmd6miSCf_ehK","status-badge":"oR9QG6gRLvyzqiuIarmu",fixed:"bZ8KDXMWoim85Zv31E7E",ignored:"uYEkrN4rh8n5cs3aPrYC","is-fixed":"WOX88CWB12lzxcMcl_ns","support-link":"H2J5mHqyiBHXCAyZ4KGa","icon-tooltip":"BT16ByfO8ktYf15hbMG8","icon-tooltip__icon":"Gi6geVLkEyrClNXuJ34I","icon-tooltip__content":"Q02oRBdY3ZpNGOF2Y1iV"}},9141:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={"progress-bar":"vzMlFr1AXWqefpRrb976","progress-bar__wrapper":"gYSOMa4xxLXmsNzvFPkW","progress-bar__bar":"hNJWdt5qmZkWYAH_vjIp","progress-bar__percent":"DHMUfGyHbl5BgD5vjxVz"}},9386:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={mark:"jNlU3vgp4xEGwmX5aTyK",list:"gE8s4nh3f7iGIBrFeyCT"}},9719:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={tabs:"lyrXe0pA852TUmyekDb5",tab:"KgEeDTKgTC5ZjzYlbTqN","tab--active":"Lv4WoNVkeJntqvUyG4dX"}},5065:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={textarea:"rigH8UdiDrmmSLQMUurD",label:"V8FDM08CpcwQs4UwN2nI"}},7267:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={threat:"NHzH3tt6CypjZ92CvK9x",threat__icon:"D9zvYDUrOP_zwSm0yJMs",threat__summary:"_XISfmbjoVlqhB61hHYn",threat__summary__label:"AQDPBnMZFu7BOBuo8mYW",threat__summary__title:"cZ9s5eCAZe3R82Y6Ru3z",threat__severity:"AKnbWQiViZ2O_dwCV8Fw",threat__checkbox:"W6alQ2_S5Rh06djX9m27"}},203:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={empty:"OtDl6kocO_m2s9sRHaqX","threat-section":"BjwJh1S1YVPI7AjmTx6a","threat-filename":"YWfK8VTp2wnByBauYPKg","threat-footer":"pkw2LnOTd8VvQ3oT8sXQ","threat-item-cta":"ap6lG79CncSqdigJS_WA","list-header":"uym7dGHhp6ifjF57yOAO","list-title":"e_2eFTR8RyHwmfeqJL4F","list-header__controls":"IKQVU01PrDMMV5UFLOKv","accordion-header":"qazuloIt0teplSEwxRN0","manual-scan":"iJ_biSBRDribuNKX0Zuw","pagination-container":"v640YfEL85A9rCvyHSzW",unfocused:"gfxpe4zp6ShFsIsce8Ii"}},3119:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={threat:"L9hh_sX51PzO41icwJlh",threat__icon:"o8E4Bv6U0d7USFxSjRCj",threat__summary:"sT31awD6bHO8KJewvmaO",threat__summary__label:"VVTRIG51r8ORUIBPs5gN",threat__summary__title:"AiaWRbRLFT9xvP9vn5jA",threat__severity:"MP1wWkqvqfvAc7Izl7x4",footer:"i9X_KxEJiGpRT7q_RU6i"}},7751:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={footer:"uUEJGMrSV3XoBwX5xEwA"}},4257:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={container:"L1LjeCYPldBcTLILS13l","toggle-section":"JWWznnb8sAKHIOhE59_z","toggle-section__control":"kV5wl2UResIqqBdSTuQr","toggle-section__content":"oePuSIKcH_JzEhDJcHh8","toggle-section__title":"mxYw7vghJF8H8C0FjvPz","toggle-section--disabled":"lyiOCAyjT_eQyDg8Ier_","toggle-section__details":"IclfL4iNXTZfBAbHZdog","automatic-rules-stats":"bOadM4auwszaOl0el95T","automatic-rules-stats__version":"G7vkgqk8AEd6pRbNUyE4","automatic-rules-stats__last-updated":"qnlbRElIsj4nB45F5s3a","automatic-rules-stats__failed-install":"TvvdPGzRa_ubn3BnQOnQ","upgrade-trigger-section":"yAFMUkq1ot9DZF05RUwG","upgrade-prompt-button":"qoajQ2Ew8eYJgdbssSg9",badge:"EbfrbByI7NQ2_MD_gDXp",popover:"hCwaBLH_6dELyWWwB8S1",popover__header:"V3U9oKSdRmKjOQ523C86",popover__button:"OSrT4pA4vIGzg9EE6rcv",popover__footer:"rbL50q3wwzE6X7VBHcmW","allow-list-current-ip":"g593fzjQCE_4rNiiwLdg","allow-list-button-container":"CIp_BwDbLdjYPSVrreqg","block-list-button-container":"m2epTt5F7wBQNcMdguwQ",divider:"HrEvG7sxsWbTLD0sk477","popover-text":"WIttYmnC0O4JKFyPgkTW","firewall-subheading":"p1_cy4ERoY1_uwtgq3Tn","stat-card-wrapper":"ehnXRuGNl9ZnPh6c1Pa3",disabled:"q8mOIlVn7qA44s7ysC5Z","stat-card-icon":"IxCDeZ2a0wRu3AQbqGuG","stat-card-label":"YIQOtwFZuyfirlpFfjDb","share-data-section":"x8nPhsBEpxvgCxHNbCXo","share-data-toggle":"b_pwVVOoMN73AXhJNbgr","icon-tooltip":"MWO4b3hqGdH8p1Vt9zmQ","icon-tooltip__icon":"DuflEZ86axRQGuMhmH3n","icon-tooltip__content":"ywr2PcNYcX5_sEzdvHAS",footer:"e_wseeb7HqoyLjv9N_ww","standalone-mode":"nxtQ3M8WCX69d_NjShwZ","share-data":"mzfjOg8s_uehdCmNjX0P"}},5787:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={empty:"KmIjHqrMs3duMPaUf50k","subheading-content":"bpW3MfTt3siOyvw1ZsuF","list-header":"DJS67UCXVaM7nIMJePYQ","list-title":"s4378tf2MiM0bMIxQZkI","list-header__controls":"jifq7nsF5kZYrcpGQ35V","scan-navigation":"yjKVIZH3KLvJeygIzaZn"}},6852:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={"subheading-content":"WjKn66iJO4hAZYE6o9b3","product-section":"IS5K46VvD7VX5YtlwbMI","info-section":"eDfoDi2nArFY_KiYIwKh","scan-navigation":"XKUO6pX9vFbOUplFfCW2"}},2952:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={container:"fpgCv0GsKj6lv26ZC__z","toggle-section":"t3248kyG_GTPptiBJiTA","toggle-section__control":"XXSjyHsaaMnOIJluqxLZ","toggle-section__content":"CMHYvYceF9cddD0aZqkI","toggle-section__description":"j2c0RHlQpkkxt4uvBDMJ","toggle-section__info":"Lff4d__AtQ7JrFnjzqJD"}},3728:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r={"protect-header":"T1f6nRmWifz5im6hB29M","get-started-button":"Ki7ehROrWxWBIMIt8SUH"}},6281:()=>{},4997:e=>{var t=1e3,s=60*t,r=60*s,n=24*r,a=7*n,i=365.25*n;function o(e,t,s,r){var n=t>=1.5*s;return Math.round(e/s)+" "+r+(n?"s":"")}e.exports=function(e,c){c=c||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var o=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!o)return;var c=parseFloat(o[1]);switch((o[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*i;case"weeks":case"week":case"w":return c*a;case"days":case"day":case"d":return c*n;case"hours":case"hour":case"hrs":case"hr":case"h":return c*r;case"minutes":case"minute":case"mins":case"min":case"m":return c*s;case"seconds":case"second":case"secs":case"sec":case"s":return c*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(e);if("number"===l&&isFinite(e))return c.long?function(e){var a=Math.abs(e);if(a>=n)return o(e,a,n,"day");if(a>=r)return o(e,a,r,"hour");if(a>=s)return o(e,a,s,"minute");if(a>=t)return o(e,a,t,"second");return e+" ms"}(e):function(e){var a=Math.abs(e);if(a>=n)return Math.round(e/n)+"d";if(a>=r)return Math.round(e/r)+"h";if(a>=s)return Math.round(e/s)+"m";if(a>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},5762:(e,t,s)=>{"use strict";var r=s(3761);function n(){}function a(){}a.resetWarningCache=n,e.exports=function(){function e(e,t,s,n,a,i){if(i!==r){var o=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var s={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:n};return s.PropTypes=s,s}},8120:(e,t,s)=>{e.exports=s(5762)()},3761:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},372:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(6941);const n=s.n(r)()("dops:analytics");let a,i;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const o={initialize:function(e,t,s){o.setUser(e,t),o.setSuperProps(s),o.identifyUser()},setGoogleAnalyticsEnabled:function(e,t=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){i={ID:e,username:t}},setSuperProps:function(e){a=e},assignSuperProps:function(e){a=Object.assign(a||{},e)},mc:{bumpStat:function(e,t){const s=function(e,t){let s="";if("object"==typeof e){for(const t in e)s+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);n("Bumping stats %o",e)}else s="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),n('Bumping stat "%s" in group "%s"',t,e);return s}(e,t);o.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+s+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const s=function(e,t){let s="";if("object"==typeof e){for(const t in e)s+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);n("Built stats %o",e)}else s="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),n('Built stat "%s" in group "%s"',t,e);return s}(e,t);o.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+s+"&t="+Math.random())}},pageView:{record:function(e,t){o.tracks.recordPageView(e),o.ga.recordPageView(e,t)}},purchase:{record:function(e,t,s,r,n,a,i){o.ga.recordPurchase(e,t,s,r,n,a,i)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(a&&(n("- Super Props: %o",a),t=Object.assign(t,a)),n('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):n('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};o.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){o.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){n("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};o.ga.initialized||(i&&(e={userId:"u-"+i.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),o.ga.initialized=!0)},recordPageView:function(e,t){o.ga.initialize(),n("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,s,r){o.ga.initialize();let a="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==s&&(a+=" [Option Label: "+s+"]"),void 0!==r&&(a+=" [Option Value: "+r+"]"),n(a),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,s,r)},recordPurchase:function(e,t,s,r,n,a,i){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:r,currency:i}),window.ga("ecommerce:addItem",{id:e,name:t,sku:s,price:n,quantity:a}),window.ga("ecommerce:send")}},identifyUser:function(){i&&window._tkq.push(["identifyUser",i.ID,i.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},c=o},5932:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>u});var r=s(6439),n=s(3832);function a(e){class t extends Error{constructor(...t){super(...t),this.name=e}}return t}const i=a("JsonParseError"),o=a("JsonParseAfterRedirectError"),c=a("Api404Error"),l=a("Api404AfterRedirectError"),d=a("FetchNetworkError");const u=new function(e,t){let s=e,a=e,i={"X-WP-Nonce":t},o={credentials:"same-origin",headers:i},c={method:"post",credentials:"same-origin",headers:Object.assign({},i,{"Content-type":"application/json"})},l=function(e){const t=e.split("?"),s=t.length>1?t[1]:"",r=s.length?s.split("&"):[];return r.push("_cacheBuster="+(new Date).getTime()),t[0]+"?"+r.join("&")};const d={setApiRoot(e){s=e},setWpcomOriginApiUrl(e){a=e},setApiNonce(e){i={"X-WP-Nonce":e},o={credentials:"same-origin",headers:i},c={method:"post",credentials:"same-origin",headers:Object.assign({},i,{"Content-type":"application/json"})}},setCacheBusterCallback:e=>{l=e},registerSite:(e,t,n)=>{const a={};return(0,r.jetpackConfigHas)("consumer_slug")&&(a.plugin_slug=(0,r.jetpackConfigGet)("consumer_slug")),null!==t&&(a.redirect_uri=t),n&&(a.from=n),m(`${s}jetpack/v4/connection/register`,c,{body:JSON.stringify(a)}).then(h).then(p)},fetchAuthorizationUrl:e=>u((0,n.addQueryArgs)(`${s}jetpack/v4/connection/authorize_url`,{no_iframe:"1",redirect_uri:e}),o).then(h).then(p),fetchSiteConnectionData:()=>u(`${s}jetpack/v4/connection/data`,o).then(p),fetchSiteConnectionStatus:()=>u(`${s}jetpack/v4/connection`,o).then(p),fetchSiteConnectionTest:()=>u(`${s}jetpack/v4/connection/test`,o).then(h).then(p),fetchUserConnectionData:()=>u(`${s}jetpack/v4/connection/data`,o).then(p),fetchUserTrackingSettings:()=>u(`${s}jetpack/v4/tracking/settings`,o).then(h).then(p),updateUserTrackingSettings:e=>m(`${s}jetpack/v4/tracking/settings`,c,{body:JSON.stringify(e)}).then(h).then(p),disconnectSite:()=>m(`${s}jetpack/v4/connection`,c,{body:JSON.stringify({isActive:!1})}).then(h).then(p),fetchConnectUrl:()=>u(`${s}jetpack/v4/connection/url`,o).then(h).then(p),unlinkUser:(e=!1,t={})=>{const r={linked:!1,force:!!e};return t.disconnectAllUsers&&(r["disconnect-all-users"]=!0),m(`${s}jetpack/v4/connection/user`,c,{body:JSON.stringify(r)}).then(h).then(p)},reconnect:()=>m(`${s}jetpack/v4/connection/reconnect`,c).then(h).then(p),fetchConnectedPlugins:()=>u(`${s}jetpack/v4/connection/plugins`,o).then(h).then(p),setHasSeenWCConnectionModal:()=>m(`${s}jetpack/v4/seen-wc-connection-modal`,c).then(h).then(p),fetchModules:()=>u(`${s}jetpack/v4/module/all`,o).then(h).then(p),fetchModule:e=>u(`${s}jetpack/v4/module/${e}`,o).then(h).then(p),activateModule:e=>m(`${s}jetpack/v4/module/${e}/active`,c,{body:JSON.stringify({active:!0})}).then(h).then(p),deactivateModule:e=>m(`${s}jetpack/v4/module/${e}/active`,c,{body:JSON.stringify({active:!1})}),updateModuleOptions:(e,t)=>m(`${s}jetpack/v4/module/${e}`,c,{body:JSON.stringify(t)}).then(h).then(p),updateSettings:e=>m(`${s}jetpack/v4/settings`,c,{body:JSON.stringify(e)}).then(h).then(p),getProtectCount:()=>u(`${s}jetpack/v4/module/protect/data`,o).then(h).then(p),resetOptions:e=>m(`${s}jetpack/v4/options/${e}`,c,{body:JSON.stringify({reset:!0})}).then(h).then(p),activateVaultPress:()=>m(`${s}jetpack/v4/plugins`,c,{body:JSON.stringify({slug:"vaultpress",status:"active"})}).then(h).then(p),getVaultPressData:()=>u(`${s}jetpack/v4/module/vaultpress/data`,o).then(h).then(p),installPlugin:(e,t)=>{const r={slug:e,status:"active"};return t&&(r.source=t),m(`${s}jetpack/v4/plugins`,c,{body:JSON.stringify(r)}).then(h).then(p)},activateAkismet:()=>m(`${s}jetpack/v4/plugins`,c,{body:JSON.stringify({slug:"akismet",status:"active"})}).then(h).then(p),getAkismetData:()=>u(`${s}jetpack/v4/module/akismet/data`,o).then(h).then(p),checkAkismetKey:()=>u(`${s}jetpack/v4/module/akismet/key/check`,o).then(h).then(p),checkAkismetKeyTyped:e=>m(`${s}jetpack/v4/module/akismet/key/check`,c,{body:JSON.stringify({api_key:e})}).then(h).then(p),getFeatureTypeStatus:e=>u(`${s}jetpack/v4/feature/${e}`,o).then(h).then(p),fetchStatsData:e=>u(function(e){let t=`${s}jetpack/v4/module/stats/data`;-1!==t.indexOf("?")?t+=`&range=${encodeURIComponent(e)}`:t+=`?range=${encodeURIComponent(e)}`;return t}(e),o).then(h).then(p).then(f),getPluginUpdates:()=>u(`${s}jetpack/v4/updates/plugins`,o).then(h).then(p),getPlans:()=>u(`${s}jetpack/v4/plans`,o).then(h).then(p),fetchSettings:()=>u(`${s}jetpack/v4/settings`,o).then(h).then(p),updateSetting:e=>m(`${s}jetpack/v4/settings`,c,{body:JSON.stringify(e)}).then(h).then(p),fetchSiteData:()=>u(`${s}jetpack/v4/site`,o).then(h).then(p).then((e=>JSON.parse(e.data))),fetchSiteFeatures:()=>u(`${s}jetpack/v4/site/features`,o).then(h).then(p).then((e=>JSON.parse(e.data))),fetchSiteProducts:()=>u(`${s}jetpack/v4/site/products`,o).then(h).then(p),fetchSitePurchases:()=>u(`${s}jetpack/v4/site/purchases`,o).then(h).then(p).then((e=>JSON.parse(e.data))),fetchSiteBenefits:()=>u(`${s}jetpack/v4/site/benefits`,o).then(h).then(p).then((e=>JSON.parse(e.data))),fetchSiteDiscount:()=>u(`${s}jetpack/v4/site/discount`,o).then(h).then(p).then((e=>e.data)),fetchSetupQuestionnaire:()=>u(`${s}jetpack/v4/setup/questionnaire`,o).then(h).then(p),fetchRecommendationsData:()=>u(`${s}jetpack/v4/recommendations/data`,o).then(h).then(p),fetchRecommendationsProductSuggestions:()=>u(`${s}jetpack/v4/recommendations/product-suggestions`,o).then(h).then(p),fetchRecommendationsUpsell:()=>u(`${s}jetpack/v4/recommendations/upsell`,o).then(h).then(p),fetchRecommendationsConditional:()=>u(`${s}jetpack/v4/recommendations/conditional`,o).then(h).then(p),saveRecommendationsData:e=>m(`${s}jetpack/v4/recommendations/data`,c,{body:JSON.stringify({data:e})}).then(h),fetchProducts:()=>u(`${s}jetpack/v4/products`,o).then(h).then(p),fetchRewindStatus:()=>u(`${s}jetpack/v4/rewind`,o).then(h).then(p).then((e=>JSON.parse(e.data))),fetchScanStatus:()=>u(`${s}jetpack/v4/scan`,o).then(h).then(p).then((e=>JSON.parse(e.data))),dismissJetpackNotice:e=>m(`${s}jetpack/v4/notice/${e}`,c,{body:JSON.stringify({dismissed:!0})}).then(h).then(p),fetchPluginsData:()=>u(`${s}jetpack/v4/plugins`,o).then(h).then(p),fetchIntroOffers:()=>u(`${s}jetpack/v4/intro-offers`,o).then(h).then(p),fetchVerifySiteGoogleStatus:e=>u(null!==e?`${s}jetpack/v4/verify-site/google/${e}`:`${s}jetpack/v4/verify-site/google`,o).then(h).then(p),verifySiteGoogle:e=>m(`${s}jetpack/v4/verify-site/google`,c,{body:JSON.stringify({keyring_id:e})}).then(h).then(p),submitSurvey:e=>m(`${s}jetpack/v4/marketing/survey`,c,{body:JSON.stringify(e)}).then(h).then(p),saveSetupQuestionnaire:e=>m(`${s}jetpack/v4/setup/questionnaire`,c,{body:JSON.stringify(e)}).then(h).then(p),updateLicensingError:e=>m(`${s}jetpack/v4/licensing/error`,c,{body:JSON.stringify(e)}).then(h).then(p),updateLicenseKey:e=>m(`${s}jetpack/v4/licensing/set-license`,c,{body:JSON.stringify({license:e})}).then(h).then(p),getUserLicensesCounts:()=>u(`${s}jetpack/v4/licensing/user/counts`,o).then(h).then(p),getUserLicenses:()=>u(`${s}jetpack/v4/licensing/user/licenses`,o).then(h).then(p),updateLicensingActivationNoticeDismiss:e=>m(`${s}jetpack/v4/licensing/user/activation-notice-dismiss`,c,{body:JSON.stringify({last_detached_count:e})}).then(h).then(p),updateRecommendationsStep:e=>m(`${s}jetpack/v4/recommendations/step`,c,{body:JSON.stringify({step:e})}).then(h),confirmIDCSafeMode:()=>m(`${s}jetpack/v4/identity-crisis/confirm-safe-mode`,c).then(h),startIDCFresh:e=>m(`${s}jetpack/v4/identity-crisis/start-fresh`,c,{body:JSON.stringify({redirect_uri:e})}).then(h).then(p),migrateIDC:()=>m(`${s}jetpack/v4/identity-crisis/migrate`,c).then(h),attachLicenses:e=>m(`${s}jetpack/v4/licensing/attach-licenses`,c,{body:JSON.stringify({licenses:e})}).then(h).then(p),fetchSearchPlanInfo:()=>u(`${a}jetpack/v4/search/plan`,o).then(h).then(p),fetchSearchSettings:()=>u(`${a}jetpack/v4/search/settings`,o).then(h).then(p),updateSearchSettings:e=>m(`${a}jetpack/v4/search/settings`,c,{body:JSON.stringify(e)}).then(h).then(p),fetchSearchStats:()=>u(`${a}jetpack/v4/search/stats`,o).then(h).then(p),fetchWafSettings:()=>u(`${s}jetpack/v4/waf`,o).then(h).then(p),updateWafSettings:e=>m(`${s}jetpack/v4/waf`,c,{body:JSON.stringify(e)}).then(h).then(p),fetchWordAdsSettings:()=>u(`${s}jetpack/v4/wordads/settings`,o).then(h).then(p),updateWordAdsSettings:e=>m(`${s}jetpack/v4/wordads/settings`,c,{body:JSON.stringify(e)}),fetchSearchPricing:()=>u(`${a}jetpack/v4/search/pricing`,o).then(h).then(p),fetchMigrationStatus:()=>u(`${s}jetpack/v4/migration/status`,o).then(h).then(p),fetchBackupUndoEvent:()=>u(`${s}jetpack/v4/site/backup/undo-event`,o).then(h).then(p),fetchBackupPreflightStatus:()=>u(`${s}jetpack/v4/site/backup/preflight`,o).then(h).then(p)};function u(e,t){return fetch(l(e),t)}function m(e,t,s){return fetch(e,Object.assign({},t,s)).catch(g)}function f(e){return e.general&&void 0===e.general.response||e.week&&void 0===e.week.response||e.month&&void 0===e.month.response?e:{}}Object.assign(this,d)};function h(e){return e.status>=200&&e.status<300?e:404===e.status?new Promise((()=>{throw e.redirected?new l(e.redirected):new c})):e.json().catch((e=>m(e))).then((t=>{const s=new Error(`${t.message} (Status ${e.status})`);throw s.response=t,s.name="ApiError",s}))}function p(e){return e.json().catch((t=>m(t,e.redirected,e.url)))}function m(e,t,s){throw t?new o(s):new i}function g(){throw new d}},1330:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(6427),n=s(7723),a=s(991),i=s(1112),o=s(442),c=s(7425),l=s(723),d=s(3060),u=s(790);const __=n.__,h=({hideCloseButton:e=!1,title:t,children:s,step:h=null,totalSteps:p=null,buttonContent:m=null,buttonDisabled:g=!1,buttonHref:f=null,buttonExternalLink:v=!1,offset:j=32,onClose:x,onClick:y,...b})=>{const[w]=(0,o.A)("sm");if(!t||!s||!m)return null;b.position||(b.position=w?"top center":"middle right");const A={...b,offset:j,onClose:x},k=Number.isFinite(h)&&Number.isFinite(p);let C=null;return k&&(C=(0,n.sprintf)(/* translators: 1 Current step, 2 Total steps */
__("%1$d of %2$d","jetpack-protect"),h,p)),(0,u.jsx)(r.Popover,{...A,children:(0,u.jsx)(l.Ay,{children:(0,u.jsxs)("div",{className:d.A.wrapper,children:[(0,u.jsxs)("div",{className:d.A.header,children:[(0,u.jsx)(c.Ay,{variant:"title-small",className:d.A.title,children:t}),!e&&(0,u.jsx)(u.Fragment,{children:(0,u.jsx)(i.A,{size:"small",variant:"tertiary","aria-label":"close",className:d.A["close-button"],icon:a.A,iconSize:16,onClick:x})})]}),s,(0,u.jsxs)("div",{className:d.A.footer,children:[k&&(0,u.jsx)(c.Ay,{variant:"body",className:d.A.steps,children:C}),(0,u.jsx)(i.A,{variant:"primary",className:d.A["action-button"],disabled:g,onClick:y,isExternalLink:v,href:f,children:m})]})]})})})}},2947:(e,t,s)=>{"use strict";s.d(t,{A:()=>p});var r=s(5932),n=s(7723),a=s(3022),i=s(1609),o=s(8250),c=s(7142),l=s(8509),d=s(5918),u=s(532),h=s(790);const __=n.__,p=({children:e,className:t,moduleName:s=__("Jetpack","jetpack-protect"),moduleNameHref:p,showHeader:m=!0,showFooter:g=!0,useInternalLinks:f=!1,showBackground:v=!0,sandboxedDomain:j="",apiRoot:x="",apiNonce:y="",optionalMenuItems:b,header:w})=>{(0,i.useEffect)((()=>{r.Ay.setApiRoot(x),r.Ay.setApiNonce(y)}),[x,y]);const A=(0,a.A)(u.A["admin-page"],t,{[u.A.background]:v}),k=(0,i.useCallback)((async()=>{try{const e=await r.Ay.fetchSiteConnectionTest();window.alert(e.message)}catch(e){window.alert((0,n.sprintf)(/* translators: placeholder is an error message. */
__("There was an error testing Jetpack. Error: %s","jetpack-protect"),e.message))}}),[]);return(0,h.jsxs)("div",{className:A,children:[m&&(0,h.jsx)(d.A,{horizontalSpacing:5,children:(0,h.jsxs)(l.A,{className:(0,a.A)(u.A["admin-page-header"],"jp-admin-page-header"),children:[w||(0,h.jsx)(c.A,{}),j&&(0,h.jsx)("code",{className:u.A["sandbox-domain-badge"],onClick:k,onKeyDown:k,role:"button",tabIndex:0,title:`Sandboxing via ${j}. Click to test connection.`,children:"API Sandboxed"})]})}),(0,h.jsx)(d.A,{fluid:!0,horizontalSpacing:0,children:(0,h.jsx)(l.A,{children:e})}),g&&(0,h.jsx)(d.A,{horizontalSpacing:5,children:(0,h.jsx)(l.A,{children:(0,h.jsx)(o.A,{moduleName:s,moduleNameHref:p,menu:b,useInternalLinks:f})})})]})}},5640:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(2625),n=s(790);const a=({children:e})=>(0,n.jsx)("div",{className:r.A.section,children:e})},766:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5061),n=s(790);const a=({children:e})=>(0,n.jsx)("div",{className:r.A["section-hero"],children:e})},8907:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(7723),n=s(3022),a=s(790);const __=r.__,i=({title:e=__("An Automattic Airline","jetpack-protect"),height:t=7,className:s,...r})=>(0,a.jsxs)("svg",{role:"img",x:"0",y:"0",viewBox:"0 0 935 38.2",enableBackground:"new 0 0 935 38.2","aria-labelledby":"jp-automattic-byline-logo-title",height:t,className:(0,n.A)("jp-automattic-byline-logo",s),...r,children:[(0,a.jsx)("desc",{id:"jp-automattic-byline-logo-title",children:e}),(0,a.jsx)("path",{d:"M317.1 38.2c-12.6 0-20.7-9.1-20.7-18.5v-1.2c0-9.6 8.2-18.5 20.7-18.5 12.6 0 20.8 8.9 20.8 18.5v1.2C337.9 29.1 329.7 38.2 317.1 38.2zM331.2 18.6c0-6.9-5-13-14.1-13s-14 6.1-14 13v0.9c0 6.9 5 13.1 14 13.1s14.1-6.2 14.1-13.1V18.6zM175 36.8l-4.7-8.8h-20.9l-4.5 8.8h-7L157 1.3h5.5L182 36.8H175zM159.7 8.2L152 23.1h15.7L159.7 8.2zM212.4 38.2c-12.7 0-18.7-6.9-18.7-16.2V1.3h6.6v20.9c0 6.6 4.3 10.5 12.5 10.5 8.4 0 11.9-3.9 11.9-10.5V1.3h6.7V22C231.4 30.8 225.8 38.2 212.4 38.2zM268.6 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H268.6zM397.3 36.8V8.7l-1.8 3.1 -14.9 25h-3.3l-14.7-25 -1.8-3.1v28.1h-6.5V1.3h9.2l14 24.4 1.7 3 1.7-3 13.9-24.4h9.1v35.5H397.3zM454.4 36.8l-4.7-8.8h-20.9l-4.5 8.8h-7l19.2-35.5h5.5l19.5 35.5H454.4zM439.1 8.2l-7.7 14.9h15.7L439.1 8.2zM488.4 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H488.4zM537.3 6.8v30h-6.7v-30h-15.5V1.3h37.7v5.5H537.3zM569.3 36.8V4.6c2.7 0 3.7-1.4 3.7-3.4h2.8v35.5L569.3 36.8 569.3 36.8zM628 11.3c-3.2-2.9-7.9-5.7-14.2-5.7 -9.5 0-14.8 6.5-14.8 13.3v0.7c0 6.7 5.4 13 15.3 13 5.9 0 10.8-2.8 13.9-5.7l4 4.2c-3.9 3.8-10.5 7.1-18.3 7.1 -13.4 0-21.6-8.7-21.6-18.3v-1.2c0-9.6 8.9-18.7 21.9-18.7 7.5 0 14.3 3.1 18 7.1L628 11.3zM321.5 12.4c1.2 0.8 1.5 2.4 0.8 3.6l-6.1 9.4c-0.8 1.2-2.4 1.6-3.6 0.8l0 0c-1.2-0.8-1.5-2.4-0.8-3.6l6.1-9.4C318.7 11.9 320.3 11.6 321.5 12.4L321.5 12.4z"}),(0,a.jsx)("path",{d:"M37.5 36.7l-4.7-8.9H11.7l-4.6 8.9H0L19.4 0.8H25l19.7 35.9H37.5zM22 7.8l-7.8 15.1h15.9L22 7.8zM82.8 36.7l-23.3-24 -2.3-2.5v26.6h-6.7v-36H57l22.6 24 2.3 2.6V0.8h6.7v35.9H82.8z"}),(0,a.jsx)("path",{d:"M719.9 37l-4.8-8.9H694l-4.6 8.9h-7.1l19.5-36h5.6l19.8 36H719.9zM704.4 8l-7.8 15.1h15.9L704.4 8zM733 37V1h6.8v36H733zM781 37c-1.8 0-2.6-2.5-2.9-5.8l-0.2-3.7c-0.2-3.6-1.7-5.1-8.4-5.1h-12.8V37H750V1h19.6c10.8 0 15.7 4.3 15.7 9.9 0 3.9-2 7.7-9 9 7 0.5 8.5 3.7 8.6 7.9l0.1 3c0.1 2.5 0.5 4.3 2.2 6.1V37H781zM778.5 11.8c0-2.6-2.1-5.1-7.9-5.1h-13.8v10.8h14.4c5 0 7.3-2.4 7.3-5.2V11.8zM794.8 37V1h6.8v30.4h28.2V37H794.8zM836.7 37V1h6.8v36H836.7zM886.2 37l-23.4-24.1 -2.3-2.5V37h-6.8V1h6.5l22.7 24.1 2.3 2.6V1h6.8v36H886.2zM902.3 37V1H935v5.6h-26v9.2h20v5.5h-20v10.1h26V37H902.3z"})]})},4105:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(3022),n=s(8912),a=s(790);const i=({children:e,className:t,variant:s,...i})=>{const o=(0,r.A)(n.A.badge,{[n.A["is-success"]]:"success"===s,[n.A["is-warning"]]:"warning"===s,[n.A["is-danger"]]:"danger"===s},t);return(0,a.jsx)("span",{className:o,...i,children:e})}},1112:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(6427),n=s(7723),a=s(1113),i=s(3512),o=s(3022),c=s(1609),l=s(4659),d=s(790);const __=n.__,u=(0,c.forwardRef)(((e,t)=>{const{children:s,variant:n="primary",size:c="normal",weight:u="bold",icon:h,iconSize:p,disabled:m,isDestructive:g,isLoading:f,isExternalLink:v,className:j,text:x,fullWidth:y,...b}=e,w=(0,o.A)(l.A.button,j,{[l.A.normal]:"normal"===c,[l.A.small]:"small"===c,[l.A.icon]:Boolean(h),[l.A.loading]:f,[l.A.regular]:"regular"===u,[l.A["full-width"]]:y,[l.A["is-icon-button"]]:Boolean(h)&&!s});b.ref=t;const A="normal"===c?20:16,k=v&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(a.A,{size:A,icon:i.A,className:l.A["external-icon"]}),(0,d.jsx)(r.VisuallyHidden,{as:"span",children:/* translators: accessibility text */
__("(opens in a new tab)","jetpack-protect")})]}),C=v?"_blank":void 0,_=s?.[0]&&null!==s[0]&&"components-tooltip"!==s?.[0]?.props?.className;return(0,d.jsxs)(r.Button,{target:C,variant:n,className:(0,o.A)(w,{"has-text":!!h&&_}),icon:v?void 0:h,iconSize:p,disabled:m,"aria-disabled":m,isDestructive:g,text:x,...b,children:[f&&(0,d.jsx)(r.Spinner,{}),(0,d.jsx)("span",{children:s}),k]})}));u.displayName="Button";const h=u},4437:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(1113),n=s(1797),a=s(3022),i=s(597),o=s(7425),c=s(4178),l=s(790);const d=({description:e,cta:t,onClick:s,href:d,openInNewTab:u=!1,className:h,tooltipText:p=""})=>{const m=void 0!==d?"a":"button",g="a"===m?{href:d,...u&&{target:"_blank"}}:{onClick:s};return(0,l.jsxs)("div",{className:(0,a.A)(c.A.cut,h),children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(o.Ay,{className:c.A.description,children:e}),p&&(0,l.jsx)(i.A,{className:c.A.iconContainer,iconSize:16,offset:4,children:(0,l.jsx)(o.Ay,{variant:"body-small",children:p})})]}),(0,l.jsx)("div",{children:(0,l.jsx)(m,{...g,children:(0,l.jsx)(o.Ay,{className:c.A.cta,children:t})})})]}),(0,l.jsx)(r.A,{icon:n.A,className:c.A.icon,size:30})]})}},4741:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(553),n=s(790);const a=e=>e.replace(/[A-Z]/g,(e=>`_${e.toLowerCase()}`)),i=e=>null===e||"object"!=typeof e?e:Array.isArray(e)?e.map((e=>i(e))):Object.entries(e).reduce(((e,[t,s])=>(e[a(t)]=i(s),e)),{}),o=({details:e})=>e&&"object"==typeof e?(0,n.jsx)("div",{className:r.A["details-viewer"],children:Object.entries(e).map((([e,t])=>(0,n.jsxs)("div",{className:r.A["details-viewer__item"],children:[(0,n.jsxs)("div",{className:r.A["details-viewer__key"],children:[a(e),":"]}),(0,n.jsx)("div",{className:r.A["details-viewer__value"],children:(0,n.jsx)("pre",{children:"object"==typeof t&&null!==t?JSON.stringify(i(t),null,2):String(t)})})]},e)))}):null},4252:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(1609),n=s(4433),a=s(680),i=s(2904),o=s(790);const c=({oldFileName:e,newFileName:t})=>{const{prev:s,next:a}=(0,n.A)(e,t);return s.prefix+s.path===a.prefix+a.path?(0,o.jsxs)(r.Fragment,{children:[s.prefix&&(0,o.jsx)("span",{className:i.A["diff-viewer__path-prefix"],children:s.prefix}),(0,o.jsx)("span",{className:i.A["diff-viewer__path"],children:s.path})]}):(0,o.jsxs)(r.Fragment,{children:[!!s.prefix&&(0,o.jsx)("span",{className:i.A["diff-viewer__path-prefix"],children:s.prefix}),(0,o.jsx)("span",{className:i.A["diff-viewer__path"],children:s.path})," → ",!!a.prefix&&(0,o.jsx)("span",{className:i.A["diff-viewer__path-prefix"],children:a.prefix}),(0,o.jsx)("span",{className:i.A["diff-viewer__path"],children:a.path})]})},l=({diff:e})=>(0,o.jsx)("div",{className:i.A["diff-viewer"],children:(0,a.A)(e).map(((e,t)=>(0,o.jsxs)(r.Fragment,{children:[(0,o.jsx)("div",{className:i.A["diff-viewer__filename"],children:c(e)},`file-${t}`),(0,o.jsxs)("div",{className:i.A["diff-viewer__file"],children:[(0,o.jsx)("div",{className:i.A["diff-viewer__line-numbers"],children:e.hunks.map(((e,t)=>{let s=0;return e.lines.map(((r,n)=>(0,o.jsx)("div",{children:"+"===r[0]?" ":e.oldStart+s++},`${t}-${n}`)))}))},"left-numbers"),(0,o.jsx)("div",{className:i.A["diff-viewer__line-numbers"],children:e.hunks.map(((e,t)=>{let s=0;return e.lines.map(((r,n)=>(0,o.jsx)("div",{children:"-"===r[0]?" ":e.newStart+s++},`${t}-${n}`)))}))},"right-numbers"),(0,o.jsx)("div",{className:i.A["diff-viewer__lines"],children:e.hunks.map(((e,t)=>e.lines.map(((e,s)=>{const r=e.slice(1).replace(/^\s*$/," "),n=`${t}-${s}`;switch(e[0]){case" ":return(0,o.jsx)("div",{children:r},n);case"-":return(0,o.jsx)("del",{children:r},n);case"+":return(0,o.jsx)("ins",{children:r},n);default:return}}))))})]},`diff-${t}`)]},t)))})},4433:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});const r=e=>{const t=e.lastIndexOf("/");return t>-1?{prefix:e.slice(0,t),path:e.slice(t)}:{prefix:"",path:e}};function n(e,t){const s=e.startsWith("a/")&&t.startsWith("b/");if((e=s?e.slice(2):e)===(t=s?t.slice(2):t)){const{prefix:t,path:s}=r(e);return{prev:{prefix:t,path:s},next:{prefix:t,path:s}}}const n=Math.max(e.length,t.length);for(let s=0,r=0;s<n;s++)if("/"===e[s]&&"/"===t[s]&&(r=s),e[s]!==t[s])return{prev:{prefix:e.slice(0,r),path:e.slice(r)},next:{prefix:t.slice(0,r),path:t.slice(r)}};return{prev:r(e),next:r(t)}}},680:(e,t,s)=>{"use strict";function r(e){const t=e.split(/\n/),s=[];let r=0;function n(){const e={};for(s.push(e);r<t.length;){const s=t[r];if(/^(---|\+\+\+|@@)\s/.test(s))break;const n=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(s);n&&(e.index=n[1]),r++}for(a(e),a(e),e.hunks=[];r<t.length;){const s=t[r];if(/^(Index:\s|diff\s|---\s|\+\+\+\s|===================================================================)/.test(s))break;if(/^@@/.test(s))e.hunks.push(i());else{if(s)throw new Error("Unknown line "+(r+1)+" "+JSON.stringify(s));r++}}}function a(e){const s=/^(---|\+\+\+)\s+(.*)\r?$/.exec(t[r]);if(s){const t="---"===s[1]?"old":"new",n=s[2].split("\t",2);let a=n[0].replace(/\\\\/g,"\\");/^".*"$/.test(a)&&(a=a.substr(1,a.length-2)),e[t+"FileName"]=a,e[t+"Header"]=(n[1]||"").trim(),r++}}function i(){const e=r,s=t[r++].split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/),n={oldStart:+s[1],oldLines:void 0===s[2]?1:+s[2],newStart:+s[3],newLines:void 0===s[4]?1:+s[4],lines:[]};0===n.oldLines&&(n.oldStart+=1),0===n.newLines&&(n.newStart+=1);let a,i=0,o=0;for(;r<t.length&&(o<n.oldLines||i<n.newLines||null!==(a=t[r])&&void 0!==a&&a.startsWith("\\"));r++){const s=0===t[r].length&&r!==t.length-1?" ":t[r][0];if("+"!==s&&"-"!==s&&" "!==s&&"\\"!==s)throw new Error(`Hunk at line ${e+1} contained invalid line ${t[r]}`);n.lines.push(t[r]),"+"===s?i++:"-"===s?o++:" "===s&&(i++,o++)}if(i||1!==n.newLines||(n.newLines=0),o||1!==n.oldLines||(n.oldLines=0),i!==n.newLines)throw new Error("Added line count did not match for hunk at line "+(e+1));if(o!==n.oldLines)throw new Error("Removed line count did not match for hunk at line "+(e+1));return n}for(;r<t.length;)n();return s}s.d(t,{A:()=>r})},1883:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(7723),n=s(3022),a=s(1609),i=(s(5618),s(790));const __=r.__;class o extends a.Component{static defaultProps={"aria-hidden":"false",focusable:"true"};needsOffset(e,t){return["gridicons-arrow-left","gridicons-arrow-right","gridicons-calendar","gridicons-cart","gridicons-folder","gridicons-help-outline","gridicons-info","gridicons-info-outline","gridicons-posts","gridicons-star-outline","gridicons-star"].indexOf(e)>=0&&t%18==0}getSVGDescription(e){if("description"in this.props)return this.props.description;switch(e){default:return"";case"gridicons-audio":return __("Has audio.","jetpack-protect");case"gridicons-arrow-left":return __("Arrow left","jetpack-protect");case"gridicons-arrow-right":return __("Arrow right","jetpack-protect");case"gridicons-calendar":return __("Is an event.","jetpack-protect");case"gridicons-cart":return __("Is a product.","jetpack-protect");case"chevron-down":return __("Show filters","jetpack-protect");case"gridicons-comment":return __("Matching comment.","jetpack-protect");case"gridicons-cross":return __("Close.","jetpack-protect");case"gridicons-filter":return __("Toggle search filters.","jetpack-protect");case"gridicons-folder":return __("Category","jetpack-protect");case"gridicons-help-outline":return __("Help","jetpack-protect");case"gridicons-info":case"gridicons-info-outline":return __("Information.","jetpack-protect");case"gridicons-image-multiple":return __("Has multiple images.","jetpack-protect");case"gridicons-image":return __("Has an image.","jetpack-protect");case"gridicons-page":return __("Page","jetpack-protect");case"gridicons-post":return __("Post","jetpack-protect");case"gridicons-jetpack-search":case"gridicons-search":return __("Magnifying Glass","jetpack-protect");case"gridicons-tag":return __("Tag","jetpack-protect");case"gridicons-video":return __("Has a video.","jetpack-protect")}}renderIcon(e){switch(e){default:return null;case"gridicons-audio":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M8 4v10.184C7.686 14.072 7.353 14 7 14c-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3V7h7v4.184c-.314-.112-.647-.184-1-.184-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3V4H8z"})});case"gridicons-arrow-left":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"})});case"gridicons-arrow-right":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8-8-8z"})});case"gridicons-block":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zM4 12c0-4.418 3.582-8 8-8 1.848 0 3.545.633 4.9 1.686L5.686 16.9C4.633 15.545 4 13.848 4 12zm8 8c-1.848 0-3.546-.633-4.9-1.686L18.314 7.1C19.367 8.455 20 10.152 20 12c0 4.418-3.582 8-8 8z"})});case"gridicons-calendar":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M19 4h-1V2h-2v2H8V2H6v2H5c-1.105 0-2 .896-2 2v13c0 1.104.895 2 2 2h14c1.104 0 2-.896 2-2V6c0-1.104-.896-2-2-2zm0 15H5V8h14v11z"})});case"gridicons-cart":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M9 20c0 1.1-.9 2-2 2s-1.99-.9-1.99-2S5.9 18 7 18s2 .9 2 2zm8-2c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2zm.396-5c.937 0 1.75-.65 1.952-1.566L21 5H7V4c0-1.105-.895-2-2-2H3v2h2v11c0 1.105.895 2 2 2h12c0-1.105-.895-2-2-2H7v-2h10.396z"})});case"gridicons-checkmark":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M11 17.768l-4.884-4.884 1.768-1.768L11 14.232l8.658-8.658C17.823 3.39 15.075 2 12 2 6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10c0-1.528-.353-2.97-.966-4.266L11 17.768z"})});case"gridicons-chevron-left":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M16.443 7.41L15.0399 6L9.06934 12L15.0399 18L16.443 16.59L11.8855 12L16.443 7.41Z"})});case"gridicons-chevron-right":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M10.2366 6L8.8335 7.41L13.391 12L8.8335 16.59L10.2366 18L16.2072 12L10.2366 6Z"})});case"gridicons-chevron-down":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M20 9l-8 8-8-8 1.414-1.414L12 14.172l6.586-6.586"})});case"gridicons-comment":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M3 6v9c0 1.105.895 2 2 2h9v5l5.325-3.804c1.05-.75 1.675-1.963 1.675-3.254V6c0-1.105-.895-2-2-2H5c-1.105 0-2 .895-2 2z"})});case"gridicons-computer":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M20 2H4c-1.104 0-2 .896-2 2v12c0 1.104.896 2 2 2h6v2H7v2h10v-2h-3v-2h6c1.104 0 2-.896 2-2V4c0-1.104-.896-2-2-2zm0 14H4V4h16v12z"})});case"gridicons-cross":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M18.36 19.78L12 13.41l-6.36 6.37-1.42-1.42L10.59 12 4.22 5.64l1.42-1.42L12 10.59l6.36-6.36 1.41 1.41L13.41 12l6.36 6.36z"})});case"gridicons-filter":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M10 19h4v-2h-4v2zm-4-6h12v-2H6v2zM3 5v2h18V5H3z"})});case"gridicons-folder":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M18 19H6c-1.1 0-2-.9-2-2V7c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2h7c1.1 0 2 .9 2 2v8c0 1.1-.9 2-2 2z"})});case"gridicons-help-outline":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8-8-3.59-8-8 3.59-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-1.962-2v-.528c0-.4.082-.74.246-1.017.163-.276.454-.546.87-.808.333-.21.572-.397.717-.565.146-.168.22-.36.22-.577 0-.172-.078-.308-.234-.41-.156-.1-.358-.15-.608-.15-.62 0-1.34.22-2.168.658l-.854-1.67c1.02-.58 2.084-.872 3.194-.872.913 0 1.63.202 2.15.603.52.4.78.948.78 1.64 0 .495-.116.924-.347 1.287-.23.362-.6.705-1.11 1.03-.43.278-.7.48-.807.61-.108.13-.163.282-.163.458V13h-1.885z"})});case"gridicons-image":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M13 9.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5-.672 1.5-1.5 1.5-1.5-.672-1.5-1.5zM22 6v12c0 1.105-.895 2-2 2H4c-1.105 0-2-.895-2-2V6c0-1.105.895-2 2-2h16c1.105 0 2 .895 2 2zm-2 0H4v7.444L8 9l5.895 6.55 1.587-1.85c.798-.932 2.24-.932 3.037 0L20 15.426V6z"})});case"gridicons-image-multiple":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M15 7.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5S17.328 9 16.5 9 15 8.328 15 7.5zM4 20h14c0 1.105-.895 2-2 2H4c-1.1 0-2-.9-2-2V8c0-1.105.895-2 2-2v14zM22 4v12c0 1.105-.895 2-2 2H8c-1.105 0-2-.895-2-2V4c0-1.105.895-2 2-2h12c1.105 0 2 .895 2 2zM8 4v6.333L11 7l4.855 5.395.656-.73c.796-.886 2.183-.886 2.977 0l.513.57V4H8z"})});case"gridicons-info":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"})});case"gridicons-info-outline":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M13 9h-2V7h2v2zm0 2h-2v6h2v-6zm-1-7c-4.411 0-8 3.589-8 8s3.589 8 8 8 8-3.589 8-8-3.589-8-8-8m0-2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"})});case"gridicons-jetpack-search":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M0 9.257C0 4.15 4.151 0 9.257 0c5.105 0 9.256 4.151 9.256 9.257a9.218 9.218 0 01-2.251 6.045l.034.033h1.053L24 22.01l-1.986 1.989-6.664-6.662v-1.055l-.033-.033a9.218 9.218 0 01-6.06 2.264C4.15 18.513 0 14.362 0 9.257zm4.169 1.537h4.61V1.82l-4.61 8.973zm5.547-3.092v8.974l4.61-8.974h-4.61z"})});case"gridicons-phone":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M16 2H8c-1.104 0-2 .896-2 2v16c0 1.104.896 2 2 2h8c1.104 0 2-.896 2-2V4c0-1.104-.896-2-2-2zm-3 19h-2v-1h2v1zm3-2H8V5h8v14z"})});case"gridicons-pages":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M16 8H8V6h8v2zm0 2H8v2h8v-2zm4-6v12l-6 6H6c-1.105 0-2-.895-2-2V4c0-1.105.895-2 2-2h12c1.105 0 2 .895 2 2zm-2 10V4H6v16h6v-4c0-1.105.895-2 2-2h4z"})});case"gridicons-posts":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M16 19H3v-2h13v2zm5-10H3v2h18V9zM3 5v2h11V5H3zm14 0v2h4V5h-4zm-6 8v2h10v-2H11zm-8 0v2h5v-2H3z"})});case"gridicons-search":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M21 19l-5.154-5.154C16.574 12.742 17 11.42 17 10c0-3.866-3.134-7-7-7s-7 3.134-7 7 3.134 7 7 7c1.42 0 2.742-.426 3.846-1.154L19 21l2-2zM5 10c0-2.757 2.243-5 5-5s5 2.243 5 5-2.243 5-5 5-5-2.243-5-5z"})});case"gridicons-star-outline":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M12 6.308l1.176 3.167.347.936.997.042 3.374.14-2.647 2.09-.784.62.27.963.91 3.25-2.813-1.872-.83-.553-.83.552-2.814 1.87.91-3.248.27-.962-.783-.62-2.648-2.092 3.374-.14.996-.04.347-.936L12 6.308M12 2L9.418 8.953 2 9.257l5.822 4.602L5.82 21 12 16.89 18.18 21l-2.002-7.14L22 9.256l-7.418-.305L12 2z"})});case"gridicons-star":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M12 2l2.582 6.953L22 9.257l-5.822 4.602L18.18 21 12 16.89 5.82 21l2.002-7.14L2 9.256l7.418-.304"})});case"gridicons-tag":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M20 2.007h-7.087c-.53 0-1.04.21-1.414.586L2.592 11.5c-.78.78-.78 2.046 0 2.827l7.086 7.086c.78.78 2.046.78 2.827 0l8.906-8.906c.376-.374.587-.883.587-1.413V4.007c0-1.105-.895-2-2-2zM17.007 9c-1.105 0-2-.895-2-2s.895-2 2-2 2 .895 2 2-.895 2-2 2z"})});case"gridicons-video":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M20 4v2h-2V4H6v2H4V4c-1.105 0-2 .895-2 2v12c0 1.105.895 2 2 2v-2h2v2h12v-2h2v2c1.105 0 2-.895 2-2V6c0-1.105-.895-2-2-2zM6 16H4v-3h2v3zm0-5H4V8h2v3zm4 4V9l4.5 3-4.5 3zm10 1h-2v-3h2v3zm0-5h-2V8h2v3z"})});case"gridicons-lock":return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("g",{id:"lock",children:(0,i.jsx)("path",{d:"M18,8h-1V7c0-2.757-2.243-5-5-5S7,4.243,7,7v1H6c-1.105,0-2,0.895-2,2v10c0,1.105,0.895,2,2,2h12c1.105,0,2-0.895,2-2V10 C20,8.895,19.105,8,18,8z M9,7c0-1.654,1.346-3,3-3s3,1.346,3,3v1H9V7z M13,15.723V18h-2v-2.277c-0.595-0.346-1-0.984-1-1.723 c0-1.105,0.895-2,2-2s2,0.895,2,2C14,14.738,13.595,15.376,13,15.723z"})}),(0,i.jsx)("g",{id:"Layer_1"})]});case"gridicons-external":return(0,i.jsx)("g",{children:(0,i.jsx)("path",{d:"M19 13v6c0 1.105-.895 2-2 2H5c-1.105 0-2-.895-2-2V7c0-1.105.895-2 2-2h6v2H5v12h12v-6h2zM13 3v2h4.586l-7.793 7.793 1.414 1.414L19 6.414V11h2V3h-8z"})})}}render(){const{size:e=24,className:t=""}=this.props,s=this.props.height||e,r=this.props.width||e,a=this.props.style||{height:s,width:r},o="gridicons-"+this.props.icon,c=(0,n.A)("gridicon",o,t,{"needs-offset":this.needsOffset(o,e)}),l=this.getSVGDescription(o);return(0,i.jsxs)("svg",{className:c,focusable:this.props.focusable,height:s,onClick:this.props.onClick,style:a,viewBox:"0 0 24 24",width:r,xmlns:"http://www.w3.org/2000/svg","aria-hidden":this.props["aria-hidden"],children:[l?(0,i.jsx)("desc",{children:l}):null,this.renderIcon(o)]})}}const c=o},597:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(6427),n=s(3022),a=s(1609),i=s(1112),o=s(1883),c=(s(4984),s(790));const l=e=>({"top-end":"top left",top:"top center","top-start":"top right","bottom-end":"bottom left",bottom:"bottom center","bottom-start":"bottom right"}[e]),d=({className:e="",iconClassName:t="",placement:s="bottom-end",animate:d=!0,iconCode:u="info-outline",iconSize:h=18,offset:p=10,title:m,children:g,popoverAnchorStyle:f="icon",forceShow:v=!1,hoverShow:j=!1,wide:x=!1,inline:y=!0,shift:b=!1})=>{const[w,A]=(0,a.useState)(!1),[k,C]=(0,a.useState)(null),_=(0,a.useCallback)((()=>A(!1)),[A]),S=(0,a.useCallback)((e=>{e.preventDefault(),A(!w)}),[w,A]),N={position:l(s),placement:s,animate:d,noArrow:!1,resize:!1,flip:!1,offset:p,focusOnMount:"container",onClose:_,className:"icon-tooltip-container",inline:y,shift:b},M="wrapper"===f,L=(0,n.A)("icon-tooltip-wrapper",e),E={left:M?0:-(62-h/2)+"px"},P=M&&v,R=(0,a.useCallback)((()=>{j&&(k&&(clearTimeout(k),C(null)),A(!0))}),[j,k]),z=(0,a.useCallback)((()=>{if(j){const e=setTimeout((()=>{A(!1),C(null)}),100);C(e)}}),[j]);return(0,c.jsxs)("div",{className:L,"data-testid":"icon-tooltip_wrapper",onMouseEnter:R,onMouseLeave:z,children:[!M&&(0,c.jsx)(i.A,{variant:"link",onMouseDown:S,children:(0,c.jsx)(o.A,{className:t,icon:u,size:h})}),(0,c.jsx)("div",{className:(0,n.A)("icon-tooltip-helper",{"is-wide":x}),style:E,children:(P||w)&&(0,c.jsx)(r.Popover,{...N,children:(0,c.jsxs)("div",{children:[m&&(0,c.jsx)("div",{className:"icon-tooltip-title",children:m}),(0,c.jsx)("div",{className:"icon-tooltip-content",children:g})]})})})]})}},8478:(e,t,s)=>{"use strict";s.d(t,{Wy:()=>l});var r=s(6427),n=s(3022),a=(s(4705),s(9723)),i=s(790);const o=({className:e,size:t=24,viewBox:s="0 0 24 24",opacity:o=1,color:c="#2C3338",children:l})=>{const d={className:(0,n.A)(a.A.iconWrapper,e),width:t,height:t,viewBox:s,opacity:o,fill:void 0};return c&&(d.fill=c),(0,i.jsx)(r.SVG,{...d,fillRule:"evenodd",clipRule:"evenodd",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)(r.G,{opacity:o,children:l})})},c={...{"anti-spam":({opacity:e=1,size:t,color:s})=>(0,i.jsxs)(o,{size:t,opacity:e,color:s,children:[(0,i.jsx)(r.Path,{d:"M13.2,4.7l4.7,12.8c0.4,1.1,1,1.5,2.1,1.6c0.1,0,0.1,0,0.1,0l0.1,0.1l0.1,0.1c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0,0.1 s0,0.1-0.1,0.1c-0.1,0-0.1,0.1-0.1,0.1s-0.1,0-0.2,0h-5.1c-0.1,0-0.1,0-0.2,0c-0.1,0-0.1-0.1-0.1-0.1l-0.1-0.1c0-0.1,0-0.1,0-0.1 c0-0.1,0-0.1,0-0.2s0-0.1,0.1-0.1l0.1-0.1c0,0,0.1,0,0.2,0c0.5,0,1.1-0.2,1.1-0.8c0-0.3-0.1-0.5-0.2-0.8l-1.1-3.1 c-0.1-0.2-0.1-0.2-0.2-0.2h-4.3c-0.7,0-1.5,0-1.9,0.9l-1.1,2.4C7.1,17.6,7,17.8,7,18.1c0,0.8,1,0.9,1.6,0.9c0.1,0,0.1,0,0.2,0 L8.8,19l0.1,0.1c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0,0.1s-0.1,0.1-0.1,0.1l-0.1,0.1c-0.1,0-0.1,0-0.2,0H4.1c-0.1,0-0.1,0-0.1,0 c-0.1,0-0.1-0.1-0.1-0.1l-0.1-0.1c0-0.1,0-0.1,0-0.1c0-0.1,0-0.1,0-0.2s0-0.1,0.1-0.1L4,19c0,0,0.1,0,0.1,0C5.2,19,5.5,18.5,6,17.5 l5.4-12.4c0.2-0.5,0.8-1,1.3-1C13,4.2,13.1,4.4,13.2,4.7z M9.1,13.1c0,0.1-0.1,0.1-0.1,0.2c0,0.1,0.1,0.1,0.1,0.1h4.4 c0.3,0,0.4-0.1,0.4-0.3c0-0.1,0-0.2-0.1-0.3l-1.2-3.5c-0.3-0.8-0.8-1.9-0.8-2.7c0-0.1,0-0.1-0.1-0.1c0,0-0.1,0-0.1,0.1 c-0.1,0.6-0.4,1.2-0.7,1.7L9.1,13.1z"}),(0,i.jsx)(r.Path,{d:"M13.2,4.7l4.7,12.8c0.4,1.1,1,1.5,2.1,1.6c0.1,0,0.1,0,0.1,0l0.1,0.1l0.1,0.1c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0,0.1 s0,0.1-0.1,0.1c-0.1,0-0.1,0.1-0.1,0.1s-0.1,0-0.2,0h-5.1c-0.1,0-0.1,0-0.2,0c-0.1,0-0.1-0.1-0.1-0.1l-0.1-0.1c0-0.1,0-0.1,0-0.1 c0-0.1,0-0.1,0-0.2s0-0.1,0.1-0.1l0.1-0.1c0,0,0.1,0,0.2,0c0.5,0,1.1-0.2,1.1-0.8c0-0.3-0.1-0.5-0.2-0.8l-1.1-3.1 c-0.1-0.2-0.1-0.2-0.2-0.2h-4.3c-0.7,0-1.5,0-1.9,0.9l-1.1,2.4C7.1,17.6,7,17.8,7,18.1c0,0.8,1,0.9,1.6,0.9c0.1,0,0.1,0,0.2,0 L8.8,19l0.1,0.1c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0,0.1s-0.1,0.1-0.1,0.1l-0.1,0.1c-0.1,0-0.1,0-0.2,0H4.1c-0.1,0-0.1,0-0.1,0 c-0.1,0-0.1-0.1-0.1-0.1l-0.1-0.1c0-0.1,0-0.1,0-0.1c0-0.1,0-0.1,0-0.2s0-0.1,0.1-0.1L4,19c0,0,0.1,0,0.1,0C5.2,19,5.5,18.5,6,17.5 l5.4-12.4c0.2-0.5,0.8-1,1.3-1C13,4.2,13.1,4.4,13.2,4.7z M9.1,13.1c0,0.1-0.1,0.1-0.1,0.2c0,0.1,0.1,0.1,0.1,0.1h4.4 c0.3,0,0.4-0.1,0.4-0.3c0-0.1,0-0.2-0.1-0.3l-1.2-3.5c-0.3-0.8-0.8-1.9-0.8-2.7c0-0.1,0-0.1-0.1-0.1c0,0-0.1,0-0.1,0.1 c-0.1,0.6-0.4,1.2-0.7,1.7L9.1,13.1z"}),(0,i.jsx)(r.Path,{d:"M21.6,12.5c0,0.6-0.3,1-0.9,1c-0.6,0-0.8-0.3-0.8-0.8c0-0.6,0.4-1,0.9-1C21.3,11.7,21.6,12.1,21.6,12.5z"}),(0,i.jsx)(r.Path,{d:"M4.1,12.5c0,0.6-0.3,1-0.9,1s-0.8-0.3-0.8-0.8c0-0.6,0.4-1,0.9-1S4.1,12.1,4.1,12.5z"})]}),backup:({opacity:e=1,size:t,color:s})=>(0,i.jsx)(o,{size:t,opacity:e,color:s,children:(0,i.jsx)(r.Path,{d:"M2.1,5.8c0-0.1,0-0.1,0-0.2c0-0.2,0.1-0.5,0.1-0.7c0.1-0.4,0.4-0.6,0.7-0.8l8.3-2.9c0.1-0.1,0.3-0.1,0.4-0.1l0.5,0.1 l8.3,2.9c0.3,0.2,0.5,0.4,0.7,0.7c0.2,0.2,0.2,0.4,0.2,0.7c0,0.1,0,0.1,0,0.2v0.1c-0.1,0.5-0.2,0.9-0.3,1.4 c-0.2,0.4-0.3,1.2-0.7,2.2c-0.3,1-0.7,2.1-1.1,3.1c-0.5,1-1,2.1-1.6,3.3s-1.4,2.3-2.2,3.5c-0.9,1.1-1.8,2.2-2.8,3.1 c-0.2,0.2-0.5,0.4-0.9,0.4c-0.3,0-0.6-0.1-0.9-0.4c-1.2-1.1-2.4-2.4-3.5-4c-1-1.6-1.9-3-2.5-4.3c-0.6-1.3-1.1-2.7-1.6-4 C2.8,8.7,2.5,7.6,2.3,7C2.3,6.5,2.1,6.1,2.1,5.8z M2.9,5.9c0,0.2,0.1,0.4,0.1,0.8C3.1,7,3.2,7.5,3.5,8.2C3.7,9,3.9,9.7,4.2,10.6 c0.3,0.7,0.7,1.7,1.1,2.7c0.4,1,1,2,1.5,2.9c0.5,1,1.2,1.9,1.9,2.9c0.8,1,1.6,1.9,2.4,2.6c0.2,0.2,0.4,0.2,0.5,0.2 c0.2,0,0.4-0.1,0.5-0.2c1.2-1,2.2-2.3,3.2-3.8c1-1.5,1.8-2.8,2.3-4c0.6-1.3,1.1-2.5,1.5-3.9c0.4-1.3,0.7-2.2,0.9-2.8 c0.1-0.5,0.2-1,0.3-1.3c0-0.1,0-0.1,0-0.1c0-0.2,0-0.3-0.1-0.4C20.3,5.2,20.2,5.1,20,5L12,2.1c0,0-0.1,0-0.2,0s-0.1,0-0.1,0h-0.2 l-8,2.8C3.2,5,3.1,5.2,3,5.3C2.9,5.5,2.9,5.6,2.9,5.8C2.9,5.8,2.9,5.8,2.9,5.9z M5.9,6.7h3l2.8,7l2.8-7h3c-0.1,0.1-0.2,0.5-0.3,0.8 C17,7.8,17,8.2,16.8,8.4c-0.1,0.3-0.2,0.5-0.4,0.8c0,0.1-0.1,0.1-0.1,0.1s-0.1,0.1-0.2,0.1c-0.1,0-0.1,0-0.1,0 c-0.1,0-0.2,0.1-0.2,0.2c0,0-0.1,0.1-0.1,0.1s-0.1,0.1-0.1,0.1c0,0,0,0.1-0.1,0.2c0,0.1-0.1,0.1-0.1,0.1l-0.4,1.1 c-1.3,3.3-2.1,5.2-2.3,5.8h-2.2l-1-2.4c-0.1-0.3-0.3-0.8-0.5-1.3c-0.1-0.3-0.3-0.8-0.5-1.3L8,10.8c-0.1-0.1-0.1-0.2-0.1-0.4 C7.8,10.2,7.7,10,7.7,9.8C7.6,9.7,7.5,9.5,7.4,9.4C7.3,9.3,7.3,9.3,7.3,9.3c-0.1,0-0.2,0-0.2,0s-0.1,0-0.1,0 C6.6,8.5,6.3,7.6,5.9,6.7z"})}),boost:({opacity:e=1,size:t,color:s})=>(0,i.jsx)(o,{size:t,opacity:e,color:s,children:(0,i.jsx)(r.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M4.19505 16.2545C4.47368 16.561 4.94802 16.5836 5.25451 16.3049L10.2595 11.7549L14.2842 15.2765L19 10.5607V13.75H20.5V9.5V8.75239V8.7476V8H19.7529H19.7471H19H14.75V9.5H17.9393L14.2158 13.2235L10.2405 9.74507L4.2455 15.195C3.93901 15.4737 3.91642 15.948 4.19505 16.2545Z"})}),crm:({opacity:e=1,size:t,color:s})=>(0,i.jsx)(o,{size:t,opacity:e,color:s,children:(0,i.jsx)(r.Path,{d:"M15.5 9.5a1 1 0 1 0 0-2 1 1 0 0 0 0 2Zm0 1.5a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm-2.25 6v-2a2.75 2.75 0 0 0-2.75-2.75h-4A2.75 2.75 0 0 0 3.75 15v2h1.5v-2c0-.69.56-1.25 1.25-1.25h4c.69 0 1.25.56 1.25 1.25v2h1.5Zm7-2v2h-1.5v-2c0-.69-.56-1.25-1.25-1.25H15v-1.5h2.5A2.75 2.75 0 0 1 20.25 15ZM9.5 8.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm1.5 0a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0Z"})}),extras:({opacity:e=1,size:t,color:s})=>(0,i.jsx)(o,{size:t,opacity:e,color:s,children:(0,i.jsx)(r.Path,{d:"M18.5 5.5V8H20V5.5h2.5V4H20V1.5h-1.5V4H16v1.5h2.5ZM12 4H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-6h-1.5v6a.5.5 0 0 1-.5.5H6a.5.5 0 0 1-.5-.5V6a.5.5 0 0 1 .5-.5h6V4Z"})}),protect:({opacity:e=1,size:t,className:s,color:n})=>(0,i.jsxs)(o,{className:s,size:t,opacity:e,color:n,children:[(0,i.jsx)(r.Path,{d:"M12 3.17627L18.75 6.24445V10.8183C18.75 14.7173 16.2458 18.4089 12.7147 19.5735C12.2507 19.7265 11.7493 19.7265 11.2853 19.5735C7.75416 18.4089 5.25 14.7173 5.25 10.8183V6.24445L12 3.17627ZM6.75 7.21032V10.8183C6.75 14.1312 8.89514 17.2057 11.7551 18.149C11.914 18.2014 12.086 18.2014 12.2449 18.149C15.1049 17.2057 17.25 14.1312 17.25 10.8183V7.21032L12 4.82396L6.75 7.21032Z"}),(0,i.jsx)(r.Path,{d:"M15.5291 10.0315L11.1818 14.358L8.47095 11.66L9.52907 10.5968L11.1818 12.2417L14.4709 8.96826L15.5291 10.0315Z"})]}),scan:({opacity:e=1,size:t,color:s})=>(0,i.jsx)(o,{size:t,opacity:e,color:s,children:(0,i.jsx)(r.Path,{d:"m12 3.176 6.75 3.068v4.574c0 3.9-2.504 7.59-6.035 8.755a2.283 2.283 0 0 1-1.43 0c-3.53-1.164-6.035-4.856-6.035-8.755V6.244L12 3.176ZM6.75 7.21v3.608c0 3.313 2.145 6.388 5.005 ************.331.053.49 0 2.86-.942 5.005-4.017 5.005-7.33V7.21L12 4.824 6.75 7.21Z"})}),search:({opacity:e=1,size:t,color:s})=>(0,i.jsx)(o,{size:t,opacity:e,color:s,children:(0,i.jsx)(r.Path,{d:"M17.5 11.5a4 4 0 1 1-8 0 4 4 0 0 1 8 0Zm1.5 0a5.5 5.5 0 0 1-9.142 4.121l-3.364 2.943-.988-1.128 3.373-2.952A5.5 5.5 0 1 1 19 11.5Z"})}),social:({opacity:e=1,size:t,color:s})=>(0,i.jsx)(o,{size:t,opacity:e,color:s,children:(0,i.jsx)(r.Path,{d:"M15.5 3.97809V18.0219L7.5 15.5977V20H6V15.1431L3.27498 14.3173C2.22086 13.9979 1.5 13.0262 1.5 11.9248V10.0752C1.5 8.97375 2.22087 8.00207 3.27498 7.68264L15.5 3.97809ZM14 16L7.5 14.0303L7.5 7.96969L14 5.99999V16ZM6 8.42423L6 13.5757L3.70999 12.8818C3.28835 12.754 3 12.3654 3 11.9248V10.0752C3 9.63462 3.28835 9.24595 3.70999 9.11818L6 8.42423ZM17.5 11.75H21.5V10.25H17.5V11.75ZM21.5 16L17.5 15V13.5L21.5 14.5V16ZM17.5 8.5L21.5 7.5V6L17.5 7V8.5Z"})}),star:({size:e,className:t=a.A["star-icon"],color:s})=>(0,i.jsx)(o,{className:t,size:e,color:s,children:(0,i.jsx)(r.Path,{d:"M12 2l2.582 6.953L22 9.257l-5.822 4.602L18.18 21 12 16.89 5.82 21l2.002-7.14L2 9.256l7.418-.304"})}),videopress:({opacity:e=1,size:t,color:s})=>(0,i.jsx)(o,{size:t,opacity:e,color:s,children:(0,i.jsx)(r.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M4.3,6.2c0.8,0,1.6,0.6,1.8,1.4l2.3,7.9c0,0,0,0,0,0l2.7-9.3h1.5h4.2c2.9,0,4.9,1.9,4.9,4.7c0,2.9-2,4.7-5,4.7 h-2h-2.5l-0.5,1.5c-0.4,1.4-1.7,2.3-3.2,2.3c-1.4,0-2.7-0.9-3.2-2.3L2.5,8.7C2.1,7.4,3,6.2,4.3,6.2z M13,12.8h2.9c1.3,0,2-0.7,2-1.9 c0-1.2-0.8-1.8-2-1.8h-1.7L13,12.8z"})}),jetpack:({size:e,className:t=a.A.jetpack,color:s})=>(0,i.jsxs)(o,{className:t,size:e,color:s,viewBox:"0 0 32 32",children:[(0,i.jsx)(r.Path,{className:"jetpack-logo__icon-circle",d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z"}),(0,i.jsx)(r.Polygon,{fill:"#fff",points:"15,19 7,19 15,3"}),(0,i.jsx)(r.Polygon,{fill:"#fff",points:"17,29 17,13 25,13"})]}),share:({size:e=16,className:t,color:s})=>(0,i.jsx)(o,{className:t,size:e,color:s,viewBox:"0 0 16 16",children:(0,i.jsx)(r.Path,{fill:"#161722",fillRule:"evenodd",d:"M8.3 4.66C3.85 5.308.727 9.75.034 13.69l-.02.117c-.137.842.809 1.232 1.446.68 2.013-1.745 3.648-2.475 5.318-2.719a10.482 10.482 0 011.524-.103v2.792c0 .694.82 1.041 1.3.55l6.176-6.307a.79.79 0 00.012-1.088L9.614 1.004C9.14.496 8.301.84 8.301 1.542v3.117zm1.525-1.175v1.85a.773.773 0 01-.654.77l-.655.096c-2.133.311-3.987 1.732-5.295 3.672-.472.7-.854 1.44-1.143 2.18a12.32 12.32 0 011.675-.972c1.58-.75 3.048-.972 4.548-.972h.762a.77.77 0 01.762.779v1.69l4.347-4.44-4.347-4.653z",clipRule:"evenodd"})}),ai:({size:e=24,color:t="#069e08"})=>(0,i.jsxs)(o,{color:t,size:e,viewBox:"0 0 32 32",children:[(0,i.jsx)(r.Path,{className:"spark-first",d:"M9.33301 5.33325L10.4644 8.20188L13.333 9.33325L10.4644 10.4646L9.33301 13.3333L8.20164 10.4646L5.33301 9.33325L8.20164 8.20188L9.33301 5.33325Z"}),(0,i.jsx)(r.Path,{className:"spark-second",d:"M21.3333 5.33333L22.8418 9.15817L26.6667 10.6667L22.8418 12.1752L21.3333 16L19.8248 12.1752L16 10.6667L19.8248 9.15817L21.3333 5.33333Z"}),(0,i.jsx)(r.Path,{className:"spark-third",d:"M14.6667 13.3333L16.5523 18.1144L21.3333 20L16.5523 21.8856L14.6667 26.6667L12.781 21.8856L8 20L12.781 18.1144L14.6667 13.3333Z"})]}),stats:({opacity:e=1,size:t,color:s})=>(0,i.jsx)(o,{size:t,opacity:e,color:s,children:(0,i.jsx)(r.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M11.25 5H12.75V20H11.25V5ZM6 10H7.5V20H6V10ZM18 14H16.5V20H18V14Z"})})}};function l(e){return c[e]?c[e]:null}},8250:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var r=s(7723),n=s(1113),a=s(3512),i=s(3022),o=s(3924),c=s(1069),l=s(8907),d=(s(2997),s(7142)),u=s(442),h=s(790);const __=r.__,_x=r._x,p=()=>(0,h.jsx)(d.A,{logoColor:"#000",showText:!1,height:16,"aria-hidden":"true"}),m=()=>(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(n.A,{icon:a.A,size:16}),(0,h.jsx)("span",{className:"jp-dashboard-footer__accessible-external-link",children:/* translators: accessibility text */
__("(opens in a new tab)","jetpack-protect")})]}),g=({moduleName:e=__("Jetpack","jetpack-protect"),className:t,moduleNameHref:s="https://jetpack.com",menu:r,useInternalLinks:n,onAboutClick:a,onPrivacyClick:d,onTermsClick:g,...f})=>{const[v]=(0,u.A)("sm","<="),[j]=(0,u.A)("md","<="),[x]=(0,u.A)("lg",">"),y=(0,c.A)();let b=[{label:_x("About","Link to learn more about Jetpack.","jetpack-protect"),title:__("About Jetpack","jetpack-protect"),href:n?new URL("admin.php?page=jetpack_about",y).href:(0,o.A)("jetpack-about"),target:n?"_self":"_blank",onClick:a},{label:_x("Privacy","Shorthand for Privacy Policy.","jetpack-protect"),title:__("Automattic's Privacy Policy","jetpack-protect"),href:n?new URL("admin.php?page=jetpack#/privacy",y).href:(0,o.A)("a8c-privacy"),target:n?"_self":"_blank",onClick:d},{label:_x("Terms","Shorthand for Terms of Service.","jetpack-protect"),title:__("WordPress.com Terms of Service","jetpack-protect"),href:(0,o.A)("wpcom-tos"),target:"_blank",onClick:g}];r&&(b=[...b,...r]);const w=(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(p,{}),e]});return(0,h.jsx)("footer",{className:(0,i.A)("jp-dashboard-footer",{"is-sm":v,"is-md":j,"is-lg":x},t),"aria-label":__("Jetpack","jetpack-protect"),role:"contentinfo",...f,children:(0,h.jsxs)("ul",{children:[(0,h.jsx)("li",{className:"jp-dashboard-footer__jp-item",children:s?(0,h.jsx)("a",{href:s,children:w}):w}),b.map((e=>{const t="button"===e.role,s=!t&&"_blank"===e.target;return(0,h.jsx)("li",{children:(0,h.jsxs)("a",{href:e.href,title:e.title,target:e.target,onClick:e.onClick,onKeyDown:e.onKeyDown,className:(0,i.A)("jp-dashboard-footer__menu-item",{"is-external":s}),role:e.role,rel:s?"noopener noreferrer":void 0,tabIndex:t?0:void 0,children:[e.label,s&&(0,h.jsx)(m,{})]})},e.label)})),(0,h.jsx)("li",{className:"jp-dashboard-footer__a8c-item",children:(0,h.jsx)("a",{href:n?new URL("admin.php?page=jetpack_about",y).href:(0,o.A)("a8c-about"),"aria-label":__("An Automattic Airline","jetpack-protect"),children:(0,h.jsx)(l.A,{"aria-hidden":"true"})})})]})})}},7142:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(7723),n=s(3022),a=s(790);const __=r.__,i=({logoColor:e="#069e08",showText:t=!0,className:s,height:r=32,...i})=>{const o=t?"0 0 118 32":"0 0 32 32";return(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:o,className:(0,n.A)("jetpack-logo",s),"aria-labelledby":"jetpack-logo-title",height:r,...i,role:"img",children:[(0,a.jsx)("title",{id:"jetpack-logo-title",children:__("Jetpack Logo","jetpack-protect")}),(0,a.jsx)("path",{fill:e,d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z M15,19H7l8-16V19z M17,29V13h8L17,29z"}),t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("path",{d:"M41.3,26.6c-0.5-0.7-0.9-1.4-1.3-2.1c2.3-1.4,3-2.5,3-4.6V8h-3V6h6v13.4C46,22.8,45,24.8,41.3,26.6z"}),(0,a.jsx)("path",{d:"M65,18.4c0,1.1,0.8,1.3,1.4,1.3c0.5,0,2-0.2,2.6-0.4v2.1c-0.9,0.3-2.5,0.5-3.7,0.5c-1.5,0-3.2-0.5-3.2-3.1V12H60v-2h2.1V7.1 H65V10h4v2h-4V18.4z"}),(0,a.jsx)("path",{d:"M71,10h3v1.3c1.1-0.8,1.9-1.3,3.3-1.3c2.5,0,4.5,1.8,4.5,5.6s-2.2,6.3-5.8,6.3c-0.9,0-1.3-0.1-2-0.3V28h-3V10z M76.5,12.3 c-0.8,0-1.6,0.4-2.5,1.2v5.9c0.6,0.1,0.9,0.2,1.8,0.2c2,0,3.2-1.3,3.2-3.9C79,13.4,78.1,12.3,76.5,12.3z"}),(0,a.jsx)("path",{d:"M93,22h-3v-1.5c-0.9,0.7-1.9,1.5-3.5,1.5c-1.5,0-3.1-1.1-3.1-3.2c0-2.9,2.5-3.4,4.2-3.7l2.4-0.3v-0.3c0-1.5-0.5-2.3-2-2.3 c-0.7,0-2.3,0.5-3.7,1.1L84,11c1.2-0.4,3-1,4.4-1c2.7,0,4.6,1.4,4.6,4.7L93,22z M90,16.4l-2.2,0.4c-0.7,0.1-1.4,0.5-1.4,1.6 c0,0.9,0.5,1.4,1.3,1.4s1.5-0.5,2.3-1V16.4z"}),(0,a.jsx)("path",{d:"M104.5,21.3c-1.1,0.4-2.2,0.6-3.5,0.6c-4.2,0-5.9-2.4-5.9-5.9c0-3.7,2.3-6,6.1-6c1.4,0,2.3,0.2,3.2,0.5V13 c-0.8-0.3-2-0.6-3.2-0.6c-1.7,0-3.2,0.9-3.2,3.6c0,2.9,1.5,3.8,3.3,3.8c0.9,0,1.9-0.2,3.2-0.7V21.3z"}),(0,a.jsx)("path",{d:"M110,15.2c0.2-0.3,0.2-0.8,3.8-5.2h3.7l-4.6,5.7l5,6.3h-3.7l-4.2-5.8V22h-3V6h3V15.2z"}),(0,a.jsx)("path",{d:"M58.5,21.3c-1.5,0.5-2.7,0.6-4.2,0.6c-3.6,0-5.8-1.8-5.8-6c0-3.1,1.9-5.9,5.5-5.9s4.9,2.5,4.9,4.9c0,0.8,0,1.5-0.1,2h-7.3 c0.1,2.5,1.5,2.8,3.6,2.8c1.1,0,2.2-0.3,3.4-0.7C58.5,19,58.5,21.3,58.5,21.3z M56,15c0-1.4-0.5-2.9-2-2.9c-1.4,0-2.3,1.3-2.4,2.9 C51.6,15,56,15,56,15z"})]})]})}},1608:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(7723),n=s(3022),a=s(790);const __=r.__,i=({logoColor:e="#069e08",showText:t=!0,className:s,height:r=42,...i})=>{const o=t?"0 0 245 41":"0 0 41 41";return(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:o,className:(0,n.A)("jetpack-logo",s),"aria-labelledby":"jetpack-logo-title",height:r,...i,role:"img",children:[(0,a.jsx)("title",{id:"jetpack-logo-title",children:__("Jetpack Protect Logo","jetpack-protect")}),(0,a.jsx)("path",{d:"M20.1063 40.2111C31.2106 40.2111 40.2126 31.2091 40.2126 20.1048C40.2126 9.00048 31.2106 0 20.1063 0C9.00197 0 0 9.00197 0 20.1063C0 31.2106 9.00197 40.2111 20.1063 40.2111Z",fill:e}),(0,a.jsx)("path",{d:"M21.104 16.7295V36.2209L31.1571 16.7295H21.104Z",fill:"white"}),(0,a.jsx)("path",{d:"M19.0701 23.4444V3.99023L9.05566 23.4444H19.0701Z",fill:"white"}),t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("path",{d:"M159.347 27.3605H156.978V7.9082H162.371C163.176 7.9082 163.917 7.95666 164.596 8.05359C165.284 8.15051 165.914 8.30074 166.486 8.50428C167.649 8.92104 168.531 9.54619 169.132 10.3797C169.733 11.2036 170.033 12.2213 170.033 13.4328C170.033 14.4214 169.834 15.2937 169.437 16.0497C169.049 16.796 168.482 17.4211 167.736 17.9251C166.99 18.4291 166.079 18.812 165.003 19.0737C163.937 19.3257 162.725 19.4517 161.368 19.4517C160.719 19.4517 160.045 19.4226 159.347 19.3644V27.3605ZM159.347 17.1982C159.667 17.237 160.002 17.2661 160.35 17.2854C160.699 17.3048 161.029 17.3145 161.339 17.3145C162.454 17.3145 163.403 17.2273 164.189 17.0528C164.974 16.8784 165.613 16.6264 166.108 16.2968C166.602 15.9576 166.961 15.5505 167.183 15.0756C167.406 14.591 167.518 14.0434 167.518 13.4328C167.518 12.6768 167.334 12.0565 166.965 11.5719C166.607 11.0873 166.093 10.719 165.424 10.467C165.008 10.3119 164.528 10.2053 163.985 10.1471C163.442 10.0793 162.812 10.0453 162.095 10.0453H159.347V17.1982Z"}),(0,a.jsx)("path",{d:"M181.093 15.3809H180.919C180.386 15.3809 179.862 15.4294 179.349 15.5263C178.835 15.6232 178.35 15.7735 177.895 15.977C177.449 16.1708 177.042 16.4131 176.674 16.7039C176.315 16.9947 176.014 17.3388 175.772 17.7361V27.3605H173.519V13.5491H175.263L175.699 15.7444H175.743C175.966 15.3858 176.237 15.0514 176.557 14.7412C176.887 14.4311 177.255 14.1597 177.662 13.9271C178.069 13.6945 178.515 13.5152 179 13.3892C179.484 13.2535 179.993 13.1856 180.526 13.1856C180.623 13.1856 180.72 13.1905 180.817 13.2002C180.924 13.2002 181.016 13.205 181.093 13.2147V15.3809Z"}),(0,a.jsx)("path",{d:"M193.302 20.4694C193.302 19.6164 193.195 18.8604 192.982 18.2014C192.779 17.5326 192.488 16.9705 192.11 16.5149C191.741 16.0497 191.3 15.7008 190.787 15.4682C190.273 15.2258 189.711 15.1047 189.1 15.1047C188.49 15.1047 187.928 15.2258 187.414 15.4682C186.9 15.7008 186.454 16.0497 186.076 16.5149C185.708 16.9705 185.417 17.5326 185.204 18.2014C185.001 18.8604 184.899 19.6164 184.899 20.4694C184.899 21.3126 185.001 22.0686 185.204 22.7373C185.417 23.3964 185.713 23.9537 186.091 24.4092C186.469 24.8648 186.915 25.2137 187.428 25.456C187.942 25.6886 188.504 25.8049 189.115 25.8049C189.725 25.8049 190.283 25.6886 190.787 25.456C191.3 25.2137 191.741 24.8648 192.11 24.4092C192.488 23.9537 192.779 23.3964 192.982 22.7373C193.195 22.0686 193.302 21.3126 193.302 20.4694ZM195.657 20.4694C195.657 21.5549 195.502 22.5435 195.192 23.4352C194.882 24.3269 194.441 25.0926 193.869 25.7322C193.307 26.3622 192.623 26.8517 191.819 27.2006C191.015 27.5495 190.113 27.724 189.115 27.724C188.088 27.724 187.167 27.5495 186.353 27.2006C185.548 26.8517 184.86 26.3622 184.288 25.7322C183.726 25.0926 183.295 24.3269 182.994 23.4352C182.694 22.5435 182.544 21.5549 182.544 20.4694C182.544 19.3741 182.699 18.3807 183.009 17.489C183.319 16.5973 183.755 15.8316 184.317 15.1919C184.889 14.5522 185.577 14.0579 186.382 13.709C187.196 13.3601 188.102 13.1856 189.1 13.1856C190.118 13.1856 191.034 13.3601 191.848 13.709C192.662 14.0579 193.35 14.5522 193.913 15.1919C194.475 15.8316 194.906 16.5973 195.206 17.489C195.507 18.3807 195.657 19.3741 195.657 20.4694Z"}),(0,a.jsx)("path",{d:"M206.354 27.3896C206.16 27.4672 205.864 27.5398 205.467 27.6077C205.07 27.6755 204.595 27.7143 204.042 27.724C203.315 27.724 202.685 27.6319 202.152 27.4478C201.619 27.2733 201.173 27.0213 200.815 26.6918C200.466 26.3525 200.204 25.9455 200.03 25.4705C199.855 24.9956 199.768 24.4674 199.768 23.8859V15.4827H197.107V13.5491H199.768V9.76911H202.021V13.5491H206.15V15.4827H202.021V23.5369C202.021 23.8568 202.065 24.1524 202.152 24.4238C202.249 24.6952 202.395 24.9326 202.588 25.1362C202.792 25.33 203.049 25.4851 203.359 25.6014C203.669 25.708 204.042 25.7613 204.478 25.7613C204.779 25.7613 205.094 25.7371 205.423 25.6886C205.763 25.6402 206.073 25.5772 206.354 25.4996V27.3896Z"}),(0,a.jsx)("path",{d:"M214.681 15.1192C214.167 15.1192 213.687 15.221 213.242 15.4245C212.805 15.6281 212.423 15.9043 212.093 16.2532C211.764 16.6021 211.497 17.0141 211.293 17.489C211.09 17.9542 210.964 18.4534 210.915 18.9864H217.894C217.894 18.4437 217.821 17.9397 217.676 17.4744C217.54 16.9995 217.337 16.5876 217.065 16.2387C216.794 15.8898 216.459 15.6184 216.062 15.4245C215.665 15.221 215.204 15.1192 214.681 15.1192ZM215.917 25.7613C216.663 25.7613 217.341 25.7032 217.952 25.5869C218.572 25.4609 219.188 25.2864 219.798 25.0635V26.9389C219.304 27.1812 218.693 27.3702 217.967 27.5059C217.24 27.6513 216.459 27.724 215.626 27.724C214.637 27.724 213.707 27.598 212.835 27.346C211.972 27.094 211.216 26.6869 210.567 26.1248C209.917 25.5626 209.403 24.8309 209.025 23.9295C208.657 23.0184 208.473 21.9232 208.473 20.6438C208.473 19.3838 208.647 18.2934 208.996 17.3727C209.345 16.4422 209.811 15.6668 210.392 15.0465C210.974 14.4262 211.642 13.961 212.398 13.6509C213.154 13.3407 213.944 13.1856 214.768 13.1856C215.544 13.1856 216.266 13.3165 216.934 13.5782C217.613 13.8302 218.199 14.2324 218.693 14.7849C219.188 15.3373 219.575 16.0497 219.857 16.922C220.147 17.7943 220.293 18.8411 220.293 20.0623C220.293 20.1786 220.293 20.2804 220.293 20.3676C220.293 20.4451 220.288 20.6293 220.278 20.92H210.799C210.799 21.802 210.93 22.5532 211.192 23.1735C211.463 23.7841 211.827 24.2832 212.282 24.6709C212.747 25.0489 213.29 25.3252 213.91 25.4996C214.531 25.6741 215.199 25.7613 215.917 25.7613Z"}),(0,a.jsx)("path",{d:"M229.885 13.1856C230.553 13.1856 231.193 13.2583 231.804 13.4037C232.414 13.5394 232.938 13.7332 233.374 13.9852V15.8607C232.763 15.6281 232.201 15.4536 231.687 15.3373C231.174 15.221 230.65 15.1628 230.117 15.1628C229.555 15.1628 228.998 15.2549 228.445 15.4391C227.893 15.6232 227.398 15.9285 226.962 16.355C226.526 16.7718 226.172 17.3291 225.901 18.0269C225.639 18.7151 225.508 19.5728 225.508 20.6002C225.508 21.3465 225.605 22.0346 225.799 22.6646C226.003 23.2946 226.298 23.8423 226.686 24.3075C227.083 24.763 227.578 25.1216 228.169 25.3833C228.76 25.6353 229.444 25.7613 230.219 25.7613C230.781 25.7613 231.348 25.7032 231.92 25.5869C232.492 25.4609 233.049 25.2864 233.592 25.0635V26.9389C233.427 27.0358 233.209 27.1328 232.938 27.2297C232.676 27.3266 232.385 27.409 232.065 27.4768C231.745 27.5544 231.406 27.6125 231.048 27.6513C230.689 27.6998 230.33 27.724 229.972 27.724C229.041 27.724 228.159 27.5835 227.326 27.3024C226.502 27.0213 225.78 26.59 225.16 26.0085C224.539 25.4269 224.05 24.6903 223.691 23.7986C223.333 22.907 223.153 21.8505 223.153 20.6293C223.153 19.7182 223.245 18.8992 223.429 18.1723C223.623 17.4454 223.885 16.8057 224.215 16.2532C224.544 15.7008 224.927 15.2307 225.363 14.843C225.809 14.4553 226.279 14.1403 226.773 13.898C227.277 13.646 227.796 13.4667 228.329 13.3601C228.862 13.2438 229.381 13.1856 229.885 13.1856Z"}),(0,a.jsx)("path",{d:"M244.143 27.3896C243.949 27.4672 243.654 27.5398 243.256 27.6077C242.859 27.6755 242.384 27.7143 241.832 27.724C241.105 27.724 240.475 27.6319 239.942 27.4478C239.409 27.2733 238.963 27.0213 238.604 26.6918C238.255 26.3525 237.993 25.9455 237.819 25.4705C237.645 24.9956 237.557 24.4674 237.557 23.8859V15.4827H234.897V13.5491H237.557V9.76911H239.811V13.5491H243.94V15.4827H239.811V23.5369C239.811 23.8568 239.854 24.1524 239.942 24.4238C240.039 24.6952 240.184 24.9326 240.378 25.1362C240.581 25.33 240.838 25.4851 241.148 25.6014C241.458 25.708 241.832 25.7613 242.268 25.7613C242.568 25.7613 242.883 25.7371 243.213 25.6886C243.552 25.6402 243.862 25.5772 244.143 25.4996V27.3896Z"}),(0,a.jsx)("path",{d:"M51.9141 33.3819C51.3379 32.499 50.8019 31.6176 50.2644 30.7733C53.1038 29.0462 54.0626 27.666 54.0626 25.0559V9.97619H50.7245V7.09961H57.8236V24.2891C57.8236 28.6635 56.558 31.1188 51.9141 33.3819Z"}),(0,a.jsx)("path",{d:"M81.6522 23.0994C81.6522 24.557 82.6885 24.7104 83.3793 24.7104C84.0702 24.7104 85.0677 24.4796 85.8345 24.2503V26.9363C84.7595 27.2817 83.6473 27.5497 82.1122 27.5497C80.2705 27.5497 78.122 26.8589 78.122 23.6354V15.7307H76.1655V13.006H78.122V8.97852H81.6522V13.0075H86.1025V15.7322H81.6522V23.0994Z"}),(0,a.jsx)("path",{d:"M89.0193 34.7251V12.9691H92.3961V14.2734C93.7391 13.2371 95.2355 12.585 97.0773 12.585C100.262 12.585 102.795 14.8109 102.795 19.6067C102.795 24.3652 100.033 27.5113 95.4663 27.5113C94.3541 27.5113 93.4711 27.358 92.5495 27.1659V34.6864H89.0193V34.7251ZM96.1557 15.5017C95.1194 15.5017 93.8151 16.0005 92.5867 17.0755V24.4814C93.3535 24.6347 94.1605 24.7494 95.234 24.7494C97.7279 24.7494 99.1484 23.1756 99.1484 19.8762C99.1484 16.8447 98.1121 15.5017 96.1557 15.5017Z"}),(0,a.jsx)("path",{d:"M116.683 27.2051H113.384V25.6313H113.306C112.155 26.5142 110.735 27.4731 108.625 27.4731C106.783 27.4731 104.788 26.1301 104.788 23.4054C104.788 19.7605 107.896 19.0696 110.083 18.7629L113.19 18.3416V17.9202C113.19 16.001 112.423 15.3876 110.619 15.3876C109.736 15.3876 107.665 15.6556 105.976 16.3464L105.67 13.5071C107.205 12.9696 109.315 12.5869 111.08 12.5869C114.533 12.5869 116.759 13.9686 116.759 18.0736V27.2051H116.683ZM113.153 20.5675L110.236 21.0276C109.353 21.1422 108.433 21.6797 108.433 22.984C108.433 24.1349 109.085 24.7871 110.044 24.7871C111.08 24.7871 112.193 24.1736 113.152 23.4828V20.5675H113.153Z"}),(0,a.jsx)("path",{d:"M131.264 26.745C129.806 27.2438 128.502 27.5505 126.851 27.5505C121.556 27.5505 119.445 24.5191 119.445 20.1074C119.445 15.465 122.362 12.5869 127.08 12.5869C128.845 12.5869 129.92 12.8936 131.109 13.2778V16.2705C130.073 15.8863 128.577 15.465 127.119 15.465C124.97 15.465 123.129 16.6159 123.129 19.9153C123.129 23.5602 124.97 24.6739 127.311 24.6739C128.423 24.6739 129.652 24.4431 131.301 23.791V26.745H131.264Z"}),(0,a.jsx)("path",{d:"M137.941 19.1093C138.247 18.7639 138.478 18.4185 142.928 13.0077H147.532L141.776 19.7615L148.068 27.2432H143.464L137.978 20.4895V27.2432H134.449V7.09973H137.979V19.1093H137.941Z"}),(0,a.jsx)("path",{d:"M73.5167 26.7445C71.6749 27.3207 70.1011 27.55 68.2593 27.55C63.7316 27.55 60.9309 25.2869 60.9309 19.9908C60.9309 16.1152 63.3102 12.585 67.8752 12.585C72.403 12.585 73.9768 15.731 73.9768 18.7238C73.9768 19.7213 73.8993 20.2588 73.8621 20.8335H64.7306C64.808 23.9409 66.5724 24.6705 69.2197 24.6705C70.6773 24.6705 71.9816 24.325 73.4795 23.7875V26.7416H73.5167V26.7445ZM70.2947 18.4945C70.2947 16.7673 69.7185 15.271 67.8395 15.271C66.0751 15.271 65.0001 16.5365 64.7693 18.4945H70.2947Z"})]})]})}},8509:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(3022),n=s(1609),a=s(9750);const i=Number(a.A.smcols),o=Number(a.A.mdcols),c=Number(a.A.lgcols),l=e=>{const{children:t,tagName:s="div",className:l}=e,d=Math.min(i,"number"==typeof e.sm?e.sm:i),u=Math.min(i,"object"==typeof e.sm?e.sm.start:0),h=Math.min(i,"object"==typeof e.sm?e.sm.end:0),p=Math.min(o,"number"==typeof e.md?e.md:o),m=Math.min(o,"object"==typeof e.md?e.md.start:0),g=Math.min(o,"object"==typeof e.md?e.md.end:0),f=Math.min(c,"number"==typeof e.lg?e.lg:c),v=Math.min(c,"object"==typeof e.lg?e.lg.start:0),j=Math.min(c,"object"==typeof e.lg?e.lg.end:0),x=(0,r.A)(l,{[a.A[`col-sm-${d}`]]:!(u&&h),[a.A[`col-sm-${u}-start`]]:u>0,[a.A[`col-sm-${h}-end`]]:h>0,[a.A[`col-md-${p}`]]:!(m&&g),[a.A[`col-md-${m}-start`]]:m>0,[a.A[`col-md-${g}-end`]]:g>0,[a.A[`col-lg-${f}`]]:!(v&&j),[a.A[`col-lg-${v}-start`]]:v>0,[a.A[`col-lg-${j}-end`]]:j>0});return(0,n.createElement)(s,{className:x},t)}},5918:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(3022),n=s(1609),a=s(9713);const i=({children:e,fluid:t=!1,tagName:s="div",className:i,horizontalGap:o=1,horizontalSpacing:c=1},l)=>{const d=(0,n.useMemo)((()=>{const e=`calc( var(--horizontal-spacing) * ${c} )`;return{paddingTop:e,paddingBottom:e,rowGap:`calc( var(--horizontal-spacing) * ${o} )`}}),[o,c]),u=(0,r.A)(i,a.A.container,{[a.A.fluid]:t});return(0,n.createElement)(s,{className:u,style:d,ref:l},e)},o=(0,n.forwardRef)(i)},442:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(9491),n=s(9130);const a=["sm","md","lg"],i=(e,t)=>{const s=Array.isArray(e)?e:[e],i=Array.isArray(t)?t:[t],[o,c,l]=a,d={sm:(0,r.useMediaQuery)(n.A[o]),md:(0,r.useMediaQuery)(n.A[c]),lg:(0,r.useMediaQuery)(n.A[l])};return s.map(((e,t)=>{const s=i[t];return s?((e,t,s)=>{const r=a.indexOf(e),n=r+1,i=t.includes("=");let o=[];return t.startsWith("<")&&(o=a.slice(0,i?n:r)),t.startsWith(">")&&(o=a.slice(i?r:n)),o?.length?o.some((e=>s[e])):s[e]})(e,s,d):d[e]}))}},5734:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(4456),n=s(790);const a=e=>(0,n.jsx)("mark",{className:r.A["marked-lines__mark"],children:e},e),i=(e,t)=>{const[s,r]=e.reduce((([e,s],[r,n])=>{const i=t.slice(r,n);return[[...e,...r>s?[t.slice(s,r),a(i)]:[a(i)]],n]}),[[],0]);return r<t.length?[...s,t.slice(r)]:s},o=({context:e})=>{const{marks:t,...s}=e;return(0,n.jsxs)("div",{className:r.A["marked-lines"],children:[(0,n.jsx)("div",{className:r.A["marked-lines__line-numbers"],children:Object.keys(s).map((e=>{const s=Object.hasOwn(t,e);return(0,n.jsx)("div",{className:`${r.A["marked-lines__line-number"]} ${s?r.A["marked-lines__marked-line"]:""}`,children:e},e)}))}),(0,n.jsx)("div",{className:r.A["marked-lines__lines"],children:Object.keys(s).map((e=>{const a=s[e]||" ",o=Object.hasOwn(t,e);return(0,n.jsx)("div",{className:`${r.A["marked-lines__line"]} ${o?r.A["marked-lines__marked-line"]:""} `,children:(0,n.jsx)(n.Fragment,{children:o?i(t[e],a):a})},e)}))})]})}},7656:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(3751),n=s(9783),a=s(3883),i=s(1113),o=s(991),c=s(3022),l=s(5355),d=s(790);const u=e=>{switch(e){case"error":case"warning":default:return r.A;case"info":return n.A;case"success":return a.A}},h=({level:e="info",title:t,children:s,actions:r,hideCloseButton:n=!1,onClose:a})=>{const h=(0,c.A)(l.A.container,l.A[`is-${e}`]);return(0,d.jsxs)("div",{className:h,children:[(0,d.jsx)("div",{className:l.A["icon-wrapper"],children:(0,d.jsx)(i.A,{icon:u(e),className:l.A.icon})}),(0,d.jsxs)("div",{className:l.A["main-content"],children:[t&&(0,d.jsx)("div",{className:l.A.title,children:t}),s,r&&r.length>0&&(0,d.jsx)("div",{className:l.A["action-bar"],children:r.map(((e,t)=>(0,d.jsx)("div",{className:l.A.action,children:e},t)))})]}),!n&&(0,d.jsx)("button",{"aria-label":"close",className:l.A["close-button"],onClick:a,children:(0,d.jsx)(i.A,{icon:o.A})})]})}},9245:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>w,N0:()=>b,eY:()=>x,i7:()=>y});var r=s(7723),n=s(1113),a=s(3883),i=s(1249),o=s(3022),c=s(1609),l=s(597),d=s(442),u=s(5879),h=s(7425),p=s(4435),m=s(790);const __=r.__,g=__("Included","jetpack-protect"),f=__("Not included","jetpack-protect"),v=__("Coming soon","jetpack-protect"),j=(0,c.createContext)(void 0),x=({isIncluded:e=!1,isComingSoon:t=!1,index:s=0,label:u=null,tooltipInfo:x,tooltipTitle:y,tooltipClassName:b=""})=>{const[w]=(0,d.A)("lg"),A=(0,c.useContext)(j)[s],k=t||e,C=A.name,_=A.tooltipInfo,S=A.tooltipTitle,N=x||!w&&_,M=((e,t,s)=>e?{lg:v,
// translators: Name of the current feature
default:(0,r.sprintf)(__("%s coming soon","jetpack-protect"),s)}:{lg:t?g:f,default:t?s:(0,r.sprintf)(/* translators: Name of the current feature */
__("%s not included","jetpack-protect"),s)})(t,e,C),L=w?M.lg:M.default;return(0,m.jsxs)("div",{className:(0,o.A)(p.A.item,p.A.value),children:[(0,m.jsx)(n.A,{className:(0,o.A)(p.A.icon,k?p.A["icon-check"]:p.A["icon-cross"]),size:32,icon:k?a.A:i.A}),(0,m.jsx)(h.Ay,{variant:"body-small",children:u||L}),N&&(0,m.jsx)(l.A,{title:y||S,iconClassName:p.A["popover-icon"],className:(0,o.A)(p.A.popover,b),placement:"bottom-end",iconSize:14,offset:4,wide:Boolean(y&&x),children:(0,m.jsx)(h.Ay,{variant:"body-small",component:"div",children:x||_})})]})},y=({children:e})=>(0,m.jsx)("div",{className:p.A.header,children:e}),b=({primary:e=!1,children:t})=>{let s=0;return(0,m.jsx)("div",{className:(0,o.A)(p.A.card,{[p.A["is-primary"]]:e}),children:c.Children.map(t,(e=>{const t=e;return t.type===x?(s++,(0,c.cloneElement)(t,{index:s-1})):t}))})},w=({title:e,items:t,children:s,showIntroOfferDisclaimer:r=!1})=>{const[n]=(0,d.A)("lg");return(0,m.jsxs)(j.Provider,{value:t,children:[(0,m.jsx)("div",{className:(0,o.A)(p.A.container,{[p.A["is-viewport-large"]]:n}),style:{"--rows":t.length+1,"--columns":c.Children.toArray(s).length+1},children:(0,m.jsxs)("div",{className:p.A.table,children:[(0,m.jsx)(h.Ay,{variant:"headline-small",children:e}),n&&t.map(((e,s)=>(0,m.jsxs)("div",{className:(0,o.A)(p.A.item,{[p.A["last-feature"]]:s===t.length-1}),children:[(0,m.jsx)(h.Ay,{variant:"body-small",children:(0,m.jsx)("strong",{children:e.name})}),e.tooltipInfo&&(0,m.jsx)(l.A,{title:e.tooltipTitle,iconClassName:p.A["popover-icon"],className:p.A.popover,placement:e.tooltipPlacement?e.tooltipPlacement:"bottom-end",iconSize:14,offset:4,wide:Boolean(e.tooltipTitle&&e.tooltipInfo),children:(0,m.jsx)(h.Ay,{variant:"body-small",children:e.tooltipInfo})})]},s))),s]})}),(0,m.jsx)("div",{className:p.A["tos-container"],children:(0,m.jsxs)("div",{className:p.A.tos,children:[r&&(0,m.jsx)(h.Ay,{variant:"body-small",children:__("Reduced pricing is a limited offer for the first year and renews at regular price.","jetpack-protect")}),(0,m.jsx)(u.A,{multipleButtons:!0})]})})]})}},489:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(7723),n=s(3022),a=s(7425),i=s(2746),o=s(8336),c=s(790);const __=r.__,l=({price:e,offPrice:t,currency:s="",showNotOffPrice:r=!0,hideDiscountLabel:l=!0,promoLabel:d="",legend:u=__("/month, paid yearly","jetpack-protect"),isNotConvenientPrice:h=!1,hidePriceFraction:p=!1,children:m})=>{if(null==e&&null==t||!s)return null;r=r&&null!=t;const g="number"==typeof e&&"number"==typeof t?Math.floor((e-t)/e*100):0,f=!l&&g&&g>0?g+__("% off","jetpack-protect"):null;return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{className:o.A.container,children:(0,c.jsxs)("div",{className:(0,n.A)(o.A["price-container"],"product-price_container"),children:[(0,c.jsx)(i.g,{value:t??e,currency:s,isOff:!h,hidePriceFraction:p}),r&&(0,c.jsx)(i.g,{value:e,currency:s,isOff:!1,hidePriceFraction:p}),f&&(0,c.jsx)(a.Ay,{className:(0,n.A)(o.A["promo-label"],"product-price_promo_label"),children:f})]})}),(0,c.jsxs)("div",{className:o.A.footer,children:[m||(0,c.jsx)(a.Ay,{className:(0,n.A)(o.A.legend,"product-price_legend"),children:u}),d&&(0,c.jsx)(a.Ay,{className:(0,n.A)(o.A["promo-label"],"product-price_promo_label"),children:d})]})]})}},2746:(e,t,s)=>{"use strict";s.d(t,{g:()=>c});var r=s(4268),n=s(3022),a=s(7425),i=s(8336),o=s(790);const c=({value:e,currency:t,isOff:s,hidePriceFraction:c})=>{const l=(0,n.A)(i.A.price,"product-price_price",{[i.A["is-not-off-price"]]:!s}),{symbol:d,integer:u,fraction:h}=(0,r.vA)(e,t),p=!c||!h.endsWith("00");return(0,o.jsxs)(a.Ay,{className:l,variant:"headline-medium",component:"p",children:[(0,o.jsx)(a.Ay,{className:i.A.symbol,component:"sup",variant:"title-medium",children:d}),u,p&&(0,o.jsx)(a.Ay,{component:"sup",variant:"body-small","data-testid":"PriceFraction",children:(0,o.jsx)("strong",{children:h})})]})}},6461:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(8120),n=s.n(r),a=(s(2144),s(790));const i=({color:e="#FFFFFF",className:t="",size:s=20})=>{const r=t+" jp-components-spinner",n={width:s,height:s,fontSize:s,borderTopColor:e},i={borderTopColor:e,borderRightColor:e};return(0,a.jsx)("div",{className:r,children:(0,a.jsx)("div",{className:"jp-components-spinner__outer",style:n,children:(0,a.jsx)("div",{className:"jp-components-spinner__inner",style:i})})})};i.propTypes={color:n().string,className:n().string,size:n().number};const o=i},3390:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(4268),n=s(6427),a=s(3022),i=s(7425),o=s(4152),c=s(790);const l=({className:e,hideValue:t,icon:s,label:l,value:d,variant:u="square"})=>{const h=(0,r.ZV)(d),p=(0,r.qe)(d);return(0,c.jsxs)("div",{className:(0,a.A)(e,o.A.wrapper,o.A[u]),children:[(0,c.jsx)("div",{className:(0,a.A)(o.A.icon),children:s}),(0,c.jsxs)("div",{className:(0,a.A)(o.A.info),children:[(0,c.jsx)(i.Ay,{className:o.A.label,children:l}),"square"===u?(0,c.jsx)(n.Tooltip,{text:h,placement:"top",children:(0,c.jsx)(i.Ay,{variant:"headline-small",className:(0,a.A)(o.A.value),children:t?"-":p})}):(0,c.jsx)(i.Ay,{variant:"title-medium-semi-bold",className:(0,a.A)(o.A.value),children:t?"-":h})]})]})}},1158:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(7723),n=s(3022),a=s(7425),i=s(4533),o=s(790);const __=r.__,c=({className:e,label:t,status:s="inactive"})=>{const r={active:__("Active","jetpack-protect"),error:__("Error","jetpack-protect"),action:__("Action needed","jetpack-protect"),inactive:__("Inactive","jetpack-protect"),initializing:__("Setting up","jetpack-protect")};return(0,o.jsxs)(a.Ay,{variant:"body-extra-small",className:(0,n.A)(i.A.status,{[i.A[`is-${s}`]]:s},e),children:[(0,o.jsx)("span",{className:i.A.status__indicator}),(0,o.jsx)("span",{className:i.A.status__label,children:t||""===t?t:r[s]})]})}},5879:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(6427),n=s(6087),a=s(7723),i=s(3022),o=s(3924),c=s(7425),l=(s(4099),s(790));const __=a.__,d=({multipleButtonsLabels:e})=>Array.isArray(e)&&e.length>1?(0,n.createInterpolateElement)((0,a.sprintf)(/* translators: %1$s is button label 1 and %2$s is button label 2 */
__("By clicking <strong>%1$s</strong> or <strong>%2$s</strong>, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-protect"),e[0],e[1]),{strong:(0,l.jsx)("strong",{}),tosLink:(0,l.jsx)(p,{slug:"wpcom-tos"}),shareDetailsLink:(0,l.jsx)(p,{slug:"jetpack-support-what-data-does-jetpack-sync"})}):(0,n.createInterpolateElement)(__("By clicking the buttons above, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-protect"),{tosLink:(0,l.jsx)(p,{slug:"wpcom-tos"}),shareDetailsLink:(0,l.jsx)(p,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),u=({agreeButtonLabel:e})=>(0,n.createInterpolateElement)((0,a.sprintf)(/* translators: %s is a button label */
__("By clicking <strong>%s</strong>, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-protect"),e),{strong:(0,l.jsx)("strong",{}),tosLink:(0,l.jsx)(p,{slug:"wpcom-tos"}),shareDetailsLink:(0,l.jsx)(p,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),h=()=>(0,n.createInterpolateElement)(__("By continuing you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site’s data</shareDetailsLink> with us. We’ll check if that email is linked to an existing WordPress.com account or create a new one instantly.","jetpack-protect"),{tosLink:(0,l.jsx)(p,{slug:"wpcom-tos"}),shareDetailsLink:(0,l.jsx)(p,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),p=({slug:e,children:t})=>(0,l.jsx)(r.ExternalLink,{className:"terms-of-service__link",href:(0,o.A)(e),children:t}),m=({className:e,multipleButtons:t,agreeButtonLabel:s,isTextOnly:r,...n})=>(0,l.jsx)(c.Ay,{className:(0,i.A)(e,"terms-of-service"),...n,children:r?(0,l.jsx)(h,{}):t?(0,l.jsx)(d,{multipleButtonsLabels:t}):(0,l.jsx)(u,{agreeButtonLabel:s})})},110:(e,t,s)=>{"use strict";s.d(t,{Q:()=>r,Z:()=>n});const r={"headline-medium":"h1","headline-small":"h2","headline-small-regular":"h2","title-medium":"h3","title-medium-semi-bold":"h3","title-small":"h4",body:"p","body-small":"p","body-extra-small":"p","body-extra-small-bold":"p",label:"p"},n=["mt","mr","mb","ml","mx","my","m","pt","pr","pb","pl","px","py","p"]},7425:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>l,H3:()=>d,hE:()=>u});var r=s(3022),n=s(1609),a=s(110),i=s(3370),o=s(790);const c=(0,n.forwardRef)((({variant:e="body",children:t,component:s,className:c,...l},d)=>{const u=s||a.Q[e]||"span",h=(0,n.useMemo)((()=>a.Z.reduce(((e,t)=>(void 0!==l[t]&&(e+=i.A[`${t}-${l[t]}`]+" ",delete l[t]),e)),"")),[l]);return(0,o.jsx)(u,{className:(0,r.A)(i.A.reset,i.A[e],c,h),...l,ref:d,children:t})}));c.displayName="Text";const l=c,d=({children:e,weight:t="bold",...s})=>{const r="headline-small"+("bold"===t?"":`-${t}`);return(0,o.jsx)(c,{variant:r,mb:3,...s,children:e})},u=({children:e,size:t="medium",...s})=>(0,o.jsx)(c,{variant:`title-${t}`,mb:1,...s,children:e})},723:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>h});var r=s(1609),n=s(3167),a=s(790);const i={"--font-headline-medium":"48px","--font-headline-small":"36px","--font-title-medium":"24px","--font-title-small":"20px","--font-body":"16px","--font-body-small":"14px","--font-body-extra-small":"12px","--font-title-large":"var(--font-headline-small)","--font-label":"var(--font-body-extra-small)"},o={"--jp-black":"#000000","--jp-black-80":"#2c3338","--jp-white":"#ffffff","--jp-white-off":"#f9f9f6","--jp-gray":"#dcdcde","--jp-gray-0":"#F6F7F7","--jp-gray-5":"var(--jp-gray)","--jp-gray-10":"#C3C4C7","--jp-gray-20":"#A7AAAD","--jp-gray-40":"#787C82","--jp-gray-50":"#646970","--jp-gray-60":"#50575E","--jp-gray-70":"#3C434A","--jp-gray-80":"#2C3338","--jp-gray-90":"#1d2327","--jp-gray-off":"#e2e2df","--jp-red-0":"#F7EBEC","--jp-red-5":"#FACFD2","--jp-red-40":"#E65054","--jp-red-50":"#D63638","--jp-red-60":"#B32D2E","--jp-red-70":"#8A2424","--jp-red-80":"#691C1C","--jp-red":"#d63639","--jp-yellow-5":"#F5E6B3","--jp-yellow-10":"#F2CF75","--jp-yellow-20":"#F0C930","--jp-yellow-30":"#DEB100","--jp-yellow-40":"#C08C00","--jp-yellow-50":"#9D6E00","--jp-yellow-60":"#7D5600","--jp-blue-20":"#68B3E8","--jp-blue-40":"#1689DB","--jp-pink":"#C9356E","--jp-green-0":"#f0f2eb","--jp-green-5":"#d0e6b8","--jp-green-10":"#9dd977","--jp-green-20":"#64ca43","--jp-green-30":"#2fb41f","--jp-green-40":"#069e08","--jp-green-50":"#008710","--jp-green-60":"#007117","--jp-green-70":"#005b18","--jp-green-80":"#004515","--jp-green-90":"#003010","--jp-green-100":"#001c09","--jp-green":"#069e08","--jp-green-primary":"var( --jp-green-40 )","--jp-green-secondary":"var( --jp-green-30 )"},c={"--jp-border-radius":"4px","--jp-menu-border-height":"1px","--jp-underline-thickness":"2px"},l={"--spacing-base":"8px"},d={},u=(e,t,s)=>{const r={...i,...o,...c,...l};for(const t in r)e.style.setProperty(t,r[t]);s&&e.classList.add(n.A.global),t&&(d[t]={provided:!0,root:e})},h=({children:e=null,targetDom:t,id:s,withGlobalStyles:n=!0})=>{const i=(0,r.useRef)(),o=d?.[s]?.provided;return(0,r.useLayoutEffect)((()=>{if(!o)return t?u(t,s,n):void(i?.current&&u(i.current,s,n))}),[t,i,o,s,n]),t?(0,a.jsx)(a.Fragment,{children:e}):(0,a.jsx)("div",{ref:i,children:e})}},8316:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(6427),n=s(3022),a=s(1609),i=s(9550),o=s(790);const c=({checked:e,className:t,disabled:s,help:c,toggling:l,label:d,size:u="normal",onChange:h})=>{const p=void 0!==l?e&&!l||!e&&l:e,m=(0,a.useCallback)((e=>{l||h(e)}),[l,h]);return(0,o.jsx)(r.ToggleControl,{__nextHasNoMarginBottom:!0,checked:p,className:(0,n.A)(i.A.toggle,t,{[i.A["is-toggling"]]:l,[i.A["is-small"]]:"small"===u,[i.A["no-label"]]:!d}),disabled:s,help:c,label:d,onChange:m})}},1069:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(7999);function n(){return(0,r.getScriptData)()?.site?.admin_url||window.Initial_State?.adminUrl||window.Jetpack_Editor_Initial_State?.adminUrl||window?.myJetpackInitialState?.adminUrl||null}},3924:(e,t,s)=>{"use strict";function r(e,t={}){const s={};let r;if("undefined"!=typeof window&&(r=window?.JP_CONNECTION_INITIAL_STATE?.calypsoEnv),0===e.search("https://")){const t=new URL(e);e=`https://${t.host}${t.pathname}`,s.url=encodeURIComponent(e)}else s.source=encodeURIComponent(e);for(const e in t)s[e]=encodeURIComponent(t[e]);!Object.keys(s).includes("site")&&"undefined"!=typeof jetpack_redirects&&Object.hasOwn(jetpack_redirects,"currentSiteRawUrl")&&(s.site=jetpack_redirects.currentBlogID??jetpack_redirects.currentSiteRawUrl),r&&(s.calypso_env=r);return"https://jetpack.com/redirect/?"+Object.keys(s).map((e=>e+"="+s[e])).join("&")}s.d(t,{A:()=>r})},6439:(e,t,s)=>{let r={};try{r=s(6992)}catch{console.error("jetpackConfig is missing in your webpack config file. See @automattic/jetpack-config"),r={missingConfig:!0}}const n=e=>Object.hasOwn(r,e);e.exports={jetpackConfigHas:n,jetpackConfigGet:e=>{if(!n(e))throw'This app requires the "'+e+'" Jetpack Config to be defined in your webpack configuration file. See details in @automattic/jetpack-config package docs.';return r[e]}}},3673:(e,t,s)=>{"use strict";s.d(t,{$:()=>n,M:()=>r});const r="en",n="USD"},1452:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(8443),n=s(3673),a=s(9980),i=s(1167);const o=function(){let e,t;const s=()=>{const{l10n:{locale:t}}=(0,r.getSettings)();return(e??(t||window?.window?.navigator?.language)??n.M).split("_")[0]};return{setLocale:t=>{e=t},setGeoLocation:e=>{t=e},formatNumber:(e,{decimals:t=0,forceLatin:r=!0,numberFormatOptions:n={}}={})=>{try{return(0,i.j)({browserSafeLocale:s(),decimals:t,forceLatin:r,numberFormatOptions:n}).format(e)}catch{return String(e)}},formatNumberCompact:(e,{decimals:t=0,forceLatin:r=!0,numberFormatOptions:n={}}={})=>{try{return(0,i.c)({browserSafeLocale:s(),decimals:t,forceLatin:r,numberFormatOptions:n}).format(e)}catch{return String(e)}},formatCurrency:(e,r,{stripZeros:n=!1,isSmallestUnit:i=!1,signForPositive:o=!1,forceLatin:c=!0}={})=>(0,a.u)({number:e,currency:r,browserSafeLocale:s(),stripZeros:n,isSmallestUnit:i,signForPositive:o,geoLocation:t,forceLatin:c}),getCurrencyObject:(e,r,{stripZeros:n=!1,isSmallestUnit:i=!1,signForPositive:o=!1,forceLatin:c=!0}={})=>(0,a.v)({number:e,currency:r,browserSafeLocale:s(),stripZeros:n,isSmallestUnit:i,signForPositive:o,geoLocation:t,forceLatin:c})}}},3328:(e,t,s)=>{"use strict";s.d(t,{J:()=>c});var r=s(6941),n=s.n(r),a=s(3673);const i=n()("number-formatters:get-cached-formatter"),o=new Map;function c({locale:e,fallbackLocale:t=a.M,options:s,retries:r=1}){const n=JSON.stringify([e,s]);try{return o.get(n)??o.set(n,new Intl.NumberFormat(e,s)).get(n)}catch(n){if(i(`Intl.NumberFormat was called with a non-existent locale "${e}"; falling back to ${t}`),r)return c({locale:t,options:s,retries:r-1});throw n}}},4268:(e,t,s)=>{"use strict";s.d(t,{ZV:()=>i,qe:()=>o,vA:()=>l});const r=(0,s(1452).A)(),{setLocale:n,setGeoLocation:a,formatNumber:i,formatNumberCompact:o,formatCurrency:c,getCurrencyObject:l}=r},6673:(e,t,s)=>{"use strict";s.d(t,{a:()=>r});const r={AED:{symbol:"د.إ.‏"},AFN:{symbol:"؋"},ALL:{symbol:"Lek"},AMD:{symbol:"֏"},ANG:{symbol:"ƒ"},AOA:{symbol:"Kz"},ARS:{symbol:"$"},AUD:{symbol:"A$"},AWG:{symbol:"ƒ"},AZN:{symbol:"₼"},BAM:{symbol:"КМ"},BBD:{symbol:"Bds$"},BDT:{symbol:"৳"},BGN:{symbol:"лв."},BHD:{symbol:"د.ب.‏"},BIF:{symbol:"FBu"},BMD:{symbol:"$"},BND:{symbol:"$"},BOB:{symbol:"Bs"},BRL:{symbol:"R$"},BSD:{symbol:"$"},BTC:{symbol:"Ƀ"},BTN:{symbol:"Nu."},BWP:{symbol:"P"},BYR:{symbol:"р."},BZD:{symbol:"BZ$"},CAD:{symbol:"C$"},CDF:{symbol:"FC"},CHF:{symbol:"CHF"},CLP:{symbol:"$"},CNY:{symbol:"¥"},COP:{symbol:"$"},CRC:{symbol:"₡"},CUC:{symbol:"CUC"},CUP:{symbol:"$MN"},CVE:{symbol:"$"},CZK:{symbol:"Kč"},DJF:{symbol:"Fdj"},DKK:{symbol:"kr."},DOP:{symbol:"RD$"},DZD:{symbol:"د.ج.‏"},EGP:{symbol:"ج.م.‏"},ERN:{symbol:"Nfk"},ETB:{symbol:"ETB"},EUR:{symbol:"€"},FJD:{symbol:"FJ$"},FKP:{symbol:"£"},GBP:{symbol:"£"},GEL:{symbol:"Lari"},GHS:{symbol:"₵"},GIP:{symbol:"£"},GMD:{symbol:"D"},GNF:{symbol:"FG"},GTQ:{symbol:"Q"},GYD:{symbol:"G$"},HKD:{symbol:"HK$"},HNL:{symbol:"L."},HRK:{symbol:"kn"},HTG:{symbol:"G"},HUF:{symbol:"Ft"},IDR:{symbol:"Rp"},ILS:{symbol:"₪"},INR:{symbol:"₹"},IQD:{symbol:"د.ع.‏"},IRR:{symbol:"﷼"},ISK:{symbol:"kr."},JMD:{symbol:"J$"},JOD:{symbol:"د.ا.‏"},JPY:{symbol:"¥"},KES:{symbol:"S"},KGS:{symbol:"сом"},KHR:{symbol:"៛"},KMF:{symbol:"CF"},KPW:{symbol:"₩"},KRW:{symbol:"₩"},KWD:{symbol:"د.ك.‏"},KYD:{symbol:"$"},KZT:{symbol:"₸"},LAK:{symbol:"₭"},LBP:{symbol:"ل.ل.‏"},LKR:{symbol:"₨"},LRD:{symbol:"L$"},LSL:{symbol:"M"},LYD:{symbol:"د.ل.‏"},MAD:{symbol:"د.م.‏"},MDL:{symbol:"lei"},MGA:{symbol:"Ar"},MKD:{symbol:"ден."},MMK:{symbol:"K"},MNT:{symbol:"₮"},MOP:{symbol:"MOP$"},MRO:{symbol:"UM"},MTL:{symbol:"₤"},MUR:{symbol:"₨"},MVR:{symbol:"MVR"},MWK:{symbol:"MK"},MXN:{symbol:"MX$"},MYR:{symbol:"RM"},MZN:{symbol:"MT"},NAD:{symbol:"N$"},NGN:{symbol:"₦"},NIO:{symbol:"C$"},NOK:{symbol:"kr"},NPR:{symbol:"₨"},NZD:{symbol:"NZ$"},OMR:{symbol:"﷼"},PAB:{symbol:"B/."},PEN:{symbol:"S/."},PGK:{symbol:"K"},PHP:{symbol:"₱"},PKR:{symbol:"₨"},PLN:{symbol:"zł"},PYG:{symbol:"₲"},QAR:{symbol:"﷼"},RON:{symbol:"lei"},RSD:{symbol:"Дин."},RUB:{symbol:"₽"},RWF:{symbol:"RWF"},SAR:{symbol:"﷼"},SBD:{symbol:"S$"},SCR:{symbol:"₨"},SDD:{symbol:"LSd"},SDG:{symbol:"£‏"},SEK:{symbol:"kr"},SGD:{symbol:"S$"},SHP:{symbol:"£"},SLL:{symbol:"Le"},SOS:{symbol:"S"},SRD:{symbol:"$"},STD:{symbol:"Db"},SVC:{symbol:"₡"},SYP:{symbol:"£"},SZL:{symbol:"E"},THB:{symbol:"฿"},TJS:{symbol:"TJS"},TMT:{symbol:"m"},TND:{symbol:"د.ت.‏"},TOP:{symbol:"T$"},TRY:{symbol:"TL"},TTD:{symbol:"TT$"},TVD:{symbol:"$T"},TWD:{symbol:"NT$"},TZS:{symbol:"TSh"},UAH:{symbol:"₴"},UGX:{symbol:"USh"},USD:{},UYU:{symbol:"$U"},UZS:{symbol:"сўм"},VEB:{symbol:"Bs."},VEF:{symbol:"Bs. F."},VND:{symbol:"₫"},VUV:{symbol:"VT"},WST:{symbol:"WS$"},XAF:{symbol:"F"},XCD:{symbol:"$"},XOF:{symbol:"F"},XPF:{symbol:"F"},YER:{symbol:"﷼"},ZAR:{symbol:"R"},ZMW:{symbol:"ZK"},WON:{symbol:"₩"}}},9980:(e,t,s)=>{"use strict";s.d(t,{u:()=>g,v:()=>f});var r=s(6941),n=s.n(r),a=s(3673),i=s(3328),o=s(6673);const c=n()("number-formatters:number-format-currency");function l(e,t){return"USD"===e&&t&&""!==t&&"US"!==t?{symbol:"US$"}:o.a[e]}function d(e,t){return l(e,t)?e:(c(`getValidCurrency was called with a non-existent currency "${e}"; falling back to ${a.$}`),a.$)}function u({number:e,currency:t,browserSafeLocale:s,forceLatin:r=!0,stripZeros:n,signForPositive:a}){const o=`${s}${r?"-u-nu-latn":""}`,c={style:"currency",currency:t,...n&&Number.isInteger(e)&&{maximumFractionDigits:0,minimumFractionDigits:0},...a&&{signDisplay:"exceptZero"}};return(0,i.J)({locale:o,options:c})}function h(e,t,s){return u({number:0,currency:t,browserSafeLocale:e,forceLatin:s}).resolvedOptions().maximumFractionDigits}function p(e,t){const s=Math.pow(10,t);return Math.round(e*s)/s}function m(e,t,s){if(isNaN(e))return c("formatCurrency was called with NaN"),0;if(s){Number.isInteger(e)||c("formatCurrency was called with isSmallestUnit and a float which will be rounded",e);const s=10**t;return p(Math.round(e)/s,t)}return p(e,t)}const g=({number:e,browserSafeLocale:t,currency:s,stripZeros:r,isSmallestUnit:n,signForPositive:a,geoLocation:i,forceLatin:o})=>{const c=d(s,i),p=l(c,i),g=h(t,c,o);if(n&&void 0===g)throw new Error(`Could not determine currency precision for ${c} in ${t}`);const f=m(e,g??0,n);return u({number:f,currency:c,browserSafeLocale:t,forceLatin:o,stripZeros:r,signForPositive:a}).formatToParts(f).reduce(((e,t)=>"currency"===t.type&&p?.symbol?e+p.symbol:e+t.value),"")},f=({number:e,browserSafeLocale:t,currency:s,stripZeros:r,isSmallestUnit:n,signForPositive:a,geoLocation:i,forceLatin:o})=>{const c=d(s,i),p=l(c,i),g=m(e,h(t,c,o)??0,n),f=u({number:g,currency:c,browserSafeLocale:t,forceLatin:o,stripZeros:r,signForPositive:a}).formatToParts(g);let v="",j="$",x="before",y=!1,b=!1,w="",A="";f.forEach((e=>{switch(e.type){case"currency":return j=p?.symbol??e.value,void(y&&(x="after"));case"group":case"integer":return w+=e.value,void(y=!0);case"decimal":case"fraction":return A+=e.value,y=!0,void(b=!0);case"minusSign":return void(v="-");case"plusSign":v="+"}}));const k=!Number.isInteger(g)&&b;return{sign:v,symbol:j,symbolPosition:x,integer:w,fraction:A,hasNonZeroFraction:k}}},1167:(e,t,s)=>{"use strict";s.d(t,{c:()=>a,j:()=>n});var r=s(3328);const n=({browserSafeLocale:e,decimals:t=0,forceLatin:s=!0,numberFormatOptions:n={}})=>{const a=`${e}${s?"-u-nu-latn":""}`,i={minimumFractionDigits:t,maximumFractionDigits:t,...n};return(0,r.J)({locale:a,options:i})},a=({numberFormatOptions:e={},...t})=>n({...t,numberFormatOptions:{notation:"compact",maximumFractionDigits:1,...e}})},1636:(e,t,s)=>{"use strict";s(6427),s(6087),s(7723),s(8418),s(9488),s(790)},4207:(e,t,s)=>{"use strict";s(7723),s(1609),s(5950),s(4589),s(790)},3796:(e,t,s)=>{"use strict";s(6427),s(1609),s(7082),s(5950),s(7928),s(790)},7900:(e,t,s)=>{"use strict";s(7723),s(1609),s(8418),s(4207),s(3796),s(5950),s(790)},7928:(e,t,s)=>{"use strict";s(7723),s(1609),s(3796),s(7900),s(5209),s(4589),s(1825),s(961),s(790)},5209:(e,t,s)=>{"use strict";s(7723),s(1609),s(8418),s(3796),s(5950),s(790)},4589:(e,t,s)=>{"use strict";s(6427),s(7723),s(1609),s(3796),s(5950),s(790)},1825:(e,t,s)=>{"use strict";s(7723),s(1609),s(3796),s(5950),s(790)},961:(e,t,s)=>{"use strict";s(7723),s(1609),s(3796),s(5950),s(790)},7082:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(4105),n=s(7723),a=s(790);const _x=n._x,i=({severity:e})=>e>=5?(0,a.jsx)(r.A,{variant:"danger",children:_x("Critical","Severity label for issues rated 5 or higher.","jetpack-protect")}):e>=3&&e<5?(0,a.jsx)(r.A,{variant:"warning",children:_x("High","Severity label for issues rated between 3 and 5.","jetpack-protect")}):(0,a.jsx)(r.A,{children:_x("Low","Severity label for issues rated below 3.","jetpack-protect")})},1918:(e,t,s)=>{"use strict";var r=s(7723),n=s(435),a=s(2072),i=s(4648),o=s(4314),c=s(5302);const __=r.__;__("Active","jetpack-protect"),__("Fixed","jetpack-protect"),__("Ignored","jetpack-protect"),__("Plugin","jetpack-protect"),__("Theme","jetpack-protect"),__("WordPress","jetpack-protect"),__("File","jetpack-protect"),n.A,a.A,i.A,o.A,c.A},4081:(e,t,s)=>{"use strict";s(8443),s(7723),s(1609),s(8418),s(1636),s(1918),s(7709),s(6032),s(790)},6032:(e,t,s)=>{"use strict";s(6427),s(6087),s(7723),s(7709),s(790)},8418:(e,t,s)=>{"use strict";s.d(t,{Z6:()=>r.Z6});s(4768);var r=s(5448)},4768:(e,t,s)=>{"use strict";s(7723)},4705:(e,t,s)=>{"use strict";s(8992),s(1135)},1135:(e,t,s)=>{"use strict";s.d(t,{$:()=>n});var r=s(790);const n=[{name:"amazon",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M13.582 8.182c-1.648.185-3.802.308-5.344.984-1.781.769-3.03 2.337-3.03 4.644 0 2.953 1.86 4.429 4.253 4.429 2.02 0 3.125-.477 4.685-2.065.516.747.685 1.109 1.629 1.894a.59.59 0 0 0 .672-.066l.006.006c.567-.505 1.599-1.401 2.18-1.888.231-.188.19-.496.009-.754-.52-.718-1.072-1.303-1.072-2.634V8.305c0-1.876.133-3.599-1.249-4.891C15.23 2.369 13.422 2 12.04 2 9.336 2 6.318 3.01 5.686 6.351c-.068.355.191.542.423.594l2.754.298c.258-.013.445-.266.494-.523.236-1.151 1.2-1.706 2.284-1.706.584 0 1.249.215 1.595.738.398.584.346 1.384.346 2.061zm-.533 5.906c-.451.8-1.169 1.291-1.967 1.291-1.09 0-1.728-.83-1.728-2.061 0-2.42 2.171-2.86 4.227-2.86v.615c.001 1.108.027 2.031-.532 3.015m7.634 5.251C18.329 21.076 14.917 22 11.979 22c-4.118 0-7.826-1.522-10.632-4.057-.22-.199-.024-.471.241-.317 3.027 1.762 6.771 2.823 10.639 2.823 2.608 0 5.476-.541 8.115-1.66.397-.169.73.262.341.55m.653 1.704c-.194.163-.379.076-.293-.139.284-.71.92-2.298.619-2.684s-1.99-.183-2.749-.092c-.23.027-.266-.173-.059-.319 1.348-.946 3.555-.673 3.811-.356.26.32-.066 2.533-1.329 3.59"})})})},{name:"behance",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M7.799 5.698c.589 0 1.12.051 1.606.156q.722.155 1.241.507.516.351.804.938c.188.387.281.871.281 1.443q0 .93-.421 1.551-.424.62-1.255 1.014 1.133.328 1.689 1.146.559.822.557 1.975 0 .935-.359 1.612a3.14 3.14 0 0 1-.973 1.114q-.613.432-1.399.637A6.1 6.1 0 0 1 7.963 18H2V5.698zm-.35 4.97q.721 0 1.192-.345.465-.344.463-1.119 0-.43-.152-.707a1.1 1.1 0 0 0-.416-.427 1.7 1.7 0 0 0-.596-.216 3.6 3.6 0 0 0-.697-.06H4.709v2.874zm.151 5.237q.401.001.759-.077c.243-.053.457-.137.637-.261.182-.12.332-.283.441-.491q.164-.31.163-.798-.002-.948-.533-1.357c-.356-.27-.83-.404-1.413-.404H4.709v3.388zm8.562-.041q.552.538 1.583.538.74 0 1.277-.374c.354-.248.571-.514.654-.79h2.155c-.347 1.072-.872 1.838-1.589 2.299-.708.463-1.572.693-2.58.693q-1.05 0-1.899-.337a4 4 0 0 1-1.439-.958 4.4 4.4 0 0 1-.904-1.484 5.4 5.4 0 0 1-.32-1.899q0-1 .329-1.863a4.4 4.4 0 0 1 .933-1.492q.607-.63 1.444-.994a4.6 4.6 0 0 1 1.857-.363q1.131-.001 1.98.44a3.94 3.94 0 0 1 1.389 1.181 4.8 4.8 0 0 1 .783 1.69q.24.947.171 1.983h-6.428c-.001.706.237 1.372.604 1.73m2.811-4.68c-.291-.321-.783-.496-1.384-.496q-.585 0-.973.2a2 2 0 0 0-.621.491 1.8 1.8 0 0 0-.328.628 2.7 2.7 0 0 0-.111.587h3.98c-.058-.625-.271-1.085-.563-1.41m-3.916-3.446h4.985V6.524h-4.985z"})})})},{name:"blogger-alt",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M19.779 9.904h-.981l-.021.001a1.163 1.163 0 0 1-1.16-1.079l-.001-.013A5.813 5.813 0 0 0 11.803 3H8.871a5.813 5.813 0 0 0-5.813 5.813v6.375a5.813 5.813 0 0 0 5.813 5.813h6.257a5.814 5.814 0 0 0 5.813-5.813l.002-4.121a1.164 1.164 0 0 0-1.164-1.163M8.726 7.713h3.291a1.117 1.117 0 1 1 0 2.234H8.726a1.117 1.117 0 1 1 0-2.234m6.601 8.657H8.72a1.057 1.057 0 1 1 0-2.114h6.607a1.057 1.057 0 1 1 0 2.114"})})})},{name:"blogger",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M14.722 14.019a.654.654 0 0 1-.654.654H9.977a.654.654 0 0 1 0-1.308h4.091c.361 0 .654.293.654.654m-4.741-3.321h2.038a.692.692 0 0 0 0-1.384H9.981a.692.692 0 0 0 0 1.384M21 5v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2m-3.456 6.39a.72.72 0 0 0-.72-.72h-.607l-.013.001a.72.72 0 0 1-.718-.668l-.001-.008a3.6 3.6 0 0 0-3.599-3.599H10.07a3.6 3.6 0 0 0-3.599 3.599v3.947a3.6 3.6 0 0 0 3.599 3.599h3.874a3.6 3.6 0 0 0 3.599-3.599z"})})})},{name:"bluesky",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M21.2 3.3c-.5-.2-1.4-.5-3.6 1C15.4 6 12.9 9.2 12 11c-.9-1.8-3.4-5-5.7-6.7-2.2-1.6-3-1.3-3.6-1S2 4.6 2 5.1s.3 4.7.5 5.4c.7 2.3 3.1 3.1 5.3 2.8-3.3.5-6.2 1.7-2.4 5.9 4.2 4.3 5.7-.9 6.5-3.6.8 2.7 1.7 7.7 6.4 3.6 3.6-3.6 1-5.4-2.3-5.9 2.2.2 4.6-.5 5.3-2.8.4-.7.7-4.8.7-5.4 0-.5-.1-1.5-.8-1.8"})})})},{name:"codepen",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"m22.016 8.84-.007-.037q-.006-.037-.015-.072-.007-.022-.013-.042l-.023-.062-.02-.042a.4.4 0 0 0-.03-.057l-.025-.038-.035-.052-.03-.037q-.021-.026-.043-.045-.015-.018-.035-.035a.4.4 0 0 0-.048-.04l-.037-.03-.015-.012-9.161-6.096a.86.86 0 0 0-.955 0L2.359 8.237l-.015.012-.038.028-.048.04a.638.638 0 0 0-.078.082q-.018.018-.03.037-.018.026-.035.052l-.025.038q-.016.031-.03.059l-.02.041a1 1 0 0 0-.034.106q-.01.034-.016.071-.003.02-.006.037a1 1 0 0 0-.009.114v6.093q0 .056.008.112l.007.038q.006.035.015.072a.2.2 0 0 0 .013.04q.01.032.022.063l.02.04a.4.4 0 0 0 .055.096l.035.052.03.037.042.045.035.035q.023.02.048.04l.038.03.013.01 9.163 6.095a.858.858 0 0 0 .959.004l9.163-6.095.015-.01q.02-.015.037-.03l.048-.04q.02-.017.035-.035.025-.024.043-.045l.03-.037.035-.052.025-.038a.4.4 0 0 0 .03-.058l.02-.04.023-.063c.003-.013.01-.027.013-.04q.009-.037.015-.072l.007-.037q.006-.062.007-.117V8.954a1 1 0 0 0-.008-.114m-9.154-4.376 6.751 4.49-3.016 2.013-3.735-2.492zm-1.724 0v4.009l-3.735 2.494-3.014-2.013zm-7.439 6.098L5.853 12l-2.155 1.438zm7.439 8.974-6.749-4.491 3.015-2.011 3.735 2.492zM12 14.035 8.953 12 12 9.966 15.047 12zm.862 5.501v-4.009l3.735-2.492 3.016 2.011zm7.441-6.098L18.147 12l2.156-1.438z"})})})},{name:"deezer",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M20.129 3.662c.222-1.287.548-2.096.909-2.098h.001c.673.002 1.219 2.809 1.219 6.274s-.546 6.274-1.22 6.274c-.276 0-.531-.477-.736-1.276-.324 2.926-.997 4.937-1.776 4.937-.603 0-1.144-1.208-1.507-3.114-.248 3.624-.872 6.195-1.602 6.195-.458 0-.875-1.019-1.184-2.678C13.861 21.6 13.003 24 12.002 24s-1.861-2.399-2.231-5.824c-.307 1.659-.724 2.678-1.184 2.678-.73 0-1.352-2.571-1.602-6.195-.363 1.905-.903 3.114-1.507 3.114-.778 0-1.452-2.011-1.776-4.937-.204.802-.46 1.276-.736 1.276-.674 0-1.22-2.809-1.22-6.274s.546-6.274 1.22-6.274c.362 0 .685.812.91 2.098.357-2.22.94-3.662 1.6-3.662.784 0 1.463 2.04 1.784 5.002.314-2.156.791-3.53 1.325-3.53.749 0 1.385 2.703 1.621 6.474.443-1.933 1.085-3.146 1.795-3.146s1.352 1.214 1.795 3.146c.237-3.771.872-6.474 1.621-6.474.533 0 1.009 1.374 1.325 3.53.321-2.962 1-5.002 1.784-5.002.658 0 1.244 1.443 1.603 3.662M0 7.221c0-1.549.31-2.805.692-2.805s.692 1.256.692 2.805-.31 2.805-.692 2.805S0 8.77 0 7.221m22.616 0c0-1.549.31-2.805.692-2.805S24 5.672 24 7.221s-.31 2.805-.692 2.805-.692-1.256-.692-2.805"})})})},{name:"discord",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M20.33 4.523A20 20 0 0 0 15.379 3a14 14 0 0 0-.634 1.289 18.4 18.4 0 0 0-5.495 0A14 14 0 0 0 8.615 3 20 20 0 0 0 3.66 4.527C.527 9.163-.323 13.684.102 18.141a20 20 0 0 0 6.073 3.049 14.7 14.7 0 0 0 1.301-2.097 13 13 0 0 1-2.048-.978q.258-.189.502-.378a14.27 14.27 0 0 0 12.142 0q.247.202.502.378a13 13 0 0 1-2.052.98 14.5 14.5 0 0 0 1.301 2.095 19.9 19.9 0 0 0 6.076-3.047c.498-5.168-.851-9.648-3.568-13.62M8.013 15.4c-1.183 0-2.161-1.074-2.161-2.395S6.796 10.6 8.01 10.6s2.183 1.083 2.163 2.405S9.22 15.4 8.013 15.4m7.974 0c-1.186 0-2.16-1.074-2.16-2.395s.944-2.405 2.16-2.405 2.178 1.083 2.157 2.405-.951 2.395-2.158 2.395"})})})},{name:"dribbble",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10m8.434-8.631c-.292-.092-2.644-.794-5.32-.365 1.117 3.07 1.572 5.57 1.659 6.09a8.56 8.56 0 0 0 3.661-5.725m-5.098 6.507c-.127-.749-.623-3.361-1.822-6.477l-.056.019c-4.818 1.679-6.547 5.02-6.701 5.334A8.5 8.5 0 0 0 12 20.555a8.5 8.5 0 0 0 3.336-.679m-9.682-2.152c.193-.331 2.538-4.213 6.943-5.637q.167-.054.337-.102a29 29 0 0 0-.692-1.45c-4.266 1.277-8.405 1.223-8.778 1.216a8.497 8.497 0 0 0 2.19 5.973m-2.015-7.46c.382.005 3.901.02 7.897-1.041a55 55 0 0 0-3.167-4.94 8.57 8.57 0 0 0-4.73 5.981m6.359-6.555a46 46 0 0 1 3.187 5c3.037-1.138 4.323-2.867 4.477-3.085a8.51 8.51 0 0 0-7.664-1.915m8.614 2.903c-.18.243-1.612 2.078-4.77 3.367a27 27 0 0 1 .751 1.678c2.842-.357 5.666.215 5.948.275a8.5 8.5 0 0 0-1.929-5.32"})})})},{name:"dropbox",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12 6.134 6.069 9.797 2 6.54l5.883-3.843zm-10 6.92 5.883 3.843L12 13.459 6.069 9.797zm10 .405 4.116 3.439L22 13.054l-4.069-3.257zM22 6.54l-5.884-3.843L12 6.134l5.931 3.663zm-9.989 7.66-4.129 3.426-1.767-1.153v1.291l5.896 3.539 5.897-3.539v-1.291l-1.769 1.153z"})})})},{name:"eventbrite",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M18.041 3.931 5.959 3A2.96 2.96 0 0 0 3 5.959v12.083A2.96 2.96 0 0 0 5.959 21l12.083-.931C19.699 19.983 21 18.744 21 17.11V6.89c0-1.634-1.259-2.863-2.959-2.959M16.933 8.17c-.082.215-.192.432-.378.551-.188.122-.489.132-.799.132-1.521 0-3.062-.048-4.607-.048q-.23 1.061-.451 2.128c.932-.004 1.873.005 2.81.005.726 0 1.462-.069 1.586.525.04.189-.001.426-.052.615-.105.38-.258.676-.625.783-.185.054-.408.058-.646.058-1.145 0-2.345.017-3.493.02-.169.772-.328 1.553-.489 2.333 1.57-.005 3.067-.041 4.633-.058.627-.007 1.085.194 1.009.85a2.2 2.2 0 0 1-.211.725c-.102.208-.248.376-.488.452-.237.075-.541.064-.862.078-.304.014-.614.008-.924.016-.309.009-.619.022-.919.022-1.253 0-2.429.08-3.683.073-.603-.004-1.014-.249-1.124-.757-.059-.273-.018-.58.036-.841a3543 3543 0 0 1 1.629-7.763c.056-.265.114-.511.225-.714a1.24 1.24 0 0 1 .79-.62c.368-.099.883-.047 1.344-.047.305 0 .612.008.914.016.925.026 1.817.03 2.747.053.304.007.615.016.915.016.621 0 1.17.073 1.245.614.039.288-.051.567-.132.783"})})})},{name:"facebook",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12 2C6.5 2 2 6.5 2 12c0 5 3.7 9.1 8.4 9.9v-7H7.9V12h2.5V9.8c0-2.5 1.5-3.9 3.8-3.9 1.1 0 2.2.2 2.2.2v2.5h-1.3c-1.2 0-1.6.8-1.6 1.6V12h2.8l-.4 2.9h-2.3v7C18.3 21.1 22 17 22 12c0-5.5-4.5-10-10-10"})})})},{name:"fediverse",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 743 743",children:(0,r.jsxs)("g",{children:[(0,r.jsx)("path",{d:"M181.131 275.137a68.9 68.9 0 0 1-29.465 29.328l161.758 162.389 38.998-19.764zm213.363 214.187-38.998 19.764 81.963 82.283a68.9 68.9 0 0 1 29.471-29.332zM581.646 339.391l-91.576 46.41 6.752 43.189 103.616-52.513a68.9 68.9 0 0 1-18.792-37.086m-144.738 73.351L220.383 522.477a68.9 68.9 0 0 1 18.795 37.089L443.66 455.934zM367.275 142.438l-104.48 203.97 30.848 30.967 110.623-215.957a68.9 68.9 0 0 1-36.991-18.98M235.621 399.459l-52.922 103.314a68.9 68.9 0 0 1 36.987 18.979l46.781-91.328zM150.768 304.918a68.9 68.9 0 0 1-34.416 7.195 69 69 0 0 1-6.651-.695l30.903 197.662a68.9 68.9 0 0 1 34.416-7.195 69 69 0 0 1 6.646.695zM239.342 560.545c.707 4.589.949 9.239.72 13.877a68.9 68.9 0 0 1-7.267 27.18l197.629 31.712c-.708-4.59-.95-9.24-.723-13.878a68.9 68.9 0 0 1 7.27-27.178zM601.133 377.199l-91.219 178.082a68.9 68.9 0 0 1 36.994 18.983l91.217-178.08a68.9 68.9 0 0 1-36.992-18.985M476.723 125.33a68.9 68.9 0 0 1-29.471 29.332l141.266 141.811a68.9 68.9 0 0 1 29.468-29.332zM347.787 104.631l-178.576 90.498a68.9 68.9 0 0 1 18.793 37.086l178.574-90.502a68.9 68.9 0 0 1-18.791-37.082M446.926 154.826a68.9 68.9 0 0 1-34.983 7.483 69 69 0 0 1-6.029-.633l15.818 101.291 43.163 6.926zm-16 167.028 37.4 239.482a68.9 68.9 0 0 1 33.914-6.943q3.625.206 7.207.791L474.09 328.777zM188.131 232.975c.734 4.66.988 9.383.758 14.095a68.9 68.9 0 0 1-7.16 26.983l101.369 16.281 19.923-38.908zm173.736 27.9-19.926 38.912 239.514 38.467a69 69 0 0 1-.695-13.719 68.9 68.9 0 0 1 7.349-27.324z"}),(0,r.jsx)("path",{fillOpacity:".996",d:"M412.284 156.054c34.538 1.882 64.061-24.592 65.943-59.13s-24.592-64.062-59.131-65.943c-34.538-1.882-64.061 24.592-65.943 59.13s24.593 64.062 59.131 65.943M646.144 390.82c34.538 1.881 64.062-24.593 65.943-59.131s-24.592-64.061-59.13-65.943-64.062 24.593-65.943 59.131 24.592 64.061 59.13 65.943M495.086 685.719c34.538 1.881 64.062-24.592 65.943-59.13s-24.592-64.062-59.13-65.943-64.062 24.592-65.943 59.13 24.592 64.062 59.13 65.943M167.866 633.211c34.538 1.882 64.062-24.592 65.943-59.13s-24.592-64.062-59.13-65.943-64.062 24.592-65.943 59.13 24.592 64.062 59.13 65.943M116.692 305.86c34.538 1.882 64.062-24.592 65.943-59.13s-24.592-64.062-59.131-65.943c-34.538-1.881-64.061 24.592-65.943 59.13s24.593 64.062 59.131 65.943"})]})})},{name:"feed",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M2 8.667V12c5.515 0 10 4.485 10 10h3.333c0-7.363-5.97-13.333-13.333-13.333M2 2v3.333c9.19 0 16.667 7.477 16.667 16.667H22C22 10.955 13.045 2 2 2m2.5 15a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5"})})})},{name:"flickr",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M6.5 7c-2.75 0-5 2.25-5 5s2.25 5 5 5 5-2.25 5-5-2.25-5-5-5m11 0c-2.75 0-5 2.25-5 5s2.25 5 5 5 5-2.25 5-5-2.25-5-5-5"})})})},{name:"foursquare",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M17.573 2H6.905C5.434 2 5 3.107 5 3.805v16.948c0 .785.422 1.077.66 1.172.238.097.892.177 1.285-.275 0 0 5.035-5.843 5.122-5.93.132-.132.132-.132.262-.132h3.26c1.368 0 1.588-.977 1.732-1.552.078-.318.692-3.428 1.225-6.122l.675-3.368C19.56 2.893 19.14 2 17.573 2m-1.078 5.22c-.053.252-.372.518-.665.518h-4.157c-.467 0-.802.318-.802.787v.508c0 .467.337.798.805.798h3.528c.331 0 .655.362.583.715s-.407 2.102-.448 2.295c-.04.193-.262.523-.655.523h-2.88c-.523 0-.683.068-1.033.503-.35.437-3.505 4.223-3.505 4.223-.032.035-.063.027-.063-.015V4.852c0-.298.26-.648.648-.648h8.562c.315 0 .61.297.528.683z"})})})},{name:"ghost",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M10.203 20.997H3.005v-3.599h7.198zm10.792-3.599h-7.193v3.599h7.193zm.003-7.198H3v3.599h17.998zm-7.195-7.197H3.005v3.599h10.798zm7.197 0h-3.599v3.599H21z"})})})},{name:"git",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M23.519 10.947 13.053.482a1.543 1.543 0 0 0-2.183 0L8.696 2.656l2.756 2.756a1.83 1.83 0 0 1 1.886.439 1.84 1.84 0 0 1 .436 1.898l2.656 2.657a1.83 1.83 0 0 1 1.899.436 1.837 1.837 0 0 1 0 2.597 1.84 1.84 0 0 1-2.599 0 1.84 1.84 0 0 1-.4-1.998l-2.478-2.477v6.521a1.837 1.837 0 0 1 .485 2.945 1.837 1.837 0 0 1-2.597 0 1.837 1.837 0 0 1 0-2.598 1.8 1.8 0 0 1 .602-.401V8.85a1.8 1.8 0 0 1-.602-.4 1.84 1.84 0 0 1-.395-2.009L7.628 3.723.452 10.898a1.544 1.544 0 0 0 0 2.184l10.467 10.467a1.544 1.544 0 0 0 2.183 0l10.417-10.418a1.546 1.546 0 0 0 0-2.184"})})})},{name:"github",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12 2C6.477 2 2 6.477 2 12c0 4.419 2.865 8.166 6.839 9.489.5.09.682-.218.682-.484 0-.236-.009-.866-.014-1.699-2.782.602-3.369-1.34-3.369-1.34-.455-1.157-1.11-1.465-1.11-1.465-.909-.62.069-.608.069-.608 1.004.071 1.532 1.03 1.532 1.03.891 1.529 2.341 1.089 2.91.833.091-.647.349-1.086.635-1.337-2.22-.251-4.555-1.111-4.555-4.943 0-1.091.39-1.984 1.03-2.682-.103-.254-.447-1.27.097-2.646 0 0 .84-.269 2.75 1.025A9.6 9.6 0 0 1 12 6.836c.85.004 1.705.114 2.504.336 1.909-1.294 2.748-1.025 2.748-1.025.546 1.376.202 2.394.1 2.646.64.699 1.026 1.591 1.026 2.682 0 3.841-2.337 4.687-4.565 4.935.359.307.679.917.679 1.852 0 1.335-.012 2.415-.012 2.741 0 .269.18.579.688.481A10 10 0 0 0 22 12c0-5.523-4.477-10-10-10"})})})},{name:"google-alt",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m-.05 16c-3.312 0-6-2.688-6-6s2.688-6 6-6c1.62 0 2.976.594 4.014 1.566L14.26 9.222c-.432-.408-1.188-.888-2.31-.888-1.986 0-3.606 1.65-3.606 3.672s1.62 3.672 3.606 3.672c2.298 0 3.144-1.59 3.3-2.532h-3.306v-2.238h5.616c.084.378.15.732.15 1.23 0 3.426-2.298 5.862-5.76 5.862"})})})},{name:"google-plus-alt",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M8 11h6.61c.06.35.11.7.11 1.16 0 4-2.68 6.84-6.72 6.84-3.87 0-7-3.13-7-7s3.13-7 7-7c1.89 0 3.47.69 4.69 1.83l-1.9 1.83c-.52-.5-1.43-1.08-2.79-1.08-2.39 0-4.34 1.98-4.34 4.42S5.61 16.42 8 16.42c2.77 0 3.81-1.99 3.97-3.02H8zm15 0h-2V9h-2v2h-2v2h2v2h2v-2h2"})})})},{name:"google-plus",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m-1.919 14.05a4.051 4.051 0 0 1 0-8.1c1.094 0 2.009.401 2.709 1.057l-1.15 1.118a2.23 2.23 0 0 0-1.559-.599c-1.341 0-2.434 1.114-2.434 2.479s1.094 2.479 2.434 2.479c1.551 0 2.122-1.073 2.227-1.709h-2.232v-1.511h3.791c.057.255.101.494.101.83.001 2.312-1.55 3.956-3.887 3.956M19 12.75h-1.25V14h-1.5v-1.25H15v-1.5h1.25V10h1.5v1.25H19z"})})})},{name:"google",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12.02 10.18v3.73h5.51c-.26 1.57-1.67 4.22-5.5 4.22-3.31 0-6.01-2.75-6.01-6.12s2.7-6.12 6.01-6.12c1.87 0 3.13.8 3.85 1.48l2.84-2.76C16.99 2.99 14.73 2 12.03 2c-5.52 0-10 4.48-10 10s4.48 10 10 10c5.77 0 9.6-4.06 9.6-9.77 0-.83-.11-1.42-.25-2.05z"})})})},{name:"instagram",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12 4.622c2.403 0 2.688.009 3.637.052.877.04 1.354.187 1.671.31.42.163.72.358 1.035.673s.51.615.673 1.035c.123.317.27.794.31 1.671.043.949.052 1.234.052 3.637s-.009 2.688-.052 3.637c-.04.877-.187 1.354-.31 1.671-.163.42-.358.72-.673 1.035s-.615.51-1.035.673c-.317.123-.794.27-1.671.31-.949.043-1.233.052-3.637.052s-2.688-.009-3.637-.052c-.877-.04-1.354-.187-1.671-.31a2.8 2.8 0 0 1-1.035-.673 2.8 2.8 0 0 1-.673-1.035c-.123-.317-.27-.794-.31-1.671-.043-.949-.052-1.234-.052-3.637s.009-2.688.052-3.637c.04-.877.187-1.354.31-1.671.163-.42.358-.72.673-1.035s.615-.51 1.035-.673c.317-.123.794-.27 1.671-.31.949-.043 1.234-.052 3.637-.052M12 3c-2.444 0-2.751.01-3.711.054-.958.044-1.612.196-2.184.418a4.4 4.4 0 0 0-1.594 1.039c-.5.5-.808 1.002-1.038 1.594-.223.572-.375 1.226-.419 2.184C3.01 9.249 3 9.556 3 12s.01 2.751.054 3.711c.044.958.196 1.612.418 2.185.23.592.538 1.094 1.038 1.594s1.002.808 1.594 1.038c.572.222 1.227.375 2.185.418.96.044 1.267.054 3.711.054s2.751-.01 3.711-.054c.958-.044 1.612-.196 2.185-.418a4.4 4.4 0 0 0 1.594-1.038c.5-.5.808-1.002 1.038-1.594.222-.572.375-1.227.418-2.185.044-.96.054-1.267.054-3.711s-.01-2.751-.054-3.711c-.044-.958-.196-1.612-.418-2.185A4.4 4.4 0 0 0 19.49 4.51c-.5-.5-1.002-.808-1.594-1.038-.572-.222-1.227-.375-2.185-.418C14.751 3.01 14.444 3 12 3m0 4.378a4.622 4.622 0 1 0 0 9.244 4.622 4.622 0 0 0 0-9.244M12 15a3 3 0 1 1 0-6 3 3 0 0 1 0 6m4.804-8.884a1.08 1.08 0 1 0 .001 2.161 1.08 1.08 0 0 0-.001-2.161"})})})},{name:"json-feed",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsxs)("g",{children:[(0,r.jsx)("path",{d:"m8.522 17.424.027.027c1.076-1.076 1.854-.993 3.154.306l2.053 2.053c2.136 2.136 4.131 2.028 6.515-.356l.729-.728-1.548-1.548-.373.373c-1.349 1.349-2.293 1.366-3.585.075l-2.409-2.409c-1.242-1.242-2.475-1.366-3.659-.381l-.232-.232c1.01-1.225.911-2.368-.29-3.568l-2.16-2.162c-1.317-1.317-1.308-2.236.058-3.602l.372-.372-1.54-1.54-.728.729c-2.393 2.393-2.525 4.346-.439 6.433l1.78 1.78c1.3 1.3 1.383 2.095.315 3.163l.008.008a1.384 1.384 0 0 0 1.952 1.951"}),(0,r.jsx)("circle",{cx:"13.089",cy:"10.905",r:"1.383"}),(0,r.jsx)("circle",{cx:"16.349",cy:"7.644",r:"1.383"}),(0,r.jsx)("circle",{cx:"19.61",cy:"4.383",r:"1.383"})]})})},{name:"line",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M14.255 9.572v3.333c0 .084-.066.15-.15.15h-.534a.16.16 0 0 1-.122-.061l-1.528-2.063v1.978c0 .084-.066.15-.15.15h-.534a.15.15 0 0 1-.15-.15V9.576c0-.084.066-.15.15-.15h.529a.14.14 0 0 1 .122.066l1.528 2.063V9.577c0-.084.066-.15.15-.15h.534a.15.15 0 0 1 .155.145m-3.844-.15h-.534a.15.15 0 0 0-.15.15v3.333c0 .084.066.15.15.15h.534c.084 0 .15-.066.15-.15V9.572c0-.08-.066-.15-.15-.15m-1.289 2.794H7.664V9.572a.15.15 0 0 0-.15-.15H6.98a.15.15 0 0 0-.15.15v3.333q0 .062.042.103a.16.16 0 0 0 .103.042h2.142c.084 0 .15-.066.15-.15v-.534a.15.15 0 0 0-.145-.15m7.945-2.794h-2.142c-.08 0-.15.066-.15.15v3.333c0 .08.066.15.15.15h2.142c.084 0 .15-.066.15-.15v-.534a.15.15 0 0 0-.15-.15h-1.458v-.563h1.458c.084 0 .15-.066.15-.15v-.539a.15.15 0 0 0-.15-.15h-1.458v-.563h1.458c.084 0 .15-.066.15-.15v-.534c-.005-.08-.07-.15-.15-.15M22.5 5.33v13.373c-.005 2.1-1.725 3.802-3.83 3.797H5.297c-2.1-.005-3.802-1.73-3.797-3.83V5.297c.005-2.1 1.73-3.802 3.83-3.797h13.373c2.1.005 3.802 1.725 3.797 3.83m-2.888 5.747c0-3.422-3.431-6.206-7.645-6.206s-7.645 2.784-7.645 6.206c0 3.066 2.719 5.634 6.394 6.122.895.192.792.52.591 1.725-.033.192-.155.755.661.413s4.402-2.592 6.009-4.439c1.106-1.219 1.636-2.452 1.636-3.82"})})})},{name:"link",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M17 13H7v-2h10zm1-6h-1c-1.631 0-3.065.792-3.977 2H18c1.103 0 2 .897 2 2v2c0 1.103-.897 2-2 2h-4.977c.913 1.208 2.347 2 3.977 2h1a4 4 0 0 0 4-4v-2a4 4 0 0 0-4-4M2 11v2a4 4 0 0 0 4 4h1c1.63 0 3.065-.792 3.977-2H6c-1.103 0-2-.897-2-2v-2c0-1.103.897-2 2-2h4.977C10.065 7.792 8.631 7 7 7H6a4 4 0 0 0-4 4"})})})},{name:"linkedin",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M19.7 3H4.3A1.3 1.3 0 0 0 3 4.3v15.4A1.3 1.3 0 0 0 4.3 21h15.4a1.3 1.3 0 0 0 1.3-1.3V4.3A1.3 1.3 0 0 0 19.7 3M8.339 18.338H5.667v-8.59h2.672zM7.004 8.574a1.548 1.548 0 1 1-.002-3.096 1.548 1.548 0 0 1 .002 3.096m11.335 9.764H15.67v-4.177c0-.996-.017-2.278-1.387-2.278-1.389 0-1.601 1.086-1.601 2.206v4.249h-2.667v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.779 3.203 4.092v4.711z"})})})},{name:"mail",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M20 4H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2m0 4.236-8 4.882-8-4.882V6h16z"})})})},{name:"mastodon",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M11.973 2.352c-2.468.02-4.842.286-6.225.921 0 0-2.742 1.229-2.742 5.415 0 .958-.018 2.105.012 3.32.1 4.094.75 8.128 4.535 9.129 1.745.462 3.244.56 4.45.494 2.19-.122 3.417-.781 3.417-.781l-.072-1.588s-1.565.491-3.32.431c-1.74-.06-3.576-.188-3.858-2.324a4 4 0 0 1-.04-.598s1.709.416 3.874.516c1.324.06 2.563-.076 3.824-.226 2.418-.29 4.524-1.78 4.79-3.141.416-2.144.38-5.232.38-5.232 0-4.186-2.74-5.415-2.74-5.415-1.383-.635-3.76-.9-6.227-.921zM9.18 5.622c1.028 0 1.804.395 2.318 1.185l.502.84.5-.84c.514-.79 1.292-1.186 2.32-1.186.888 0 1.605.313 2.15.922q.795.915.794 2.469v5.068h-2.008V9.16c0-1.037-.438-1.562-1.31-1.562-.966 0-1.448.622-1.448 1.857v2.693h-1.996V9.455c0-1.235-.484-1.857-1.45-1.857-.872 0-1.308.525-1.308 1.562v4.92H6.236V9.012q-.001-1.554.793-2.469c.547-.609 1.263-.922 2.15-.922"})})})},{name:"medium-alt",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{fillRule:"nonzero",d:"M7.423 6c3.27 0 5.922 2.686 5.922 6s-2.651 6-5.922 6S1.5 15.313 1.5 12s2.652-6 5.923-6m9.458.351c1.635 0 2.961 2.53 2.961 5.65 0 3.118-1.325 5.648-2.96 5.648S13.92 15.119 13.92 12s1.325-5.649 2.96-5.649m4.577.589c.576 0 1.042 2.265 1.042 5.06s-.466 5.06-1.042 5.06c-.575 0-1.04-2.265-1.04-5.06s.465-5.06 1.04-5.06"})})})},{name:"medium",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M3 3v18h18V3zm15 4.26-1 .93a.28.28 0 0 0-.11.27v6.8a.27.27 0 0 0 .11.27l.94.93v.2h-4.75v-.2l1-1c.09-.1.09-.12.09-.27V9.74l-2.71 6.9h-.37L8 9.74v4.62a.67.67 0 0 0 .17.54l1.27 1.54v.2H5.86v-.2l1.27-1.54a.64.64 0 0 0 .17-.54V9a.5.5 0 0 0-.16-.4L6 7.26v-.2h3.52L12.23 13l2.38-5.94H18z"})})})},{name:"messenger",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12.026.375C5.462.375.375 5.172.375 11.652c0 3.389 1.393 6.318 3.66 8.341.391.352.311.556.377 2.73a.934.934 0 0 0 1.307.823c2.48-1.092 2.512-1.178 2.933-1.064 7.185 1.977 14.973-2.621 14.973-10.83 0-6.48-5.035-11.277-11.599-11.277m6.996 8.678L15.6 14.47a1.75 1.75 0 0 1-2.527.465l-2.723-2.038a.7.7 0 0 0-.844 0l-3.674 2.786c-.49.372-1.133-.216-.802-.735l3.422-5.417a1.75 1.75 0 0 1 2.527-.465l2.722 2.037a.7.7 0 0 0 .844 0L18.22 8.32c.489-.374 1.132.213.801.732"})})})},{name:"microblog",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M19.641 17.086c1.294-1.522 2.067-3.438 2.067-5.521 0-4.957-4.371-8.972-9.763-8.972s-9.763 4.015-9.763 8.972 4.371 8.972 9.763 8.972a10.5 10.5 0 0 0 3.486-.59.315.315 0 0 1 .356.112c.816 1.101 2.09 1.876 3.506 2.191a.194.194 0 0 0 .192-.309 3.82 3.82 0 0 1 .162-4.858zm-3.065-6.575-2.514 1.909.912 3.022a.286.286 0 0 1-.437.317l-2.592-1.802-2.592 1.802a.285.285 0 0 1-.436-.317l.912-3.022-2.515-1.909a.285.285 0 0 1 .167-.513l3.155-.066 1.038-2.981a.285.285 0 0 1 .539 0l1.038 2.981 3.155.066a.285.285 0 0 1 .17.513"})})})},{name:"nextdoor",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",strokeMiterlimit:"10",viewBox:"0 0 130 130",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M64.25 3.531c-31.144.337-57.596 24.22-60.469 55.907-3.064 33.799 21.857 63.685 55.657 66.75s63.685-21.857 66.75-55.657-21.857-63.686-55.657-66.75a62 62 0 0 0-6.281-.25m3.938 34.907C82.468 38.438 93.5 48.58 93.5 61.5v27c0 .685-.565 1.25-1.25 1.25H80.906a1.267 1.267 0 0 1-1.25-1.25V63.375c0-5.58-4.309-11.937-11.469-11.937-7.47 0-11.468 6.357-11.468 11.937V88.5c0 .685-.565 1.25-1.25 1.25H44.125c-.68 0-1.219-.57-1.219-1.25V64.156c0-.74-.529-1.364-1.25-1.531-13.13-2.93-15.115-10.285-15.375-21.125-.005-.332.142-.67.375-.906.233-.237.543-.375.875-.375l11.688.062c.66.01 1.187.529 1.218 1.188.13 4.44.438 9.406 4.438 9.406.83 0 1.443-1.179 1.813-1.719 4.41-6.48 12.28-10.718 21.5-10.718"})})})},{name:"patreon",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M20 7.408c-.003-2.299-1.746-4.182-3.79-4.862-2.54-.844-5.888-.722-8.312.453-2.939 1.425-3.862 4.545-3.896 7.656-.028 2.559.22 9.297 3.92 9.345 2.75.036 3.159-3.603 4.43-5.356.906-1.247 2.071-1.599 3.506-1.963 2.465-.627 4.146-2.626 4.142-5.273"})})})},{name:"pinterest-alt",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12.289 2C6.617 2 3.606 5.648 3.606 9.622c0 1.846 1.025 4.146 2.666 4.878.25.111.381.063.439-.169.044-.175.267-1.029.365-1.428a.37.37 0 0 0-.091-.362c-.54-.63-.975-1.791-.975-2.873 0-2.777 2.194-5.464 5.933-5.464 3.23 0 5.49 2.108 5.49 5.122 0 3.407-1.794 5.768-4.13 5.768-1.291 0-2.257-1.021-1.948-2.277.372-1.495 1.089-3.112 1.089-4.191 0-.967-.542-1.775-1.663-1.775-1.319 0-2.379 1.309-2.379 3.059 0 1.115.394 1.869.394 1.869s-1.302 5.279-1.54 6.261c-.405 1.666.053 4.368.094 4.604.021.126.167.169.25.063.129-.165 1.699-2.419 2.142-4.051.158-.59.817-2.995.817-2.995.43.784 1.681 1.446 3.013 1.446 3.963 0 6.822-3.494 6.822-7.833C20.394 5.112 16.849 2 12.289 2"})})})},{name:"pinterest",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12 2C6.477 2 2 6.477 2 12c0 4.236 2.636 7.855 6.356 9.312-.087-.791-.166-2.005.035-2.869.182-.78 1.173-4.971 1.173-4.971s-.299-.599-.299-1.484c0-1.39.806-2.429 1.809-2.429.853 0 1.265.641 1.265 1.409 0 .858-.546 2.141-.828 3.329-.236.996.499 1.807 1.481 1.807 1.777 0 3.144-1.874 3.144-4.579 0-2.394-1.72-4.068-4.177-4.068-2.845 0-4.515 2.134-4.515 4.34 0 .859.331 1.781.744 2.282a.3.3 0 0 1 .069.287c-.077.316-.246.995-.279 1.134-.044.183-.145.222-.334.134-1.249-.581-2.03-2.407-2.03-3.874 0-3.154 2.292-6.051 6.607-6.051 3.469 0 6.165 2.472 6.165 5.775 0 3.446-2.173 6.22-5.189 6.22-1.013 0-1.966-.526-2.292-1.148l-.623 2.377c-.226.869-.835 1.957-1.243 2.622.936.289 1.93.445 2.961.445 5.523 0 10-4.477 10-10S17.523 2 12 2"})})})},{name:"pocket",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M21.927 4.194A1.82 1.82 0 0 0 20.222 3H3.839a1.823 1.823 0 0 0-1.813 1.814v6.035l.069 1.2c.29 2.73 1.707 5.115 3.899 6.778l.119.089.025.018a9.9 9.9 0 0 0 3.91 1.727 10.06 10.06 0 0 0 4.049-.014.3.3 0 0 0 .064-.023 9.9 9.9 0 0 0 3.753-1.691l.025-.018q.06-.043.119-.089c2.192-1.664 3.609-4.049 3.898-6.778l.069-1.2V4.814a1.8 1.8 0 0 0-.098-.62m-4.235 6.287-4.704 4.512a1.37 1.37 0 0 1-1.898 0l-4.705-4.512a1.371 1.371 0 1 1 1.898-1.979l3.756 3.601 3.755-3.601a1.372 1.372 0 0 1 1.898 1.979"})})})},{name:"polldaddy",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12 2C6.487 2 2 6.487 2 12c0 5.514 4.487 10 10 10 5.514 0 10-4.486 10-10 0-5.513-4.486-10-10-10m.991 1.68c2.361.084 4.657 1.251 6.197 3.136.283.334.541.693.774 1.067a7.78 7.78 0 0 0-6.094-2.94 7.76 7.76 0 0 0-5.896 2.703q-.008.006-.016.014l-.152.159-.031.032a6.12 6.12 0 0 0-1.633 4.165 6.15 6.15 0 0 0 6.143 6.143c.57 0 1.123-.081 1.649-.227-1.849.839-4.131.747-5.926-.324-1.841-1.089-3.171-3.111-3.433-5.313A7.39 7.39 0 0 1 6.69 6.137C8.294 4.5 10.634 3.563 12.991 3.68m3.373 8.519c-.049-2.024-1.587-3.889-3.544-4.174-1.927-.343-3.917.857-4.451 2.661a3.67 3.67 0 0 0 .2 2.653c.39.8 1.067 1.451 1.894 1.759 1.664.654 3.63-.27 4.173-1.863.593-1.58-.396-3.423-1.94-3.776-1.52-.407-3.161.757-3.204 2.243a2.36 2.36 0 0 0 .753 1.879c.501.476 1.23.667 1.871.529a2.07 2.07 0 0 0 1.469-1.134 1.91 1.91 0 0 0-.087-1.767c-.297-.513-.859-.863-1.429-.881a1.7 1.7 0 0 0-1.437.679 1.53 1.53 0 0 0-.18 1.489q.006.016.016.03c.193.634.774 1.1 1.467 1.117a1.6 1.6 0 0 1-.97-.183c-.466-.244-.809-.747-.893-1.29a1.8 1.8 0 0 1 .499-1.539 2.02 2.02 0 0 1 1.58-.606c.593.04 1.159.35 1.517.859.364.496.51 1.156.383 1.773-.116.62-.529 1.174-1.093 1.514a2.52 2.52 0 0 1-1.914.286c-.65-.161-1.226-.606-1.584-1.206a2.83 2.83 0 0 1-.341-2.031c.143-.7.573-1.321 1.176-1.753 1.193-.883 3.056-.751 4.106.411 1.106 1.1 1.327 3.027.406 4.371-.877 1.376-2.74 2.086-4.374 1.594-1.639-.449-2.913-2.079-3.031-3.853-.07-.884.13-1.797.583-2.577.445-.777 1.155-1.432 1.972-1.862 1.64-.88 3.816-.743 5.349.424 1.251.924 2.083 2.42 2.236 4.009l.001.03c0 2.9-2.359 5.26-5.26 5.26a5.2 5.2 0 0 1-1.947-.376 5 5 0 0 0 2.613-.079 4.96 4.96 0 0 0 2.514-1.751c.618-.828.95-1.861.901-2.869M12 21.113c-5.024 0-9.111-4.087-9.111-9.113 0-4.789 3.713-8.723 8.411-9.081a7 7 0 0 0-.397.06c-2.644.453-5.017 2.106-6.32 4.409-1.309 2.301-1.391 5.19-.3 7.527 1.056 2.34 3.253 4.156 5.776 4.553 2.497.44 5.133-.483 6.787-2.301 1.719-1.797 2.269-4.529 1.486-6.796-.583-1.81-1.976-3.331-3.7-4.046 3.417.594 6.174 3.221 6.174 6.781 0 1.004-.241 2.02-.657 2.966-1.498 2.984-4.586 5.041-8.149 5.041"})})})},{name:"print",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M9 16h6v2H9zm13 1h-3v3a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-3H2V9a2 2 0 0 1 2-2h1V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v2h1a2 2 0 0 1 2 2zM7 7h10V5H7zm10 7H7v6h10zm3-3.5a1.5 1.5 0 1 0-3.001.001A1.5 1.5 0 0 0 20 10.5"})})})},{name:"quora",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M47.736 16.521c-.41-.81-.898-1.631-1.846-1.631a1 1 0 0 0-.527.107l-.322-.644a2.93 2.93 0 0 1 1.836-.595c1.26 0 1.914.605 2.431 1.397a6.8 6.8 0 0 0 .449-2.675c0-2.773-.869-4.199-2.929-4.199-1.992 0-2.851 1.465-2.851 4.199s.859 4.17 2.851 4.17a4 4 0 0 0 .869-.107zm.498.966a6 6 0 0 1-1.367.185 5.27 5.27 0 0 1-5.263-5.204c0-3.114 2.558-5.233 5.263-5.233s5.282 2.109 5.282 5.233a5.08 5.08 0 0 1-1.992 4.072c.381.566.781.956 1.319.956.595 0 .839-.459.878-.82h.781c.049.488-.195 2.48-2.373 2.48-1.319 0-2.012-.761-2.529-1.66zm5.624-2.646v-3.563c0-.371-.146-.586-.615-.586h-.498v-.956h3.251v5.048c0 .849.459 1.231 1.161 1.231a1.56 1.56 0 0 0 1.465-.839V11.28c0-.371-.146-.586-.615-.586h-.527v-.957h3.28v5.302c0 .527.195.732.8.732h.107v.976l-2.929.468V16.21h-.057a3.12 3.12 0 0 1-2.509 1.152c-1.28 0-2.304-.644-2.304-2.558zm12.059 1.611c1.152 0 1.592-1.005 1.611-3.027.02-1.982-.459-2.929-1.611-2.929-1.005 0-1.641.956-1.641 2.929 0 2.021.625 3.027 1.641 3.027m0 .956a3.906 3.906 0 0 1-3.974-3.974c0-2.334 1.836-3.886 3.974-3.886 2.226 0 4.004 1.582 4.004 3.886a3.867 3.867 0 0 1-4.004 3.974m4.072-.146v-.956h.312c.781 0 .859-.224.859-.908v-4.121c0-.371-.215-.586-.732-.586h-.42v-.955h2.968l.146 1.553h.108c.371-1.113 1.221-1.699 2.051-1.699.693 0 1.221.39 1.221 1.181 0 .547-.264 1.093-1.005 1.093-.664 0-.8-.449-1.358-.449-.488 0-.869.468-.869 1.152v2.783c0 .673.166.908.937.908h.439v.956h-4.658zm9.901-1.093c.956 0 1.338-.898 1.338-1.797v-1.211c-.732.722-2.304.742-2.304 2.021 0 .625.371.986.966.986m1.387 0c-.39.752-1.191 1.26-2.314 1.26-1.309 0-2.148-.732-2.148-1.914 0-2.451 3.417-1.797 4.423-3.427v-.185c0-1.25-.488-1.445-1.035-1.445-1.524 0-.83 1.631-2.226 1.631-.673 0-.937-.371-.937-.859 0-.927 1.093-1.67 3.173-1.67 1.963 0 3.163.537 3.163 2.49v3.114q-.02.742.595.742a1 1 0 0 0 .449-.127l.254.615c-.205.312-.752.869-1.836.869-.908 0-1.465-.42-1.543-1.113h-.01zm-68.554 2.558c-.83-1.641-1.807-3.3-3.711-3.3a2.9 2.9 0 0 0-1.093.215l-.644-1.299a5.66 5.66 0 0 1 3.662-1.211c2.548 0 3.857 1.231 4.892 2.792q.917-2.012.908-5.38c0-5.585-1.748-8.417-5.829-8.417-4.033 0-5.76 2.87-5.76 8.417s1.738 8.397 5.76 8.397a5.9 5.9 0 0 0 1.748-.224zm.996 1.953a9.8 9.8 0 0 1-2.744.371C5.614 21.041.371 16.764.371 10.545.371 4.277 5.614 0 10.965 0c5.448 0 10.642 4.248 10.642 10.545a10.25 10.25 0 0 1-4.013 8.201c.732 1.152 1.563 1.914 2.665 1.914 1.201 0 1.689-.927 1.768-1.66h1.572c.088.966-.4 4.999-4.775 4.999-2.646 0-4.052-1.543-5.106-3.339z"})})})},{name:"reddit",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M22 11.816a2.28 2.28 0 0 0-2.277-2.277c-.593 0-1.122.24-1.526.614-1.481-.965-3.455-1.594-5.647-1.69l1.171-3.702 3.18.748a1.88 1.88 0 0 0 1.876 1.862 1.88 1.88 0 0 0 1.877-1.878 1.88 1.88 0 0 0-1.877-1.877c-.769 0-1.431.466-1.72 1.13l-3.508-.826a.386.386 0 0 0-.46.261l-1.35 4.268c-2.316.038-4.411.67-5.97 1.671a2.24 2.24 0 0 0-1.492-.581A2.28 2.28 0 0 0 2 11.816c0 .814.433 1.523 1.078 1.925a4 4 0 0 0-.061.672c0 3.292 4.011 5.97 8.941 5.97s8.941-2.678 8.941-5.97q-.002-.32-.053-.632A2.26 2.26 0 0 0 22 11.816m-3.224-7.422a1.1 1.1 0 1 1-.001 2.199 1.1 1.1 0 0 1 .001-2.199M2.777 11.816c0-.827.672-1.5 1.499-1.5.313 0 .598.103.838.269-.851.676-1.477 1.479-1.812 2.36a1.48 1.48 0 0 1-.525-1.129m9.182 7.79c-4.501 0-8.164-2.329-8.164-5.193S7.457 9.22 11.959 9.22s8.164 2.329 8.164 5.193-3.663 5.193-8.164 5.193m8.677-6.605c-.326-.89-.948-1.701-1.797-2.384.248-.186.55-.301.883-.301.827 0 1.5.673 1.5 1.5.001.483-.23.911-.586 1.185m-11.64 1.703c-.76 0-1.397-.616-1.397-1.376s.637-1.397 1.397-1.397 1.376.637 1.376 1.397-.616 1.376-1.376 1.376m7.405-1.376c0 .76-.616 1.376-1.376 1.376s-1.399-.616-1.399-1.376.639-1.397 1.399-1.397 1.376.637 1.376 1.397m-1.172 3.38a.39.39 0 0 1 0 .55c-.674.674-1.727 1.002-3.219 1.002l-.011-.002-.011.002c-1.492 0-2.544-.328-3.218-1.002a.389.389 0 1 1 .55-.55c.521.521 1.394.775 2.669.775l.011.002.011-.002c1.275 0 2.148-.253 2.669-.775a.387.387 0 0 1 .549 0"})})})},{name:"share",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M18 16c-.788 0-1.499.31-2.034.807L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.048 4.118A3 3 0 0 0 15 19a3 3 0 1 0 3-3"})})})},{name:"sms",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M17.696 4C20.069 4 22 5.973 22 8.398v4.357c0 2.04-1.368 3.783-3.261 4.266v4.427l-5.234-4.295h-7.2C3.93 17.153 2 15.18 2 12.755V8.398C2 5.973 3.931 4 6.304 4zM7.028 8.515c-.98 0-1.66.562-1.66 1.349-.009.497.322.91.985 1.178l.39.142c.242.097.305.171.305.297 0 .162-.131.251-.442.251s-.76-.135-1.004-.284l-.112.046-.215.868c.359.258.832.364 1.33.364 1.104 0 1.764-.523 1.764-1.333-.008-.574-.305-.956-.954-1.216l-.393-.146c-.266-.108-.341-.181-.341-.287 0-.152.131-.243.387-.243.274 0 .587.093.808.214l.109-.047.214-.837c-.315-.224-.741-.316-1.171-.316m10.302 0c-.98 0-1.66.562-1.66 1.349-.008.497.322.91.985 1.178l.39.142c.243.097.305.171.305.297 0 .162-.13.251-.442.251-.311 0-.76-.135-1.004-.284l-.112.046-.215.868c.359.258.832.364 1.33.364 1.104 0 1.764-.523 1.764-1.333-.008-.574-.305-.956-.954-1.216l-.393-.146c-.266-.108-.341-.181-.341-.287 0-.152.131-.243.387-.243.274 0 .587.093.808.214l.109-.047.214-.837c-.316-.224-.741-.316-1.171-.316m-3.733 0c-.297 0-.55.066-.78.202l-.144.098a2 2 0 0 0-.264.247l-.078.095-.027-.077c-.15-.34-.55-.565-1.033-.565l-.169.007a1.36 1.36 0 0 0-.896.42l-.08.09-.038-.363-.075-.067H8.994l-.075.079.024.634c.005.2.008.397.008.604v2.652l.075.075h1.178l.075-.075v-2.269q-.002-.168.042-.274c.083-.23.262-.392.496-.392.314 0 .483.267.483.753v2.182l.075.075h1.179l.075-.075v-2.277c0-.097.016-.213.043-.285.077-.224.26-.373.486-.373.33 0 .5.272.5.817v2.118l.074.075h1.179l.075-.075v-2.293c0-1.131-.537-1.763-1.39-1.763Z"})})})},{name:"snapchat",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M11.989 1.728c3.221.001 5.904 2.683 5.908 5.912q.002 1.133.067 2.094a.737.737 0 0 0 .902.669l1.009-.237a.6.6 0 0 1 .129-.015c.256 0 .492.175.55.434a.74.74 0 0 1-.479.861l-1.532.618a.823.823 0 0 0-.485.98c1.229 4.543 4.661 4.071 4.661 4.662 0 .743-2.587.848-2.821 1.082s-.01 1.368-.532 1.588a1.1 1.1 0 0 1-.409.056c-.393 0-.95-.077-1.536-.077-.509 0-1.04.058-1.507.273-1.239.572-2.433 1.641-3.914 1.641S9.325 21.2 8.086 20.628c-.467-.216-.998-.273-1.507-.273-.586 0-1.143.077-1.536.077-.17 0-.31-.014-.409-.056-.522-.22-.299-1.354-.532-1.588s-2.821-.337-2.821-1.08c0-.592 3.432-.119 4.661-4.662a.824.824 0 0 0-.486-.98l-1.532-.618a.74.74 0 0 1-.479-.861.56.56 0 0 1 .679-.419l1.009.237q.086.02.169.02a.737.737 0 0 0 .733-.689q.065-.961.067-2.094c.004-3.229 2.666-5.91 5.887-5.912m0-1.281c-.961 0-1.898.194-2.784.574A7.2 7.2 0 0 0 6.93 2.572a7.2 7.2 0 0 0-1.539 2.282A7.1 7.1 0 0 0 4.82 7.64a33 33 0 0 1-.029 1.369l-.375-.088a2 2 0 0 0-.421-.049 1.86 1.86 0 0 0-1.135.389 1.84 1.84 0 0 0-.666 1.049 2.024 2.024 0 0 0 1.271 2.335l1.124.454c-.744 2.285-2.117 2.723-3.041 3.018a5 5 0 0 0-.659.246C.087 16.76 0 17.436 0 17.708c0 .521.247.996.694 1.339.223.17.499.311.844.43.47.162 1.016.265 1.459.347.021.164.053.341.106.518.22.738.684 1.069 1.034 1.217.332.14.676.156.905.156.224 0 .462-.018.713-.036.269-.02.548-.041.823-.041.426 0 .743.051.97.155.311.144.64.337.989.542.972.571 2.073 1.217 3.462 1.217s2.49-.647 3.462-1.217c.349-.205.679-.399.989-.542.226-.105.544-.155.97-.155.275 0 .554.021.823.041.251.019.488.036.713.036.229 0 .573-.016.905-.156.35-.147.814-.478 1.034-1.217.053-.178.084-.354.106-.518.443-.082.989-.185 1.459-.347.345-.119.621-.259.844-.43.448-.342.694-.818.694-1.339 0-.272-.087-.948-.891-1.347a5 5 0 0 0-.659-.246c-.924-.295-2.297-.733-3.041-3.018l1.124-.454a2.025 2.025 0 0 0 1.271-2.335 1.83 1.83 0 0 0-.666-1.049 1.86 1.86 0 0 0-1.556-.34l-.375.088a33 33 0 0 1-.029-1.369 7.1 7.1 0 0 0-.575-2.789c-.365-.853-.886-1.62-1.547-2.282s-1.428-1.182-2.28-1.547a7.1 7.1 0 0 0-2.786-.574"})})})},{name:"soundcloud",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M23.587 13.923a3.303 3.303 0 0 1-3.344 3.117h-8.037a.674.674 0 0 1-.667-.67V7.717a.74.74 0 0 1 .444-.705s.739-.512 2.296-.512a5.27 5.27 0 0 1 2.702.742 5.35 5.35 0 0 1 2.516 3.485 3.1 3.1 0 0 1 .852-.116 3.217 3.217 0 0 1 3.237 3.312m-13.05-5.659c.242 2.935.419 5.612 0 8.538a.261.261 0 0 1-.519 0c-.39-2.901-.221-5.628 0-8.538a.26.26 0 0 1 .398-.25.26.26 0 0 1 .12.25zm-1.627 8.541a.273.273 0 0 1-.541 0 32.7 32.7 0 0 1 0-7.533.274.274 0 0 1 .544 0 29.4 29.4 0 0 1-.003 7.533m-1.63-7.788c.264 2.69.384 5.099-.003 7.782a.262.262 0 0 1-.522 0c-.374-2.649-.249-5.127 0-7.782a.264.264 0 0 1 .525 0m-1.631 7.792a.268.268 0 0 1-.532 0 27.6 27.6 0 0 1 0-7.034.27.27 0 1 1 .541 0 25.8 25.8 0 0 1-.01 7.034zm-1.63-5.276c.412 1.824.227 3.435-.015 5.294a.255.255 0 0 1-.504 0c-.22-1.834-.402-3.482-.015-5.295a.268.268 0 0 1 .535 0m-1.626-.277c.378 1.869.254 3.451-.01 5.325-.031.277-.506.28-.531 0-.239-1.846-.352-3.476-.01-5.325a.277.277 0 0 1 .551 0m-1.643.907c.396 1.239.261 2.246-.016 3.517a.258.258 0 0 1-.514 0c-.239-1.246-.336-2.274-.021-3.517a.276.276 0 0 1 .55 0z"})})})},{name:"spotify",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m4.586 14.424a.62.62 0 0 1-.857.207c-2.348-1.435-5.304-1.76-8.785-.964a.622.622 0 1 1-.277-1.215c3.809-.871 7.077-.496 9.713 1.115a.623.623 0 0 1 .206.857M17.81 13.7a.78.78 0 0 1-1.072.257c-2.687-1.652-6.785-2.131-9.965-1.166A.779.779 0 1 1 6.32 11.3c3.632-1.102 8.147-.568 11.234 1.328a.78.78 0 0 1 .256 1.072m.105-2.835c-3.223-1.914-8.54-2.09-11.618-1.156a.935.935 0 1 1-.542-1.79c3.532-1.072 9.404-.865 13.115 1.338a.936.936 0 1 1-.955 1.608"})})})},{name:"squarespace",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M20.87 9.271a3.86 3.86 0 0 0-5.458 0l-6.141 6.141a.964.964 0 1 0 1.365 1.364l6.14-6.14a1.929 1.929 0 1 1 2.729 2.729l-6.022 6.022a1.93 1.93 0 0 0 2.729 0l4.658-4.658a3.86 3.86 0 0 0 0-5.458m-2.047 2.047a.965.965 0 0 0-1.365 0l-6.14 6.14a1.93 1.93 0 0 1-2.729 0 .964.964 0 1 0-1.364 1.364 3.86 3.86 0 0 0 5.458 0l6.14-6.14a.966.966 0 0 0 0-1.364m-2.047-6.141a3.86 3.86 0 0 0-5.458 0l-6.14 6.14a.964.964 0 1 0 1.364 1.364l6.141-6.14a1.93 1.93 0 0 1 2.729 0 .965.965 0 1 0 1.364-1.364m-2.047 2.047a.964.964 0 0 0-1.364 0l-6.14 6.141a1.929 1.929 0 1 1-2.729-2.729l6.022-6.022a1.93 1.93 0 0 0-2.729 0L3.13 9.271a3.86 3.86 0 0 0 5.458 5.458l6.14-6.141a.963.963 0 0 0 .001-1.364"})})})},{name:"stackexchange",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M4 11.606h16v3.272H4zM4 7.377h16v3.272H4zM17.514 3H6.55C5.147 3 4 4.169 4 5.614v.848h16v-.85C20 4.167 18.895 3 17.514 3M4 15.813v.85c0 1.445 1.147 2.614 2.55 2.614h6.799v3.463l3.357-3.463h.744c1.402 0 2.55-1.169 2.55-2.614v-.85z"})})})},{name:"stackoverflow",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsxs)("g",{children:[(0,r.jsx)("path",{d:"M18.18 20.103V14.78h1.767v7.09H4v-7.09h1.767v5.323z"}),(0,r.jsx)("path",{d:"m7.717 14.275 8.673 1.813.367-1.744-8.673-1.813zm1.147-4.13 8.031 3.74.734-1.606-8.031-3.763zm2.226-3.946 6.815 5.667 1.124-1.354-6.815-5.667zM15.495 2l-1.423 1.055 5.277 7.113 1.423-1.055zM7.533 18.314h8.857v-1.767H7.533z"})]})})},{name:"stumbleupon",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12 4.294a4.47 4.47 0 0 0-4.471 4.471v6.353a1.059 1.059 0 1 1-2.118 0v-2.824H2v2.941a4.471 4.471 0 0 0 8.942 0v-6.47a1.059 1.059 0 1 1 2.118 0v1.294l1.412.647 2-.647V8.765A4.473 4.473 0 0 0 12 4.294m1.059 8.059v2.882a4.471 4.471 0 0 0 8.941 0v-2.824h-3.412v2.824a1.059 1.059 0 1 1-2.118 0v-2.882l-2 .647z"})})})},{name:"substack",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M19.904 9.182H4.095V7.054h15.81v2.127M4.095 11.109V21L12 16.583 19.905 21v-9.891zM19.905 3H4.095v2.127h15.81z"})})})},{name:"telegram",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2m3.08 14.757s-.25.625-.936.325l-2.541-1.949-1.63 1.486s-.127.096-.266.036c0 0-.12-.011-.27-.486s-.911-2.972-.911-2.972L6 12.349s-.387-.137-.425-.438c-.037-.3.437-.462.437-.462l10.03-3.934s.824-.362.824.238z"})})})},{name:"threads",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 192 192",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M141.537 88.988a67 67 0 0 0-2.518-1.143c-1.482-27.307-16.403-42.94-41.457-43.1h-.34c-14.986 0-27.449 6.396-35.12 18.036l13.779 9.452c5.73-8.695 14.724-10.548 21.348-10.548h.229c8.249.053 14.474 2.452 18.503 7.129 2.932 3.405 4.893 8.111 5.864 14.05-7.314-1.243-15.224-1.626-23.68-1.14-23.82 1.371-39.134 15.264-38.105 34.568.522 9.792 5.4 18.216 13.735 23.719 7.047 4.652 16.124 6.927 25.557 6.412 12.458-.683 22.231-5.436 29.049-14.127 5.178-6.6 8.453-15.153 9.899-25.93 5.937 3.583 10.337 8.298 12.767 13.966 4.132 9.635 4.373 25.468-8.546 38.376-11.319 11.308-24.925 16.2-45.488 16.351-22.809-.169-40.06-7.484-51.275-21.742C35.236 139.966 29.808 120.682 29.605 96c.203-24.682 5.63-43.966 16.133-57.317C56.954 24.425 74.204 17.11 97.013 16.94c22.975.17 40.526 7.52 52.171 21.847 5.71 7.026 10.015 15.86 12.853 26.162l16.147-4.308c-3.44-12.68-8.853-23.606-16.219-32.668C147.036 9.607 125.202.195 97.07 0h-.113C68.882.194 47.292 9.642 32.788 28.08 19.882 44.485 13.224 67.315 13.001 95.932L13 96v.067c.224 28.617 6.882 51.447 19.788 67.854C47.292 182.358 68.882 191.806 96.957 192h.113c24.96-.173 42.554-6.708 57.048-21.189 18.963-18.945 18.392-42.692 12.142-57.27-4.484-10.454-13.033-18.945-24.723-24.553M98.44 129.507c-10.44.588-21.286-4.098-21.82-14.135-.397-7.442 5.296-15.746 22.461-16.735q2.948-.17 5.79-.169c6.235 0 12.068.606 17.371 1.765-1.978 24.702-13.58 28.713-23.802 29.274"})})})},{name:"tiktok-alt",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M5 3a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2zm7.531 3h2.053s-.114 2.635 2.85 2.82v2.04s-1.582.099-2.85-.87l.021 4.207a3.804 3.804 0 1 1-3.802-3.802h.533v2.082a1.73 1.73 0 0 0-1.922.648 1.727 1.727 0 0 0 1.947 2.646 1.73 1.73 0 0 0 1.19-1.642z"})})})},{name:"tiktok",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12.22 2h3.42s-.19 4.394 4.75 4.702v3.396s-2.636.166-4.75-1.448l.037 7.012a6.338 6.338 0 1 1-6.34-6.339h.89v3.472a2.882 2.882 0 1 0 2.024 2.752z"})})})},{name:"tripadvisor",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsxs)("g",{children:[(0,r.jsx)("path",{d:"M21.01 9.859c.236-1.002.985-2.003.985-2.003l-3.341-.002C16.779 6.643 14.502 6 11.979 6 9.363 6 7 6.659 5.135 7.877L2 7.88s.74.988.98 1.983a4.98 4.98 0 0 0-.977 2.961 5.01 5.01 0 0 0 5.009 5.003 5 5 0 0 0 3.904-1.875l1.065 1.592 1.076-1.606a4.96 4.96 0 0 0 1.838 1.448 4.98 4.98 0 0 0 3.831.151 5.01 5.01 0 0 0 2.963-6.431 5 5 0 0 0-.679-1.247m-13.998 6.96a4 4 0 0 1-3.998-3.995 4 4 0 0 1 3.998-3.997 4 4 0 0 1 3.996 3.997 4 4 0 0 1-3.996 3.995m4.987-4.36A5.007 5.007 0 0 0 7.11 7.821c1.434-.613 3.081-.947 4.867-.947 1.798 0 3.421.324 4.853.966a4.984 4.984 0 0 0-4.831 4.619m6.288 4.134a3.97 3.97 0 0 1-3.058-.122 3.96 3.96 0 0 1-2.075-2.245v-.001a3.97 3.97 0 0 1 .118-3.056 3.97 3.97 0 0 1 2.246-2.077 4.005 4.005 0 0 1 5.135 2.366 4.006 4.006 0 0 1-2.366 5.135"}),(0,r.jsx)("path",{d:"M6.949 10.307a2.477 2.477 0 0 0-2.475 2.472 2.48 2.48 0 0 0 2.475 2.474 2.474 2.474 0 0 0 0-4.946m0 4.094a1.626 1.626 0 0 1-1.624-1.623 1.621 1.621 0 1 1 1.624 1.623M16.981 10.307a2.477 2.477 0 0 0-2.474 2.472 2.48 2.48 0 0 0 2.474 2.474 2.476 2.476 0 0 0 2.472-2.474 2.475 2.475 0 0 0-2.472-2.472m0 4.094a1.625 1.625 0 0 1-1.622-1.623 1.622 1.622 0 1 1 1.622 1.623"}),(0,r.jsx)("path",{d:"M7.778 12.778a.832.832 0 1 1-1.664.002.832.832 0 0 1 1.664-.002M16.981 11.947a.832.832 0 1 0 .002 1.666.832.832 0 0 0-.002-1.666"})]})})},{name:"tumblr-alt",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M16.749 17.396c-.357.17-1.041.319-1.551.332-1.539.041-1.837-1.081-1.85-1.896V9.847h3.861v-2.91h-3.847V2.039h-2.817c-.046 0-.127.041-.138.144-.165 1.499-.867 4.13-3.783 5.181v2.484h1.945v6.282c0 2.151 1.587 5.206 5.775 5.135 1.413-.024 2.982-.616 3.329-1.126z"})})})},{name:"tumblr",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-5.569 14.265c-2.446.042-3.372-1.742-3.372-2.998v-3.668H8.923v-1.45c1.703-.614 2.113-2.15 2.209-3.025.007-.06.054-.084.081-.084h1.645V8.9h2.246v1.7H12.85v3.495c.008.476.182 1.131 1.081 1.107.298-.008.697-.094.906-.194l.54 1.601c-.205.296-1.121.641-1.946.656"})})})},{name:"twitch",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M16.499 8.089h-1.636v4.91h1.636zm-4.499 0h-1.637v4.91H12zM4.228 3.178 3 6.451v13.092h4.499V22h2.456l2.454-2.456h3.681L21 14.636V3.178zm15.136 10.638L16.5 16.681H12l-2.453 2.453V16.68H5.863V4.814h13.501z"})})})},{name:"twitter-alt",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M22.23 5.924a8.2 8.2 0 0 1-2.357.646 4.12 4.12 0 0 0 1.804-2.27 8.2 8.2 0 0 1-2.606.996 4.103 4.103 0 0 0-6.991 3.742 11.65 11.65 0 0 1-8.457-4.287 4.1 4.1 0 0 0-.556 2.063 4.1 4.1 0 0 0 1.825 3.415 4.1 4.1 0 0 1-1.859-.513v.052a4.104 4.104 0 0 0 3.292 4.023 4.1 4.1 0 0 1-1.853.07 4.11 4.11 0 0 0 3.833 2.85 8.24 8.24 0 0 1-5.096 1.756 8 8 0 0 1-.979-.057 11.6 11.6 0 0 0 6.29 1.843c7.547 0 11.675-6.252 11.675-11.675q0-.267-.012-.531a8.3 8.3 0 0 0 2.047-2.123"})})})},{name:"twitter",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-2.534 6.71q.007.148.007.298c0 3.045-2.318 6.556-6.556 6.556a6.5 6.5 0 0 1-3.532-1.035q.27.032.55.032a4.63 4.63 0 0 0 2.862-.986 2.31 2.31 0 0 1-2.152-1.6 2.3 2.3 0 0 0 1.04-.04 2.306 2.306 0 0 1-1.848-2.259v-.029c.311.173.666.276 1.044.288a2.303 2.303 0 0 1-.713-3.076 6.54 6.54 0 0 0 4.749 2.407 2.305 2.305 0 0 1 3.926-2.101 4.6 4.6 0 0 0 1.463-.559 2.3 2.3 0 0 1-1.013 1.275c.466-.056.91-.18 1.323-.363-.31.461-.7.867-1.15 1.192"})})})},{name:"untappd",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"m11 13.299-5.824 8.133c-.298.416-.8.635-1.308.572-.578-.072-1.374-.289-2.195-.879S.392 19.849.139 19.323a1.4 1.4 0 0 1 .122-1.425l5.824-8.133a3.1 3.1 0 0 1 1.062-.927l1.146-.604c.23-.121.436-.283.608-.478.556-.631 2.049-2.284 4.696-4.957l.046-.212a.13.13 0 0 1 .096-.1l.146-.037a.135.135 0 0 0 .101-.141l-.015-.18a.13.13 0 0 1 .125-.142c.176-.005.518.046 1.001.393s.64.656.692.824a.13.13 0 0 1-.095.164l-.175.044a.13.13 0 0 0-.101.141l.012.15a.13.13 0 0 1-.063.123l-.186.112c-1.679 3.369-2.764 5.316-3.183 6.046a2.2 2.2 0 0 0-.257.73l-.205 1.281A3.1 3.1 0 0 1 11 13.3zm12.739 4.598-5.824-8.133a3.1 3.1 0 0 0-1.062-.927l-1.146-.605a2.1 2.1 0 0 1-.608-.478 51 51 0 0 0-.587-.654.09.09 0 0 0-.142.018 97 97 0 0 1-1.745 3.223 1.4 1.4 0 0 0-.171.485 3.5 3.5 0 0 0 0 1.103l.01.064c.075.471.259.918.536 1.305l5.824 8.133c.296.413.79.635 1.294.574a4.76 4.76 0 0 0 2.209-.881 4.76 4.76 0 0 0 1.533-1.802 1.4 1.4 0 0 0-.122-1.425zM8.306 3.366l.175.044a.134.134 0 0 1 .101.141l-.012.15a.13.13 0 0 0 .063.123l.186.112q.465.933.869 1.721c.026.051.091.06.129.019q.655-.703 1.585-1.668a.137.137 0 0 0 .003-.19c-.315-.322-.645-.659-1.002-1.02l-.046-.212a.13.13 0 0 0-.096-.099l-.146-.037a.135.135 0 0 1-.101-.141l.015-.18a.13.13 0 0 0-.123-.142c-.175-.005-.518.045-1.002.393-.483.347-.64.656-.692.824a.13.13 0 0 0 .095.164z"})})})},{name:"vimeo",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M22.396 7.164q-.139 3.039-4.245 8.32Q13.907 21 10.97 21q-1.82 0-3.079-3.359l-1.68-6.159q-.934-3.36-2.005-3.36-.234.001-1.634.98l-.978-1.261q1.541-1.353 3.037-2.708 2.056-1.774 3.084-1.869 2.429-.234 2.99 3.321.607 3.836.841 4.769.7 3.181 1.542 3.181.653 0 1.963-2.065 1.307-2.063 1.401-3.142.187-1.781-1.401-1.782-.747.001-1.541.341 1.534-5.024 5.862-4.884 3.21.095 3.024 4.161"})})})},{name:"vk",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M1.687 1.687C0 3.374 0 6.09 0 11.52v.96c0 5.431 0 8.146 1.687 9.833S6.09 24 11.52 24h.96c5.431 0 8.146 0 9.833-1.687S24 17.91 24 12.48v-.96c0-5.431 0-8.146-1.687-9.833S17.91 0 12.48 0h-.96C6.09 0 3.374 0 1.687 1.687M4.05 7.3c.13 6.24 3.25 9.99 8.72 9.99h.31v-3.57c2.01.2 3.53 1.67 4.14 3.57h2.84c-.78-2.84-2.83-4.41-4.11-5.01 1.28-.74 3.08-2.54 3.51-4.98h-2.58c-.56 1.98-2.22 3.78-3.8 3.95V7.3H10.5v6.92c-1.6-.4-3.62-2.34-3.71-6.92z"})})})},{name:"whatsapp",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"m2.048 22 1.406-5.136a9.9 9.9 0 0 1-1.323-4.955C2.133 6.446 6.579 2 12.042 2a9.85 9.85 0 0 1 7.011 2.906 9.85 9.85 0 0 1 2.9 7.011c-.002 5.464-4.448 9.91-9.91 9.91h-.004a9.9 9.9 0 0 1-4.736-1.206zm5.497-3.172.301.179a8.2 8.2 0 0 0 4.193 1.148h.003c4.54 0 8.235-3.695 8.237-8.237a8.2 8.2 0 0 0-2.41-5.828 8.18 8.18 0 0 0-5.824-2.416c-4.544 0-8.239 3.695-8.241 8.237a8.2 8.2 0 0 0 1.259 4.384l.196.312-.832 3.04zm9.49-4.554c-.062-.103-.227-.165-.475-.289s-1.465-.723-1.692-.806-.392-.124-.557.124-.64.806-.784.971-.289.186-.536.062-1.046-.385-1.991-1.229c-.736-.657-1.233-1.468-1.378-1.715s-.015-.382.109-.505c.111-.111.248-.289.371-.434.124-.145.165-.248.248-.413s.041-.31-.021-.434-.557-1.343-.763-1.839c-.202-.483-.407-.417-.559-.425-.144-.007-.31-.009-.475-.009a.9.9 0 0 0-.66.31c-.226.248-.866.847-.866 2.066s.887 2.396 1.011 2.562 1.746 2.666 4.23 3.739c.591.255 1.052.408 1.412.522.593.189 1.133.162 1.56.098.476-.071 1.465-.599 1.671-1.177.206-.58.206-1.075.145-1.179"})})})},{name:"woocommerce",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M19 2H5C3.3 2 2 3.3 2 5v11c0 1.7 1.3 3 3 3h4l6 3-1-3h5c1.7 0 3-1.3 3-3V5c0-1.7-1.3-3-3-3m-1.6 4.5c-.4.8-.8 2.1-1 3.9-.3 1.8-.4 3.1-.3 4.1 0 .3 0 .5-.1.7s-.3.4-.6.4-.6-.1-.9-.4c-1-1-1.8-2.6-2.4-4.6-.7 1.4-1.2 2.4-1.6 3.1-.6 1.2-1.2 1.8-1.6 1.9-.3 0-.5-.2-.8-.7-.5-1.4-1.1-4.2-1.7-8.2 0-.3 0-.5.2-.7.1-.2.4-.3.7-.4.5 0 .9.2.9.8.3 2.3.7 4.2 1.1 5.7l2.4-4.5c.2-.4.4-.6.8-.6q.75 0 .9.9c.3 1.4.6 2.6 1 3.7.3-2.7.8-4.7 1.4-5.9.2-.3.4-.5.7-.5.2 0 .5.1.7.2q.3.3.3.6c0 .3 0 .4-.1.5"})})})},{name:"wordpress",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M12.158 12.786 9.46 20.625a9 9 0 0 0 5.526-.144 1 1 0 0 1-.065-.124zM3.009 12a8.99 8.99 0 0 0 5.067 8.092L3.788 8.341A8.95 8.95 0 0 0 3.009 12m15.06-.454c0-1.112-.399-1.881-.741-2.48-.456-.741-.883-1.368-.883-2.109 0-.826.627-1.596 1.51-1.596q.06.002.116.007A8.96 8.96 0 0 0 12 3.009a8.98 8.98 0 0 0-7.512 4.052c.211.007.41.011.579.011.94 0 2.396-.114 2.396-.114.484-.028.541.684.057.741 0 0-.487.057-1.029.085l3.274 9.739 1.968-5.901-1.401-3.838c-.484-.028-.943-.085-.943-.085-.485-.029-.428-.769.057-.741 0 0 1.484.114 2.368.114.94 0 2.397-.114 2.397-.114.485-.028.542.684.057.741 0 0-.488.057-1.029.085l3.249 9.665.897-2.996q.684-1.753.684-2.907m1.82-3.86q.06.428.06.924c0 .912-.171 1.938-.684 3.22l-2.746 7.94a8.98 8.98 0 0 0 4.47-7.771 8.9 8.9 0 0 0-1.1-4.313M12 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"})})})},{name:"x",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M13.982 10.622 20.54 3h-1.554l-5.693 6.618L8.745 3H3.5l6.876 10.007L3.5 21h1.554l6.012-6.989L15.868 21h5.245zm-2.128 2.474-.697-.997-5.543-7.93H8l4.474 6.4.697.996 5.815 8.318h-2.387z"})})})},{name:"xanga",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M9 9h6v6H9zM3 9h6V3H3zm12 0h6V3h-6zm0 12h6v-6h-6zM3 21h6v-6H3z"})})})},{name:"youtube",svg:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)("g",{children:(0,r.jsx)("path",{d:"M21.8 8.001s-.195-1.378-.795-1.985c-.76-.797-1.613-.801-2.004-.847-2.799-.202-6.997-.202-6.997-.202h-.009s-4.198 0-6.997.202c-.39.047-1.242.051-2.003.847-.6.607-.795 1.985-.795 1.985S2 9.62 2 11.238v1.517c0 1.618.2 3.237.2 3.237s.195 1.378.795 1.985c.761.797 1.76.771 2.205.855 1.6.153 6.8.201 6.8.201s4.203-.006 7.001-.209c.391-.047 1.243-.051 2.004-.847.6-.607.795-1.985.795-1.985s.2-1.618.2-3.237v-1.517c0-1.618-.2-3.237-.2-3.237M9.935 14.594l-.001-5.62 5.404 2.82z"})})})}]},8992:(e,t,s)=>{"use strict";var r=s(8120),n=s.n(r),a=s(1609);s(1135),s(790);a.PureComponent,n().string.isRequired,n().number,n().func,n().string},295:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(1455),n=s.n(r),a=s(6185),i=s.n(a);const o={getAccountProtection:()=>n()({path:"jetpack-protect/v1/account-protection",method:"GET"}),toggleAccountProtection:()=>n()({method:"POST",path:"jetpack-protect/v1/toggle-account-protection"}),getWaf:()=>n()({path:"jetpack-protect/v1/waf",method:"GET"}).then(i()),toggleWaf:()=>n()({method:"POST",path:"jetpack-protect/v1/toggle-waf"}),updateWaf:e=>n()({method:"POST",path:"jetpack/v4/waf",data:e}).then(i()),wafSeen:()=>n()({path:"jetpack-protect/v1/waf-seen",method:"POST"}),wafUpgradeSeen:()=>n()({path:"jetpack-protect/v1/waf-upgrade-seen",method:"POST"}),getOnboardingProgress:()=>n()({path:"jetpack-protect/v1/onboarding-progress",method:"GET"}),completeOnboardingSteps:e=>n()({path:"jetpack-protect/v1/onboarding-progress",method:"POST",data:{step_ids:e}}),getScanHistory:()=>n()({path:"jetpack-protect/v1/scan-history",method:"GET"}).then(i()),scan:()=>n()({path:"jetpack-protect/v1/scan",method:"POST"}),getScanStatus:()=>n()({path:"jetpack-protect/v1/status?hard_refresh=true",method:"GET"}).then(i()),fixThreats:e=>n()({path:"jetpack-protect/v1/fix-threats",method:"POST",data:{threat_ids:e}}).then(i()),getFixersStatus:e=>{const t=e.reduce(((e,t)=>`${e}threat_ids[]=${t}&`),"jetpack-protect/v1/fix-threats-status?");return n()({path:t,method:"GET"}).then(i())},ignoreThreat:e=>n()({path:`jetpack-protect/v1/ignore-threat?threat_id=${e}`,method:"POST"}),unIgnoreThreat:e=>n()({path:`jetpack-protect/v1/unignore-threat?threat_id=${e}`,method:"POST"}),checkCredentials:()=>n()({path:"jetpack-protect/v1/check-credentials",method:"POST"}),checkPlan:()=>n()({path:"jetpack-protect/v1/check-plan",method:"GET"}),getProductData:()=>n()({path:"/my-jetpack/v1/site/products?products=scan",method:"GET"}).then((e=>i()(e?.scan)))}},1186:(e,t,s)=>{"use strict";s.d(t,{A:()=>f});var r=s(2947),n=s(1608),a=s(5918),i=s(9384),o=s(7723),c=s(1609),l=s(880),d=s(4537),u=s(9701),h=s(3041),p=s(241),m=s(2770),g=s(790);const __=o.__,f=({children:e})=>{const{notice:t}=(0,d.A)(),{isRegistered:s}=(0,i.useConnection)(),f=(0,l.Zp)(),{counts:{current:{threats:v}}}=(0,u.A)();return(0,c.useEffect)((()=>{s||f("/setup")}),[s,f]),s?(0,g.jsxs)(r.A,{moduleName:__("Jetpack Protect","jetpack-protect"),header:(0,g.jsx)(n.A,{}),children:[t&&(0,g.jsx)(h.A,{floating:!0,dismissable:!0,...t}),(0,g.jsx)(a.A,{horizontalSpacing:0,children:(0,g.jsxs)(p.A,{className:m.A.navigation,children:[(0,g.jsx)(p.o,{link:"/scan",label:(0,g.jsx)("span",{className:m.A.tab,children:v>0?(0,o.sprintf)(
// translators: %d is the number of threats found.
__("Scan (%d)","jetpack-protect"),v):__("Scan","jetpack-protect")})}),(0,g.jsx)(p.o,{link:"/firewall",label:(0,g.jsx)("span",{className:m.A.tab,children:__("Firewall","jetpack-protect")})}),(0,g.jsx)(p.o,{link:"/settings",label:(0,g.jsx)("span",{className:m.A.tab,children:__("Settings","jetpack-protect")})})]})}),e]}):null}},7715:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(5918),n=s(8509),a=s(9384),i=s(9067),o=s(790);const c=()=>{const{hasConnectionError:e}=(0,a.useConnectionErrorNotice)();return(0,o.jsxs)(r.A,{horizontalSpacing:0,children:[e&&(0,o.jsx)(n.A,{className:i.A["connection-error-col"],children:(0,o.jsx)(a.ConnectionError,{})}),(0,o.jsx)(n.A,{children:(0,o.jsx)("div",{id:"jp-admin-notices",className:"my-jetpack-jitm-card"})})]})}},7031:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(766),n=s(8478),a=s(7425),i=s(3127),o=s(7715),c=s(9067),l=s(790);const d=({main:e,secondary:t,preserveSecondaryOnMobile:s=!0,spacing:n=7})=>(0,l.jsxs)(r.A,{children:[(0,l.jsx)(o.A,{}),(0,l.jsx)(i.A,{spacing:n,gap:0,main:e,mainClassName:c.A["header-main"],secondary:t,secondaryClassName:c.A["header-secondary"],preserveSecondaryOnMobile:s,fluid:!1})]});d.Heading=({children:e,showIcon:t=!1})=>{const s=(0,n.Wy)("protect");return(0,l.jsxs)(a.H3,{className:c.A.heading,mt:2,mb:2,children:[e,t&&(0,l.jsx)(s,{className:c.A["heading-icon"],size:32})]})},d.Subheading=({children:e})=>(0,l.jsx)("div",{className:c.A.subheading,children:e});const u=d},4907:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(1112),n=s(6427),a=s(541),i=s(790);function o({children:e,...t}){return(0,i.jsx)(n.Flex,{gap:0,className:`components-button-group ${a.A["button-group"]}`,...t,children:e})}o.Button=({onClick:e,variant:t="secondary",children:s,...n})=>(0,i.jsx)(r.A,{onClick:e,variant:t,className:"components-button",...n,children:(0,i.jsx)("span",{children:s})});const c=o},2652:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(7425),n=s(7723),a=s(1113),i=s(3751),o=s(7031),c=s(9889),l=s(3490),d=s(790);const __=n.__,u=({baseErrorMessage:e,errorMessage:t,errorCode:s})=>{let n=t?`${t} (${s}).`:e;return n+=" "+__("Try again in a few minutes.","jetpack-protect"),(0,d.jsx)(o.A,{main:(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(o.A.Heading,{children:(0,d.jsxs)("div",{className:l.A.heading,children:[(0,d.jsx)(a.A,{className:l.A.warning,icon:i.A,size:54}),__("An error occurred","jetpack-protect")]})}),(0,d.jsx)(o.A.Subheading,{children:(0,d.jsx)(r.Ay,{children:n})}),(0,d.jsx)("div",{className:l.A["scan-navigation"],children:(0,d.jsx)(c.A,{})})]}),preserveSecondaryOnMobile:!1})}},8680:(e,t,s)=>{"use strict";s.d(t,{A:()=>p});var r=s(7425),n=s(1112),a=s(7723),i=s(1609),o=s(3657),c=s(1009),l=s(1571),d=s(4031),u=s(4906),h=s(790);const __=a.__,p=({threatList:e=[]})=>{const{setModal:t}=(0,c.A)(),{fixThreats:s,isLoading:a}=(0,o.Ay)(),p=(0,i.useMemo)((()=>e.filter((e=>e.fixable&&!1!==e.fixable.extras?.isBulkFixable))),[e]),m=(0,i.useMemo)((()=>e.filter((e=>!e.fixable||!1===e.fixable.extras?.isBulkFixable))),[e]),[g,f]=(0,i.useState)(p.map((({id:e})=>parseInt(e)))),v=(0,i.useCallback)((e=>{e.preventDefault(),t({type:null})}),[t]),j=(0,i.useCallback)((async e=>{e.preventDefault(),await s(g),t({type:null})}),[s,t,g]),x=(0,i.useCallback)(((e,t)=>{f(e?[...g,t.id]:g.filter((e=>e!==t.id)))}),[g]);return(0,h.jsxs)(d.A,{children:[(0,h.jsx)(r.Ay,{variant:"title-medium",mb:2,children:__("Fix all threats","jetpack-protect")}),p.length>0&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(r.Ay,{mb:3,children:__("Jetpack will be fixing the selected threats:","jetpack-protect")}),(0,h.jsx)("div",{className:u.A.list,children:p.map((e=>(0,h.jsx)(l.A,{threat:e,fixAllDialog:!0,onCheckFix:x},e.id)))})]}),m.length>0&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(r.Ay,{mb:3,mr:6,children:__("These threats cannot be fixed in bulk because individual confirmation is required:","jetpack-protect")}),(0,h.jsx)("div",{className:u.A.list,children:m.map((e=>(0,h.jsx)(l.A,{threat:e,fixAllDialog:!1,onCheckFix:x},e.id)))})]}),(0,h.jsxs)("div",{className:u.A.footer,children:[(0,h.jsx)(n.A,{variant:"secondary",onClick:v,children:__("Cancel","jetpack-protect")}),p.length>0&&(0,h.jsx)(n.A,{isLoading:a,onClick:j,disabled:!g.length,children:__("Fix all threats","jetpack-protect")})]})]})}},2607:(e,t,s)=>{"use strict";s.d(t,{A:()=>f});var r=s(7425),n=s(1112),a=s(6427),i=s(6087),o=s(7723),c=s(1609),l=s(3657),d=s(1009),u=s(3041),h=s(1571),p=s(4031),m=s(1697),g=s(790);const __=o.__,f=({id:e,signature:t,extension:s,fixable:f,label:v,icon:j,severity:x})=>{const{setModal:y}=(0,d.A)(),{fixThreats:b,isLoading:w}=(0,l.Ay)(),A="Vulnerable.WP.Extension"===t&&f&&"delete"===f.fixer,k=s?.slug||"unknown-slug",[C,_]=(0,c.useState)("");(0,c.useEffect)((()=>{_("")}),[e]);const S=()=>async t=>{t.preventDefault(),await b([e]),y({type:null})};return(0,g.jsxs)(p.A,{children:[(0,g.jsx)(r.Ay,{variant:"title-medium",mb:2,children:__("Fix Threat","jetpack-protect")}),(0,g.jsx)(r.Ay,{mb:3,children:__("Jetpack will be fixing the selected threat:","jetpack-protect")}),(0,g.jsx)("div",{className:m.A.list,children:(0,g.jsx)(h.A,{threat:{id:e,fixable:f,label:v,icon:j,severity:x},fixAllDialog:!1})}),A&&(0,g.jsxs)(g.Fragment,{children:["active"===f.extensionStatus?(0,g.jsx)(u.A,{type:"error",message:"plugin"===s?.type?__("This plugin seems to be currently active on your site. Deleting it may break your site. Please disable it first and check if your site is still working as expected, then proceed with the fix.","jetpack-protect"):__("This theme seems to be currently active on your site. Deleting it may break your site. Please disable it first and check if your site is still working as expected, then proceed with the fix.","jetpack-protect",0)}):(0,g.jsx)(u.A,{type:"warning",message:"plugin"===s?.type?__("This plugin seems to not currently be active on your site. Please note that deleting it may still have adverse effects and this action cannot be undone.","jetpack-protect"):__("This theme seems to not currently be active on your site. Please note that deleting it may still have adverse effects and this action cannot be undone.","jetpack-protect",0)}),!1===f.extras?.isDotorg&&(0,g.jsx)(r.Ay,{mb:3,mt:3,children:"plugin"===s?.type?__("We did not find this plugin on WordPress.org. We encourage you to create a backup of your site before fixing this threat, to keep a copy of it.","jetpack-protect"):__("We did not find this theme on WordPress.org. We encourage you to create a backup of your site before fixing this threat, to keep a copy of it.","jetpack-protect",0)}),(0,g.jsx)(r.Ay,{mb:3,mt:3,children:"plugin"===s?.type?(0,i.createInterpolateElement)((0,o.sprintf)(/* translators: %s is the plugin slug itself, e.g. jetpack-protect. */
__("To confirm you have read and understood the consequences, please enter the plugin slug <code>%s</code> in the field below.","jetpack-protect"),k),{code:(0,g.jsx)("code",{})}):(0,i.createInterpolateElement)((0,o.sprintf)(/* translators: %s is the theme slug itself, e.g. twentytwentyfive. */
__("To confirm you have read and understood the consequences, please enter the theme slug <code>%s</code> in the field below.","jetpack-protect"),k),{code:(0,g.jsx)("code",{})})}),(0,g.jsx)(a.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,help:"",label:"",onChange:e=>{_(e)},value:C,className:"deletion-confirmation",autoComplete:"off"})]}),(0,g.jsxs)("div",{className:m.A.footer,children:[(0,g.jsx)(n.A,{variant:"secondary",onClick:e=>{e.preventDefault(),y({type:null})},children:__("Cancel","jetpack-protect")}),A?(0,g.jsx)(n.A,{variant:"primary",isDestructive:!0,disabled:C!==k,isLoading:w,onClick:S(),children:__("Delete now","jetpack-protect")}):(0,g.jsx)(n.A,{isLoading:w,onClick:S(),children:__("Fix threat","jetpack-protect")})]})]})}},4114:(e,t,s)=>{"use strict";s.d(t,{A:()=>p,E:()=>h});var r=s(7425),n=s(1113),a=s(8248),i=s(4969),o=s(3022),c=s(1609),l=s(8442),d=s(790);const u=(0,c.createContext)(),h=({id:e,title:t,label:s,icon:h,children:p,onOpen:m})=>{const g=(0,c.useContext)(u),f=g?.open===e,v=g?.setOpen,j=(0,o.A)(l.A["accordion-body"],{[l.A["accordion-body-open"]]:f,[l.A["accordion-body-close"]]:!f}),x=(0,c.useCallback)((()=>{f||m?.(),v((t=>t===e?null:e))}),[f,m,v,e]);return(0,d.jsxs)("div",{className:l.A["accordion-item"],children:[(0,d.jsxs)("button",{className:l.A["accordion-header"],onClick:x,children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)(r.Ay,{className:l.A["accordion-header-label"],mb:1,children:[(0,d.jsx)(n.A,{icon:h,className:l.A["accordion-header-label-icon"]}),s]}),(0,d.jsx)(r.Ay,{className:l.A["accordion-header-description"],variant:f?"title-small":"body",children:t})]}),(0,d.jsx)("div",{className:l.A["accordion-header-button"],children:(0,d.jsx)(n.A,{icon:f?a.A:i.A,size:38})})]}),(0,d.jsx)("div",{className:j,"aria-hidden":f?"false":"true",children:p})]})},p=({children:e})=>{const[t,s]=(0,c.useState)();return(0,d.jsx)(u.Provider,{value:{open:t,setOpen:s},children:(0,d.jsx)("div",{className:l.A.accordion,children:e})})}},5920:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var r=s(3924),n=s(7425),a=s(1112),i=s(8418),o=s(6087),c=s(7723),l=s(1113),d=s(8847),u=s(1009),h=s(4031),p=s(2166),m=s(790);const __=c.__,g=({id:e,title:t,label:s,icon:c,severity:g})=>{const{setModal:f}=(0,u.A)(),v=(0,d.A)(),j=(0,r.A)("jetpack-protect-codeable-referral"),[x,y]=(0,o.useState)(!1);return(0,m.jsxs)(h.A,{children:[(0,m.jsx)(n.Ay,{variant:"title-medium",mb:2,children:__("Do you really want to ignore this threat?","jetpack-protect")}),(0,m.jsx)(n.Ay,{mb:3,children:__("Jetpack will ignore the threat:","jetpack-protect")}),(0,m.jsxs)("div",{className:p.A.threat,children:[(0,m.jsx)(l.A,{icon:c,className:p.A.threat__icon}),(0,m.jsxs)("div",{className:p.A.threat__summary,children:[(0,m.jsx)(n.Ay,{className:p.A.threat__summary__label,mb:1,children:s}),(0,m.jsx)(n.Ay,{className:p.A.threat__summary__title,children:t})]}),(0,m.jsx)("div",{className:p.A.threat__severity,children:(0,m.jsx)(i.Z6,{severity:g})})]}),(0,m.jsx)(n.Ay,{mb:4,children:(0,o.createInterpolateElement)(__("By choosing to ignore this threat, you acknowledge that you have reviewed the detected code. You are accepting the risks of maintaining a potentially malicious or vulnerable file on your site. If you are unsure, please request an estimate with <codeableLink>Codeable</codeableLink>.","jetpack-protect"),{codeableLink:(0,m.jsx)(a.A,{variant:"link",isExternalLink:!0,href:j})})}),(0,m.jsxs)("div",{className:p.A.footer,children:[(0,m.jsx)(a.A,{variant:"secondary",onClick:e=>{e.preventDefault(),f({type:null})},children:__("Cancel","jetpack-protect")}),(0,m.jsx)(a.A,{isDestructive:!0,isLoading:x,onClick:async t=>{t.preventDefault(),y(!0),await v.mutateAsync(e),f({type:null}),y(!1)},children:__("Ignore threat","jetpack-protect")})]})]})}},9671:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(7633),n=s(790);const a=()=>(0,n.jsxs)("svg",{width:"440",height:"367",viewBox:"0 0 440 367",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:r.A.inProgressAnimation,children:[(0,n.jsxs)("g",{className:r.A.inProgressAnimation__el,children:[(0,n.jsx)("g",{filter:"url(#filter_wordpress_el)",children:(0,n.jsx)("rect",{className:"rect-1",x:"40",y:"211",width:"360",height:"116",rx:"4",fill:"white"})}),(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M127 269C127 253.572 114.428 241 99 241C83.544 241 71 253.572 71 269C71 284.456 83.544 297 99 297C114.428 297 127 284.456 127 269ZM92.784 284.036L83.236 258.416C84.776 258.36 86.512 258.192 86.512 258.192C87.912 258.024 87.744 255.028 86.344 255.084C86.344 255.084 82.284 255.392 79.708 255.392C79.204 255.392 78.672 255.392 78.084 255.364C82.536 248.532 90.236 244.108 99 244.108C105.524 244.108 111.46 246.544 115.94 250.66C114.036 250.352 111.32 251.752 111.32 255.084C111.32 256.898 112.286 258.455 113.372 260.205L113.372 260.205C113.527 260.454 113.683 260.706 113.84 260.964C114.82 262.672 115.38 264.772 115.38 267.852C115.38 272.024 111.46 281.852 111.46 281.852L102.976 258.416C104.488 258.36 105.272 257.94 105.272 257.94C106.672 257.8 106.504 254.44 105.104 254.524C105.104 254.524 101.072 254.86 98.44 254.86C96.004 254.86 91.916 254.524 91.916 254.524C90.516 254.44 90.348 257.884 91.748 257.94L94.324 258.164L97.852 267.712L92.784 284.036ZM119.809 268.837L119.748 269C117.719 274.341 115.706 279.728 113.696 285.105L113.696 285.106L113.696 285.106L113.694 285.111C112.986 287.004 112.279 288.896 111.572 290.784C119.048 286.472 123.892 278.212 123.892 269C123.892 264.688 122.912 260.712 120.952 257.1C121.794 263.568 120.5 267.002 119.809 268.837ZM88.08 291.652C79.736 287.62 74.108 278.884 74.108 269C74.108 265.36 74.752 262.056 76.124 258.948C76.9623 261.244 77.8006 263.542 78.6392 265.841L78.6401 265.843L78.6404 265.844C81.7786 274.446 84.9206 283.058 88.08 291.652ZM106.588 292.632L99.364 273.088C98.0331 277.014 96.6922 280.941 95.3474 284.879C94.4288 287.568 93.5084 290.264 92.588 292.968C94.604 293.584 96.788 293.892 99 293.892C101.66 293.892 104.18 293.444 106.588 292.632Z",fill:"#E9EFF5"}),(0,n.jsx)("path",{d:"M160 283C160 279.686 162.686 277 166 277H287C290.314 277 293 279.686 293 283C293 286.314 290.314 289 287 289H166C162.686 289 160 286.314 160 283Z",fill:"#E9EFF5"}),(0,n.jsx)("path",{d:"M160 255C160 251.686 162.686 249 166 249H360C363.314 249 366 251.686 366 255C366 258.314 363.314 261 360 261H166C162.686 261 160 258.314 160 255Z",fill:"#E9EFF5"})]}),(0,n.jsxs)("g",{className:r.A.inProgressAnimation__el,children:[(0,n.jsx)("g",{filter:"url(#filter_plugins_el)",children:(0,n.jsx)("rect",{x:"72",y:"68",width:"168",height:"120",rx:"3",fill:"#A0C5D7"})}),(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M152 108L152 118H159.5V108L163.25 108V118H167C168.381 118 169.5 119.119 169.5 120.5V130.5L162 140.5V145.5C162 146.881 160.881 148 159.5 148H152C150.619 148 149.5 146.881 149.5 145.5V140.5L142 130.5V120.5C142 119.119 143.119 118 144.5 118H148.25L148.25 108L152 108ZM153.25 139.25V144.25H158.25V139.25L165.75 129.25V121.75H145.75V129.25L153.25 139.25Z",fill:"white"})]}),(0,n.jsxs)("g",{className:r.A.inProgressAnimation__el,children:[(0,n.jsx)("g",{filter:"url(#filter_themes_el)",children:(0,n.jsx)("rect",{x:"272",y:"40",width:"96",height:"132",rx:"3",fill:"#EED77B"})}),(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M320.238 122.25C326.583 122.25 331.726 117.107 331.726 110.762C331.726 109.608 331.141 107.683 329.776 105.088C328.472 102.609 326.691 99.9488 324.845 97.4584C323.206 95.2483 321.561 93.2314 320.238 91.6723C318.915 93.2314 317.27 95.2483 315.631 97.4584C313.785 99.9488 312.004 102.609 310.7 105.088C309.335 107.683 308.75 109.608 308.75 110.762C308.75 117.107 313.893 122.25 320.238 122.25ZM317.739 88.8229C313.417 93.8726 305 104.507 305 110.762C305 119.178 311.822 126 320.238 126C328.654 126 335.476 119.178 335.476 110.762C335.476 104.507 327.06 93.8726 322.737 88.8229C321.243 87.078 320.238 86 320.238 86C320.238 86 319.233 87.078 317.739 88.8229Z",fill:"white"})]}),(0,n.jsxs)("defs",{children:[(0,n.jsxs)("filter",{id:"filter_wordpress_el",x:"0",y:"171",width:"440",height:"196",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,n.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,n.jsx)("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),(0,n.jsx)("feOffset",{}),(0,n.jsx)("feGaussianBlur",{stdDeviation:"20"}),(0,n.jsx)("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"}),(0,n.jsx)("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_2754_20065"}),(0,n.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_2754_20065",result:"shape"})]}),(0,n.jsxs)("filter",{id:"filter_plugins_el",x:"32",y:"28",width:"248",height:"200",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,n.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,n.jsx)("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),(0,n.jsx)("feOffset",{}),(0,n.jsx)("feGaussianBlur",{stdDeviation:"20"}),(0,n.jsx)("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"}),(0,n.jsx)("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_2754_20065"}),(0,n.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_2754_20065",result:"shape"})]}),(0,n.jsxs)("filter",{id:"filter_themes_el",x:"232",y:"0",width:"176",height:"212",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,n.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,n.jsx)("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),(0,n.jsx)("feOffset",{}),(0,n.jsx)("feGaussianBlur",{stdDeviation:"20"}),(0,n.jsx)("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"}),(0,n.jsx)("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_2754_20065"}),(0,n.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_2754_20065",result:"shape"})]})]})]})},4440:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var r=s(7723),n=s(1113),a=s(991),i=s(1009),o=s(8680),c=s(2607),l=s(5920),d=s(3144),u=s(9623),h=s(7222),p=s(790);const __=r.__,m={IGNORE_THREAT:l.A,UNIGNORE_THREAT:u.A,FIX_THREAT:c.A,FIX_ALL_THREATS:o.A,STANDALONE_MODE:d.A},g=()=>{const{modal:e,setModal:t}=(0,i.A)();if(!e.type)return null;const s=m[e.type];return(0,p.jsx)("div",{className:h.A.modal,children:(0,p.jsxs)("div",{className:h.A.modal__window,children:[(0,p.jsx)("button",{onClick:e=>{e.preventDefault(),t({type:null})},className:h.A.modal__close,title:__("Close Modal Window","jetpack-protect"),children:(0,p.jsx)(n.A,{icon:a.A,size:24,className:h.A.modal__close__icon,"aria-label":__("Close Modal Window","jetpack-protect")})}),(0,p.jsx)(s,{...e.props})]})})}},9278:(e,t,s)=>{"use strict";s.d(t,{A:()=>f});var r=s(7425),n=s(6427),a=s(7723),i=s(1113),o=s(9783),c=s(3883),l=s(8120),d=s.n(l),u=s(1609),h=s(2425),p=s(8065),m=s(790);const __=a.__,g=({count:e,checked:t})=>{const{data:s}=(0,h.Ay)(),{popoverText:a,badgeElement:l}=((e,t)=>t?0===e?{popoverText:__("No known threats found to affect this version","jetpack-protect"),badgeElement:(0,m.jsx)(i.A,{icon:c.A,size:28,className:p.A["navigation-item-check-badge"]})}:{popoverText:null,badgeElement:(0,m.jsx)(r.Ay,{variant:"body-extra-small",className:p.A["navigation-item-badge"],component:"div",children:e})}:{popoverText:__("This item was added to your site after the most recent scan. We will check for threats during the next scheduled one.","jetpack-protect"),badgeElement:(0,m.jsx)(i.A,{icon:o.A,size:28,className:p.A["navigation-item-info-badge"]})})(e,t),[d,g]=(0,u.useState)(!1),f=(0,u.useMemo)((()=>(0,h.EV)(s)),[s]),v=(0,u.useCallback)((()=>{f||g(!0)}),[f]),j=(0,u.useCallback)((()=>{g(!1)}),[]);return(0,m.jsxs)("div",{onMouseLeave:a?j:null,onMouseEnter:a?v:null,onClick:a?v:null,onFocus:a?v:null,onBlur:a?j:null,role:"presentation",children:[f?(0,m.jsx)(n.Spinner,{}):l,d&&(0,m.jsx)(n.Popover,{noArrow:!1,inline:!0,children:(0,m.jsx)(r.Ay,{variant:"body-small",className:p.A["popover-text"],children:a})})]})};g.propTypes={count:d().number,checked:d().bool};const f=g},5498:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(1112),n=s(7723),a=s(1609),i=s(1549),o=s(8065),c=s(43),l=s(790);const __=n.__,d=({icon:e,label:t,children:s})=>{const[d,u]=(0,a.useState)(!0),{mode:h}=(0,a.useContext)(c._),p=Array.isArray(s)&&s?.length>=8&&"list"===h,m=p&&d?s.slice(0,8):s,g=p?s?.length-8:0,f=(0,a.useCallback)((()=>{u((e=>!e))}),[]);return(0,l.jsxs)("li",{tabIndex:-1,role:"menuitem",className:o.A["navigation-group"],children:[(0,l.jsx)(i.A,{icon:e,className:o.A["navigation-group-label"],children:t}),(0,l.jsxs)("div",{className:o.A["navigation-group-list"],children:[(0,l.jsx)("ul",{className:o.A["navigation-group-content"],children:m}),p&&(0,l.jsx)("div",{className:o.A["navigation-group-truncate"],children:(0,l.jsx)(r.A,{variant:"link",onClick:f,children:d?(0,n.sprintf)(/* translators: %s: Number of hide items  */
__("Show %s more","jetpack-protect"),g):(0,n.sprintf)(/* translators: %s: Number of hide items  */
__("Hide %s items","jetpack-protect"),g)})})]})]})}},59:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>v,Me:()=>d.A,s$:()=>u.A});var r=s(7425),n=s(6427),a=s(7723),i=s(1113),o=s(8248),c=s(4969),l=s(1609),d=s(5498),u=s(7366),h=s(8065),p=s(43),m=s(790);const __=a.__,g=({children:e})=>(0,m.jsx)("ul",{className:h.A.navigation,role:"menu",children:e}),f=({children:e,data:t})=>{const s=(0,l.useRef)(void 0),[a,d]=(0,l.useState)(!1),u=t?.items?.find((e=>e?.id===t?.selectedItem))??{label:__("See all results","jetpack-protect")},{label:p,icon:g}=u,f=(0,l.useCallback)((()=>{d((e=>!e))}),[]);return(0,m.jsxs)("button",{className:h.A["navigation-dropdown-button"],onClick:f,ref:s,children:[(0,m.jsxs)("div",{className:h.A["navigation-dropdown-label"],children:[g&&(0,m.jsx)(i.A,{icon:g,className:h.A["navigation-dropdown-icon"]}),(0,m.jsx)(r.Ay,{children:p})]}),(0,m.jsx)(i.A,{icon:a?o.A:c.A,size:32}),(0,m.jsx)(n.Popover,{position:"bottom center",anchorRef:s?.current,inline:!0,children:(0,m.jsx)("div",{style:{display:a?"block":"none",width:s?.current?.getBoundingClientRect?.()?.width},children:e})})]})},v=({children:e,selected:t,onSelect:s,mode:r="list"})=>{const n=(0,p.A)({selected:t,onSelect:s}),a=(e=>{switch(e){case"list":default:return g;case"dropdown":return f}})(r);return(0,m.jsx)(p._.Provider,{value:{...n,mode:r},children:(0,m.jsx)(a,{data:n,children:e})})}},7366:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(3022),n=s(1609),a=s(9278),i=s(1549),o=s(8065),c=s(43),l=s(790);const d=({id:e,label:t,icon:s,badge:d,disabled:u,onClick:h,onKeyDown:p,onFocus:m,checked:g})=>{const f=(0,n.useContext)(c._),v=f?.selectedItem===e,j=f?.registerItem,x=f?.registerRef,y=f?.handleClickItem,b=f?.handleKeyDownItem,w=f?.handleFocusItem,A=(0,r.A)(o.A["navigation-item"],{[o.A.clickable]:!u,[o.A.selected]:v}),k=(0,n.useCallback)((t=>{h?.(t),y?.(e)}),[y,e,h]),C=(0,n.useCallback)((e=>{p?.(e),b?.(e)}),[b,p]),_=(0,n.useCallback)((t=>{x(t,e)}),[x,e]),S=(0,n.useCallback)((t=>{m?.(t),w?.(e)}),[w,e,m]);return(0,n.useEffect)((()=>{j({id:e,disabled:u,label:t,icon:s})}),[]),(0,l.jsxs)("li",{className:A,onClick:u?null:k,onKeyDown:C,onFocus:u?null:S,role:"menuitem",tabIndex:u?-1:0,ref:_,children:[(0,l.jsx)(i.A,{icon:s,children:t}),(0,l.jsx)(a.A,{count:d,checked:g})]})}},1549:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(7425),n=s(1113),a=s(3022),i=s(8120),o=s.n(i),c=s(8065),l=s(790);const d=({icon:e,children:t,className:s})=>(0,l.jsxs)(r.Ay,{className:(0,a.A)(c.A["navigation-item-label"],s),children:[e&&(0,l.jsx)(n.A,{icon:e,className:c.A["navigation-item-icon"],size:28}),(0,l.jsx)("span",{className:c.A["navigation-item-label-content"],children:t})]});d.propTypes={icon:o().node,children:o().node.isRequired};const u=d},43:(e,t,s)=>{"use strict";s.d(t,{A:()=>a,_:()=>n});var r=s(1609);const n=(0,r.createContext)(),a=({selected:e,onSelect:t})=>{const[s,n]=(0,r.useState)([]),[a,i]=(0,r.useState)([]),[o,c]=(0,r.useState)(),l=(e,t)=>{const r=e-1,n=r<0?t:r,a=s[n];return a?.disabled?l(n,t):a},d=(e,t)=>{const r=e+1,n=r>t?0:r,a=s[n];return a?.disabled?d(n,t):a};return{selectedItem:e,handleClickItem:e=>{t(e)},handleKeyDownItem:r=>{const n=r?.code,i=s.findIndex((t=>t?.id===e)),c=s.length-1;let u;if("ArrowUp"===n){const e=l(i,c);u=e?.id}else if("ArrowDown"===n){const e=d(i,c);u=e?.id}else"Enter"!==n&&"Space"!==n||!o||(u=o);if(u){const e=a[u];e?.focus(),t(u)}},handleFocusItem:e=>{c(e)},registerRef:(e,t)=>{i((s=>!s[t]&&e?{...s,[t]:e}:s))},registerItem:e=>{n((t=>{const s=[...t],r=e?.id,n=s.findIndex((e=>e?.id===r));return n>=0?s[n]=e:s.push(e),s}))},items:s}}},3041:(e,t,s)=>{"use strict";s.d(t,{A:()=>p});var r=s(7723),n=s(3883),a=s(3751),i=s(9783),o=s(1113),c=s(991),l=s(1609),d=s(4537),u=s(5995),h=s(790);const __=r.__,p=({dismissable:e=!1,duration:t=null,floating:s=!1,message:r,type:p="success"})=>{const{clearNotice:m}=(0,d.A)();let g;switch(p){case"success":g=n.A;break;case"error":g=a.A;break;default:g=i.A}const f=(0,l.useCallback)((()=>{m()}),[m]);return(0,l.useEffect)((()=>{let e;return t&&(e=setTimeout(m,t)),()=>clearTimeout(e)}),[m,t,r]),(0,h.jsxs)("div",{className:`${u.A.notice} ${u.A[`notice--${p}`]} ${s?u.A["notice--floating"]:""}`,children:[(0,h.jsx)("div",{className:u.A.notice__icon,children:(0,h.jsx)(o.A,{icon:g})}),(0,h.jsx)("div",{className:u.A.notice__message,children:r}),e&&(0,h.jsx)("button",{className:u.A.notice__close,"aria-label":__("Dismiss notice.","jetpack-protect"),onClick:f,children:(0,h.jsx)(o.A,{icon:c.A})})]})}},4254:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(1330),n=s(7723),a=s(1609),i=s(1936),o=s(790);const __=n.__,c=({id:e,anchor:t,position:s})=>{const{stepsCount:n,currentStep:c,currentStepCount:l,completeCurrentStep:d,completeAllCurrentSteps:u}=(0,i.Ay)(),{setRenderedSteps:h}=(0,a.useContext)(i.OC);return(0,a.useEffect)((()=>(h((t=>[...t,e])),()=>{h((t=>t.filter((t=>t!==e))))})),[e,h]),c?.id!==e?null:(0,o.jsx)(r.A,{anchor:t,title:c.title,noArrow:!1,children:c.description,buttonContent:l<n?__("Next","jetpack-protect"):__("Finish","jetpack-protect",0),onClick:l<n?d:u,onClose:u,position:s,step:l,totalSteps:n,offset:15,flip:!1})}},2006:(e,t,s)=>{"use strict";s.d(t,{A:()=>_,S:()=>C});var r=s(7425),n=s(597),a=s(6461),i=s(442),o=s(8418),c=s(6427),l=s(8443),d=s(6087),u=s(7723),h=s(1113),p=s(3883),m=s(8248),g=s(4969),f=s(3022),v=s(1609),j=s(8140),x=s(3657),y=s(9810),b=s(790);const __=u.__,w=(0,v.createContext)(),A=({firstDetected:e,fixedOn:t,status:s})=>{const n=(0,v.useMemo)((()=>"fixed"===s?(0,u.sprintf)(/* translators: %s: Fixed on date */
__("Threat fixed %s","jetpack-protect"),(0,l.dateI18n)("M j, Y",t)):"ignored"===s?__("Threat ignored","jetpack-protect"):null),[s,t]);return e&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)(r.Ay,{className:y.A["accordion-header-status"],children:[(0,u.sprintf)(/* translators: %s: First detected date */
__("Threat found %s","jetpack-protect"),(0,l.dateI18n)("M j, Y",e)),n&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("span",{className:y.A["accordion-header-status-separator"]}),(0,b.jsx)("span",{className:y.A[`is-${s}`],children:n})]})]}),["fixed","ignored"].includes(s)&&(0,b.jsx)(k,{status:s})]})},k=({status:e})=>(0,b.jsx)("div",{className:`${y.A["status-badge"]} ${y.A[e]}`,children:"fixed"===e?__("Fixed","jetpack-protect"):__("Ignored","jetpack-protect",0)}),C=({id:e,title:t,label:s,icon:l,fixable:u,severity:k,children:C,firstDetected:_,fixedOn:S,onOpen:N,status:M,hideAutoFixColumn:L=!1})=>{const{open:E,setOpen:P}=(0,v.useContext)(w),R=E===e,{isThreatFixInProgress:z,isThreatFixStale:F}=(0,x.Ay)(),O=(0,v.useCallback)((()=>{R||N?.(),P((t=>t===e?null:e))}),[R,N,P,e]),[I]=(0,i.A)(["sm","lg"],[null,"<"]);return(0,b.jsxs)("div",{className:y.A["accordion-item"],children:[(0,b.jsxs)("button",{className:y.A["accordion-header"],onClick:O,children:[(0,b.jsxs)("div",{children:[(0,b.jsxs)(r.Ay,{className:y.A["accordion-header-label"],mb:1,children:[(0,b.jsx)(h.A,{icon:l,className:y.A["accordion-header-label-icon"]}),s]}),(0,b.jsx)(r.Ay,{className:y.A["accordion-header-description"],variant:R?"title-small":"body",children:t}),["fixed","ignored"].includes(M)&&(0,b.jsx)(A,{firstDetected:_,fixedOn:S,status:M})]}),(0,b.jsx)("div",{children:(0,b.jsx)(o.Z6,{severity:k})}),!L&&u&&(0,b.jsxs)("div",{children:[(D=z(e),V=F(e),V?(0,b.jsx)(n.A,{className:y.A["icon-tooltip"],iconClassName:y.A["icon-tooltip__icon"],iconSize:20,placement:"top",hoverShow:!0,children:(0,b.jsx)(r.Ay,{className:y.A["icon-tooltip__content"],children:(0,d.createInterpolateElement)(__("The fixer is taking longer than expected. Please try again or <supportLink>contact support</supportLink>.","jetpack-protect"),{supportLink:(0,b.jsx)(c.ExternalLink,{className:y.A["support-link"],href:j.NW})})})}):D?(0,b.jsx)(a.A,{color:"black"}):(0,b.jsx)(h.A,{icon:p.A,className:y.A["icon-check"],size:28})),I&&(0,b.jsx)("span",{children:__("Auto-fix","jetpack-protect")})]}),(0,b.jsx)("div",{className:y.A["accordion-header-button"],children:(0,b.jsx)(h.A,{icon:R?m.A:g.A,size:38})})]}),(0,b.jsx)("div",{className:(0,f.A)(y.A["accordion-body"],{[y.A["accordion-body-open"]]:R,[y.A["accordion-body-close"]]:!R}),"aria-hidden":!R,children:C})]});var D,V},_=({children:e})=>{const[t,s]=(0,v.useState)();return(0,b.jsx)(w.Provider,{value:{open:t,setOpen:s},children:(0,b.jsx)("div",{className:y.A.accordion,children:e})})}},5409:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(880),n=s(5925),a=s(790);function i({children:e,redirect:t="/"}){const{hasPlan:s}=(0,n.Ay)();return s?e:(0,a.jsx)(r.C5,{to:t,replace:!0})}},9374:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(9245),n=s(489),a=s(1112),i=s(9384),o=s(7723),c=s(1609),l=s(1226),d=s(4537),u=s(5925),h=s(9701),p=s(790);const __=o.__,m=()=>{const{showErrorNotice:e}=(0,d.A)(),{recordEvent:t}=(0,l.A)(),{upgradePlan:s,isLoading:o}=(0,u.Ay)(),{handleRegisterSite:m,registrationError:g}=(0,i.useConnection)({from:"protect",skipUserConnection:!0,redirectUri:"admin.php?page=jetpack-protect"}),[f,v]=(0,c.useState)(!1),[j,x]=(0,c.useState)(!1),{jetpackScan:y}=(0,h.A)(),{pricingForUi:b}=y,{introductoryOffer:w,currencyCode:A="USD"}=b,k=b.cost?Math.ceil(b.cost/12*100)/100:null,C=w?.costPerInterval?Math.ceil(w.costPerInterval/12*100)/100:null,_=(0,c.useCallback)((()=>{x(!0),t("jetpack_protect_pricing_table_get_scan_link_click"),s()}),[t,s]),S=(0,c.useCallback)((async()=>{v(!0),t("jetpack_protect_connected_product_activated");try{await m()}catch{e(__("Could not connect site.","jetpack-protect")),v(!1)}}),[m,t,e]),N={title:__("Stay one step ahead of threats","jetpack-protect"),items:[{name:__("Scan for threats and vulnerabilities","jetpack-protect")},{name:__("Daily automated scans","jetpack-protect")},{name:__("Web Application Firewall","jetpack-protect")},{name:__("Brute force protection","jetpack-protect")},{name:__("Account protection","jetpack-protect")},{name:__("Access to scan on Cloud","jetpack-protect")},{name:__("One-click auto fixes","jetpack-protect")},{name:__("Notifications","jetpack-protect")},{name:__("Severity labels","jetpack-protect")}]};return(0,p.jsx)(p.Fragment,{children:(0,p.jsxs)(r.Ay,{...N,children:[(0,p.jsxs)(r.N0,{primary:!0,children:[(0,p.jsxs)(r.i7,{children:[(0,p.jsx)(n.A,{price:k,offPrice:C,leyend:__("/month, billed yearly","jetpack-protect"),currency:A,hideDiscountLabel:!1}),(0,p.jsx)(a.A,{fullWidth:!0,onClick:_,isLoading:j,disabled:o||j,children:__("Get Jetpack Protect","jetpack-protect")})]}),(0,p.jsx)(r.eY,{isIncluded:!0,label:(0,p.jsx)("strong",{children:__("Line by line malware scanning","jetpack-protect")})}),(0,p.jsx)(r.eY,{isIncluded:!0,label:(0,p.jsx)("strong",{children:__("Plus on-demand manual scans","jetpack-protect")})}),(0,p.jsx)(r.eY,{isIncluded:!0,label:(0,p.jsx)("strong",{children:__("Automatic protection and rule updates","jetpack-protect")})}),(0,p.jsx)(r.eY,{isIncluded:!0}),(0,p.jsx)(r.eY,{isIncluded:!0}),(0,p.jsx)(r.eY,{isIncluded:!0}),(0,p.jsx)(r.eY,{isIncluded:!0}),(0,p.jsx)(r.eY,{isIncluded:!0}),(0,p.jsx)(r.eY,{isIncluded:!0})]}),(0,p.jsxs)(r.N0,{children:[(0,p.jsxs)(r.i7,{children:[(0,p.jsx)(n.A,{price:0,leyend:__("Free forever","jetpack-protect"),currency:A,hidePriceFraction:!0}),(0,p.jsx)(a.A,{fullWidth:!0,variant:"secondary",onClick:S,isLoading:f,disabled:o||f,error:g?__("An error occurred. Please try again.","jetpack-protect"):null,children:__("Start for free","jetpack-protect")})]}),(0,p.jsx)(r.eY,{isIncluded:!0,label:__("Check items against database","jetpack-protect")}),(0,p.jsx)(r.eY,{isIncluded:!0}),(0,p.jsx)(r.eY,{isIncluded:!0,label:__("Manual rules only","jetpack-protect")}),(0,p.jsx)(r.eY,{isIncluded:!0}),(0,p.jsx)(r.eY,{isIncluded:!0}),(0,p.jsx)(r.eY,{isIncluded:!1}),(0,p.jsx)(r.eY,{isIncluded:!1}),(0,p.jsx)(r.eY,{isIncluded:!1}),(0,p.jsx)(r.eY,{isIncluded:!1})]})]})})}},4256:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(3022),n=s(9141),a=s(790);const i=({className:e,total:t=100,value:s=0})=>{const i=Math.min(Math.round(s/t*100),100),o={width:`${i}%`};return(0,a.jsxs)("div",{className:(0,r.A)(e,n.A["progress-bar"]),children:[(0,a.jsx)("div",{className:n.A["progress-bar__wrapper"],children:(0,a.jsx)("div",{"aria-valuemax":t,"aria-valuemin":0,"aria-valuenow":Math.min(s,t),className:n.A["progress-bar__bar"],role:"progressbar",style:o})}),(0,a.jsx)("p",{className:n.A["progress-bar__percent"],children:`${i}%`})]})}},7827:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(790);function n(){return(0,r.jsxs)("svg",{width:"80",height:"96",viewBox:"0 0 80 96",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M40 0.00634766L80 17.7891V44.2985C80 66.8965 65.1605 88.2927 44.2352 95.0425C41.4856 95.9295 38.5144 95.9295 35.7648 95.0425C14.8395 88.2927 0 66.8965 0 44.2985V17.7891L40 0.00634766Z",fill:"#069E08"}),(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M60.9 33.6909L35.375 67.9124L19.2047 55.9263L22.7848 51.1264L34.1403 59.5436L56.0851 30.122L60.9 33.6909Z",fill:"white"})]})}},3535:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(1112),n=s(7723),a=s(1609),i=s(2425),o=s(240),c=s(790);const __=n.__,l=(0,a.forwardRef)((({variant:e="secondary",children:t,...s},n)=>{const l=(0,o.A)(),{data:d}=(0,i.Ay)(),u=(0,a.useMemo)((()=>l.isPending||(0,i.EV)(d)),[l.isPending,d]);return(0,c.jsx)(r.A,{ref:n,variant:e,onClick:e=>{e.preventDefault(),l.mutate()},disabled:u,...s,children:t??__("Scan now","jetpack-protect")})}))},9889:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(7723),n=s(1609),a=s(880),i=s(5925),o=s(4907),c=s(790);const __=r.__;function l(){const e=(0,a.Zp)(),t=(0,a.zy)(),{hasPlan:s}=(0,i.Ay)(),r="/scan"===t.pathname,l=t.pathname.includes("/scan/history"),d=(0,n.useCallback)((()=>e("/scan")),[e]),u=(0,n.useCallback)((()=>e("/scan/history")),[e]);return s&&(r||l)?(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)(o.A,{children:[(0,c.jsx)(o.A.Button,{variant:r?"primary":"secondary",onClick:d,children:__("Scanner","jetpack-protect")}),(0,c.jsx)(o.A.Button,{variant:l?"primary":"secondary",onClick:u,children:__("History","jetpack-protect")})]})}):null}},3127:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(442),n=s(5918),a=s(8509),i=s(790);const o=({spacing:e=0,gap:t=0,main:s,mainClassName:o,secondary:c,secondaryClassName:l,preserveSecondaryOnMobile:d=!1,fluid:u})=>{const[h,p]=(0,r.A)(["sm","lg"]),m=!d&&h;return(0,i.jsxs)(n.A,{horizontalSpacing:e,horizontalGap:t,fluid:u,children:[!m&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.A,{className:o,sm:12,md:4,lg:6,children:s}),p&&(0,i.jsx)(a.A,{lg:1}),(0,i.jsx)(a.A,{className:l,sm:12,md:4,lg:5,children:c})]}),m&&(0,i.jsx)(a.A,{children:s})]})}},3144:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(7425),n=s(6087),a=s(7723),i=s(9386),o=s(790);const __=a.__,c=()=>(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(r.Ay,{variant:"title-medium-semi-bold",mb:2,children:__("Enhanced protection","jetpack-protect")}),(0,o.jsx)(r.Ay,{mb:2,children:__("Learn how you can execute Jetpack Firewall before WordPress initializes. This mode offers the most protection.","jetpack-protect")}),(0,o.jsxs)("ul",{className:i.A.list,children:[(0,o.jsx)("li",{className:i.A["list-item"],children:(0,o.jsx)(r.Ay,{variant:"body-small",children:(0,n.createInterpolateElement)(__("To ensure the firewall can best protect your site, please update: <mark>auto_prepend_file</mark> PHP directive to point to <mark>wp-content/jetpack-waf/bootstrap.php</mark> Typically this is set either in an .htaccess file or in the global PHP configuration; contact your host for further assistance.","jetpack-protect"),{mark:(0,o.jsx)("mark",{className:i.A.mark})})})}),(0,o.jsx)("li",{className:i.A["list-item"],children:(0,o.jsx)(r.Ay,{variant:"body-small",children:__("Don't forget to undo this action when Firewall is turned off, or when you uninstall Jetpack.","jetpack-protect")})})]})]})},241:(e,t,s)=>{"use strict";s.d(t,{A:()=>o,o:()=>i});var r=s(880),n=s(9719),a=s(790);const i=({label:e,link:t})=>(0,a.jsx)(r.k2,{to:t,className:({isActive:e})=>e?`${n.A.tab} ${n.A["tab--active"]}`:n.A.tab,children:e}),o=({children:e,className:t=""})=>(0,a.jsx)("nav",{className:`${n.A.tabs} ${t}`,children:e})},4491:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5065),n=s(790);const a=({disabled:e=!1,id:t,label:s="",description:a="",placeholder:i="",rows:o=3,value:c="",onChange:l=()=>{}})=>(0,n.jsxs)("div",{children:[Boolean(s)&&(0,n.jsx)("label",{className:r.A.label,htmlFor:t,children:s}),Boolean(a)&&a,(0,n.jsx)("textarea",{className:r.A.textarea,disabled:e,placeholder:i,rows:o,id:t,name:t,onChange:l,value:c||""})]})},1571:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(7425),n=s(8418),a=s(7723),i=s(1113),o=s(1609),c=s(7267),l=s(790);const __=a.__,d=e=>{switch(e.fixer){case"replace":return __("Jetpack Scan will replace the affected file or directory.","jetpack-protect");case"delete":return __("Jetpack Scan will delete the affected file or directory.","jetpack-protect");case"update":return e.target?(0,a.sprintf)(/* translators: %s: Version that the plugin will be upgraded to  */
__("Jetpack Scan will update the vulnerable extension to a newer version %s.","jetpack-protect"),e.target):__("Jetpack Scan will update the vulnerable extension to a newer version.","jetpack-protect");case"edit":return __("Jetpack Scan will edit the affected file or directory.","jetpack-protect");case"rollback":return e.target?(0,a.sprintf)(/* translators: %s: Version that the plugin will be upgraded to  */
__("Jetpack Scan will rollback the affected file to the version from %s.","jetpack-protect"),e.target):__("Jetpack Scan will rollback the affected file to an older (clean) version.","jetpack-protect");default:return __("Jetpack Scan will resolve the threat.","jetpack-protect")}};function u({threat:e,fixAllDialog:t,onCheckFix:s}){const[a,u]=(0,o.useState)(!0),h=(0,o.useCallback)((t=>{u(t.target.checked),s(t.target.checked,e)}),[s,e]);return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:c.A.threat,children:[(0,l.jsx)(i.A,{icon:e.icon,className:c.A.threat__icon}),(0,l.jsxs)("div",{className:c.A.threat__summary,children:[(0,l.jsx)(r.Ay,{className:c.A.threat__summary__label,mb:1,children:e.label}),(0,l.jsx)(r.Ay,{className:c.A.threat__summary__title,children:d(e.fixable)})]}),(0,l.jsx)("div",{className:c.A.threat__severity,children:(0,l.jsx)(n.Z6,{severity:e.severity})}),t&&(0,l.jsx)("div",{className:c.A.threat__checkbox,children:(0,l.jsx)("input",{type:"checkbox",checked:a,onChange:h,value:e.id})})]})})}},9314:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var r=s(7425),n=s(6087),a=s(7723),i=s(1609),o=s(2425),c=s(5925),l=s(9701),d=s(4254),u=s(3535),h=s(203),p=s(790);const __=a.__,_n=a._n,m=()=>(0,p.jsxs)("svg",{width:"80",height:"96",viewBox:"0 0 80 96",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,p.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M40 0.00634766L80 17.7891V44.2985C80 66.8965 65.1605 88.2927 44.2352 95.0425C41.4856 95.9295 38.5144 95.9295 35.7648 95.0425C14.8395 88.2927 0 66.8965 0 44.2985V17.7891L40 0.00634766Z",fill:"#069E08"}),(0,p.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M60.9 33.6909L35.375 67.9124L19.2047 55.9263L22.7848 51.1264L34.1403 59.5436L56.0851 30.122L60.9 33.6909Z",fill:"white"})]}),g=()=>{const{lastChecked:e}=(0,l.A)(),{hasPlan:t}=(0,c.Ay)(),{data:s}=(0,o.Ay)(),[g,f]=(0,i.useState)(null),v=(0,i.useMemo)((()=>e?(e=>{const t=new Date,s=6e4*t.getTimezoneOffset(),r=Math.floor((new Date(t.getTime()+s).getTime()-e)/1e3);let n=r/31536e3;return n>1?(0,a.sprintf)(
// translators: placeholder is a number amount of years i.e. "5 years ago".
_n("%s year ago","%s years ago",Math.floor(n),"jetpack-protect"),Math.floor(n)):(n=r/2592e3,n>1?(0,a.sprintf)(
// translators: placeholder is a number amount of months i.e. "5 months ago".
_n("%s month ago","%s months ago",Math.floor(n),"jetpack-protect"),Math.floor(n)):(n=r/86400,n>1?(0,a.sprintf)(
// translators: placeholder is a number amount of days i.e. "5 days ago".
_n("%s day ago","%s days ago",Math.floor(n),"jetpack-protect"),Math.floor(n)):(n=r/3600,n>1?(0,a.sprintf)(
// translators: placeholder is a number amount of hours i.e. "5 hours ago".
_n("%s hour ago","%s hours ago",Math.floor(n),"jetpack-protect"),Math.floor(n)):(n=r/60,n>1?(0,a.sprintf)(
// translators: placeholder is a number amount of minutes i.e. "5 minutes ago".
_n("%s minute ago","%s minutes ago",Math.floor(n),"jetpack-protect"),Math.floor(n)):__("a few seconds ago","jetpack-protect")))))})(Date.parse(e)):null),[e]);return(0,p.jsxs)("div",{className:h.A.empty,children:[(0,p.jsx)(m,{}),(0,p.jsx)(r.H3,{weight:"bold",mt:8,children:__("Don't worry about a thing","jetpack-protect")}),(0,p.jsx)(r.Ay,{mb:4,children:v?(0,n.createInterpolateElement)((0,a.sprintf)(
// translators: placeholder is the amount of time since the last scan, i.e. "5 minutes ago".
__("The last Protect scan ran <strong>%s</strong> and everything looked great.","jetpack-protect"),v),{strong:(0,p.jsx)("strong",{})}):__("No threats have been detected by the current scan.","jetpack-protect")}),t&&(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(u.A,{ref:f}),!(0,o.EV)(s)&&(0,p.jsx)(d.A,{id:"paid-daily-and-manual-scans",position:"bottom middle",anchor:g})]})]})}},5190:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var r=s(1112),n=s(7425),a=s(4437),i=s(7723),o=s(1609),c=s(1226),l=s(5925),d=s(4114),u=s(4569),h=s(203),p=s(790);const __=i.__,m=({description:e,fixedIn:t,icon:s,id:u,label:m,name:g,source:f,title:v,type:j})=>{const{recordEvent:x}=(0,c.A)(),{upgradePlan:y}=(0,l.Ay)(),b=(0,o.useCallback)((()=>{x("jetpack_protect_threat_list_get_scan_link_click"),y()}),[x,y]),w=f?(0,p.jsx)(r.A,{variant:"link",isExternalLink:!0,weight:"regular",href:f,children:__("See more technical details of this threat","jetpack-protect")}):null;return(0,p.jsxs)(d.E,{id:u,label:m,title:v,icon:s,onOpen:(0,o.useCallback)((()=>{["core","plugin","theme"].includes(j)&&x(`jetpack_protect_${j}_threat_open`)}),[x,j]),children:[e&&(0,p.jsxs)("div",{className:h.A["threat-section"],children:[(0,p.jsx)(n.Ay,{variant:"title-small",mb:2,children:__("What is the problem?","jetpack-protect")}),(0,p.jsx)(n.Ay,{mb:2,children:e}),w]}),t&&(0,p.jsxs)("div",{className:h.A["threat-section"],children:[(0,p.jsx)(n.Ay,{variant:"title-small",mb:2,children:__("How to fix it?","jetpack-protect")}),(0,p.jsx)(n.Ay,{mb:2,children:/* translators: Translates to Update to. %1$s: Name. %2$s: Fixed version */ /* translators: Translates to Update to. %1$s: Name. %2$s: Fixed version */
(0,i.sprintf)(__("Update to %1$s %2$s","jetpack-protect"),g,t)}),(0,p.jsx)(a.A,{description:__("Looking for advanced scan results and one-click fixes?","jetpack-protect"),cta:__("Upgrade Jetpack Protect now","jetpack-protect"),onClick:b,className:h.A["threat-item-cta"]})]}),!e&&(0,p.jsx)("div",{className:h.A["threat-section"],children:w})]})},g=({list:e})=>(0,p.jsx)(u.A,{list:e,children:({currentItems:e})=>(0,p.jsx)(d.A,{children:e.map((({description:e,fixedIn:t,icon:s,id:r,label:n,name:a,source:i,table:o,title:c,type:l,version:d})=>(0,p.jsx)(m,{description:e,fixedIn:t,icon:s,id:r,label:n,name:a,source:i,table:o,title:c,type:l,version:d},r)))})})},5661:(e,t,s)=>{"use strict";s.d(t,{A:()=>A});var r=s(442),n=s(5918),a=s(8509),i=s(7425),o=s(1112),c=s(7723),l=s(1609),d=s(2425),u=s(3657),h=s(1009),p=s(5925),m=s(4254),g=s(3535),f=s(9314),v=s(5190),j=s(9395),x=s(6742),y=s(203),b=s(993),w=s(790);const __=c.__,A=()=>{const{hasPlan:e}=(0,p.Ay)(),{item:t,list:s,selected:A,setSelected:k}=(0,b.A)(),[C]=(0,r.A)("sm"),{isThreatFixInProgress:_,isThreatFixStale:S}=(0,u.Ay)(),{data:N}=(0,d.Ay)(),M=(0,d.EV)(N),L=(0,l.useMemo)((()=>s.filter((e=>{const t=parseInt(e.id);return e.fixable&&!_(t)&&!S(t)}))),[s,_,S]),[E,P]=(0,l.useState)(null),[R,z]=(0,l.useState)(null),[F,O]=(0,l.useState)(null),[I,D]=(0,l.useState)(null),{setModal:V}=(0,h.A)(),H=(0,l.useCallback)((()=>{switch(A){case"all":return 1===s.length?__("All threats","jetpack-protect"):(0,c.sprintf)(/* translators: placeholder is the amount of threats found on the site. */
__("All %s threats","jetpack-protect"),s.length);case"core":return(0,c.sprintf)(/* translators: placeholder is the amount of WordPress threats found on the site. */
__("%1$s WordPress %2$s","jetpack-protect"),s.length,1===s.length?"threat":"threats");case"files":return(0,c.sprintf)(/* translators: placeholder is the amount of file threats found on the site. */
__("%1$s file %2$s","jetpack-protect"),s.length,1===s.length?"threat":"threats");case"database":return(0,c.sprintf)(/* translators: placeholder is the amount of database threats found on the site. */
__("%1$s database %2$s","jetpack-protect"),s.length,1===s.length?"threat":"threats");default:return(0,c.sprintf)(/* translators: Translates to Update to. %1$s: Name. %2$s: Fixed version */
__("%1$s %2$s in %3$s %4$s","jetpack-protect"),s.length,1===s.length?"threat":"threats",t?.name,t?.version)}}),[A,s,t]);return(0,w.jsxs)(n.A,{fluid:!0,horizontalSpacing:0,horizontalGap:3,children:[(0,w.jsxs)(a.A,{lg:4,children:[(0,w.jsx)("div",{ref:P,children:(0,w.jsx)(j.A,{selected:A,onSelect:k})}),!M&&(0,w.jsx)(m.A,{id:e?"paid-scan-results":"free-scan-results",position:"top",anchor:E})]}),(0,w.jsx)(a.A,{lg:8,children:s?.length>0?(0,w.jsxs)(w.Fragment,{children:[(0,w.jsxs)("div",{className:y.A["list-header"],children:[(0,w.jsx)(i.hE,{className:y.A["list-title"],children:H()}),e&&(0,w.jsx)("div",{className:y.A["list-header__controls"],children:L.length>0&&(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(o.A,{ref:O,variant:"primary",onClick:(T=L,e=>{e.preventDefault(),V({type:"FIX_ALL_THREATS",props:{threatList:T}})}),children:(0,c.sprintf)(/* translators: Translates to Show auto fixers $s: Number of fixable threats. */
__("Show auto fixers (%s)","jetpack-protect"),L.length)}),!M&&(0,w.jsx)(m.A,{id:"paid-fix-all-threats",position:C?"bottom right":"middle left",anchor:F}),(0,w.jsx)(g.A,{ref:D}),!M&&(0,w.jsx)(m.A,{id:"paid-daily-and-manual-scans",position:C?"bottom left":"middle left",anchor:I})]})})]}),e?(0,w.jsxs)(w.Fragment,{children:[(0,w.jsxs)("div",{ref:z,children:[(0,w.jsx)(x.A,{list:s}),(0,w.jsxs)("div",{className:y.A["manual-scan"],children:[(0,w.jsx)(i.Ay,{variant:"body-small",mb:4,children:__("If you have manually fixed any of the threats listed above, you can run a manual scan now or wait for Jetpack to scan your site later today.","jetpack-protect")}),(0,w.jsx)(g.A,{})]})]}),!M&&(0,w.jsx)(m.A,{id:"paid-understand-severity",position:"top",anchor:R})]}):(0,w.jsx)(v.A,{list:s})]}):(0,w.jsx)(f.A,{})})]});var T}},9395:(e,t,s)=>{"use strict";s.d(t,{A:()=>v});var r=s(442),n=s(7723),a=s(3751),i=s(4648),o=s(1651),c=s(435),l=s(2072),d=s(4314),u=s(1609),h=s(1226),p=s(5925),m=s(9701),g=s(59),f=s(790);const __=n.__,v=({selected:e,onSelect:t,sourceType:s="scan",statusFilter:n="all"})=>{const{hasPlan:v}=(0,p.Ay)(),{results:{plugins:j,themes:x},counts:{current:{threats:y,core:b,files:w,database:A}}}=(0,m.A)({sourceType:s,filter:{status:n}}),{recordEvent:k}=(0,h.A)(),[C]=(0,r.A)("lg","<"),_=(0,u.useCallback)((()=>{k("jetpack_protect_navigation_all_click")}),[k]),S=(0,u.useCallback)((()=>{k("jetpack_protect_navigation_core_click")}),[k]),N=(0,u.useCallback)((()=>{k("jetpack_protect_navigation_database_click")}),[k]),M=(0,u.useCallback)((()=>{k("jetpack_protect_navigation_plugin_click")}),[k]),L=(0,u.useCallback)((()=>{k("jetpack_protect_navigation_theme_click")}),[k]),E=(0,u.useCallback)((()=>{k("jetpack_protect_navigation_file_click")}),[k]),P=(0,u.useMemo)((()=>"fixed"===n?__("All fixed threats","jetpack-protect"):"ignored"===n?__("All ignored threats","jetpack-protect",0):__("All threats","jetpack-protect")),[n]);return(0,f.jsxs)(g.Ay,{selected:e,onSelect:t,mode:C?"dropdown":"list",children:[(0,f.jsx)(g.s$,{initial:!0,id:"all",label:P,icon:a.A,badge:y,disabled:y<=0,onClick:_,checked:!0}),(0,f.jsx)(g.s$,{id:"core",label:__("WordPress","jetpack-protect"),icon:i.A,badge:b,disabled:b<=0,onClick:S,checked:!0}),(0,f.jsx)(g.s$,{id:"database",label:__("Database","jetpack-protect"),icon:o.A,badge:A,disabled:A<=0,onClick:N,checked:!0}),(0,f.jsx)(g.Me,{label:__("Plugins","jetpack-protect"),icon:c.A,children:j.map((({name:e,threats:t,checked:s})=>(0,f.jsx)(g.s$,{id:e,label:e,checked:s,badge:t?.length,disabled:t?.length<=0,onClick:M},e)))}),(0,f.jsx)(g.Me,{label:__("Themes","jetpack-protect"),icon:l.A,children:x.map((({name:e,threats:t,checked:s})=>(0,f.jsx)(g.s$,{id:e,label:e,checked:s,badge:t?.length,disabled:t?.length<=0,onClick:L},e)))}),v&&(0,f.jsx)(f.Fragment,{children:(0,f.jsx)(g.s$,{id:"files",label:__("Files","jetpack-protect"),icon:d.A,badge:w,disabled:w<=0,onClick:E,checked:!0})})]})}},4569:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(1112),n=s(442),a=s(7723),i=s(8888),o=s(9115),c=s(1609),l=s(203),d=s(790);const __=a.__,u=({pageNumber:e,currentPage:t,onPageChange:s})=>{const n=(0,c.useMemo)((()=>t===e),[t,e]),i=(0,c.useCallback)((()=>{s(e)}),[s,e]);return(0,d.jsx)(r.A,{size:"medium",className:n?null:l.A.unfocused,onClick:i,"aria-current":n?"page":void 0,"aria-label":(0,a.sprintf)(/* translators: placeholder is a page number, i.e. "Page 123" */
__("Page %d","jetpack-protect"),e),children:e})},h=({list:e,itemPerPage:t=10,children:s})=>{const[a]=(0,n.A)("sm"),[h,p]=(0,c.useState)(1),m=(0,c.useCallback)((()=>p(h-1)),[h,p]),g=(0,c.useCallback)((()=>p(h+1)),[h,p]),f=(0,c.useMemo)((()=>Math.ceil(e.length/t)),[e,t]),v=(0,c.useMemo)((()=>{const s=h*t,r=s-t;return e.slice(r,s)}),[h,e,t]),j=(0,c.useMemo)((()=>{if(a)return[h];const e=[1];return h>3&&f>4&&e.push("…"),1===h?e.push(h+1,h+2,h+3):2===h?e.push(h,h+1,h+2):h<f-1?e.push(h-1,h,h+1):h===f-1?(h>3&&e.push(h-2),h>2&&e.push(h-1),e.push(h)):h===f&&(h>=5&&e.push(h-3),h>=4&&e.push(h-2),e.push(h-1)),e[e.length-1]<f-1?(e.push("…"),e.push(f)):e[e.length-1]<f&&e.push(f),e.filter((e=>e<=f||isNaN(e)))}),[h,a,f]);return(0,d.jsxs)(d.Fragment,{children:[s({currentItems:v}),f>1&&(0,d.jsxs)("nav",{role:"navigation","aria-label":__("Threat list pages","jetpack-protect"),className:l.A["pagination-container"],children:[(0,d.jsx)(r.A,{onClick:m,disabled:1===h,variant:"link",icon:i.A,iconSize:24,"aria-label":__("Previous page","jetpack-protect")}),j.map(((e,t)=>"number"==typeof e?(0,d.jsx)(u,{pageNumber:e,currentPage:h,onPageChange:p},e):(0,d.jsx)("span",{children:e},`ellipses_${t}`))),(0,d.jsx)(r.A,{onClick:g,disabled:h===f,variant:"link",icon:o.A,iconSize:24,"aria-label":__("Next page","jetpack-protect")})]})]})}},6742:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var r=s(1112),n=s(7425),a=s(5734),i=s(4741),o=s(4252),c=s(442),l=s(7723),d=s(1609),u=s(1226),h=s(3657),p=s(1009),m=s(2006),g=s(4569),f=s(203),v=s(790);const __=l.__,j=({context:e,description:t,diff:s,extension:c,signature:g,filename:j,firstDetected:x,fixedIn:y,fixedOn:b,icon:w,fixable:A,id:k,label:C,name:_,source:S,title:N,details:M,type:L,severity:E,status:P,hideAutoFixColumn:R=!1})=>{const{setModal:z}=(0,p.A)(),{recordEvent:F}=(0,u.A)(),{isThreatFixInProgress:O,isThreatFixStale:I}=(0,h.Ay)(),D=O(k),V=I(k),H=S?(0,v.jsx)(r.A,{variant:"link",isExternalLink:!0,weight:"regular",href:S,children:__("See more technical details of this threat","jetpack-protect")}):null;return(0,v.jsxs)(m.S,{id:k,label:C,title:N,icon:w,fixable:A,severity:E,firstDetected:x,fixedOn:b,status:P,onOpen:(0,d.useCallback)((()=>{["core","plugin","theme","file","database"].includes(L)&&F(`jetpack_protect_${L}_threat_open`)}),[F,L]),hideAutoFixColumn:R,children:[t&&(0,v.jsxs)("div",{className:f.A["threat-section"],children:[(0,v.jsx)(n.Ay,{variant:"title-small",mb:2,children:"fixed"!==P?__("What is the problem?","jetpack-protect"):__("What was the problem?","jetpack-protect",0)}),(0,v.jsx)(n.Ay,{mb:2,children:t}),H]}),(j||e||s||M)&&(0,v.jsx)(n.Ay,{variant:"title-small",mb:2,children:__("The technical details","jetpack-protect")}),j&&(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(n.Ay,{mb:2,children:/* translators: filename follows in separate line; e.g. "PHP.Injection.5 in: `post.php`" */
__("Threat found in file:","jetpack-protect")}),(0,v.jsx)("pre",{className:f.A["threat-filename"],children:j})]}),e&&(0,v.jsx)(a.A,{context:e}),M&&(0,v.jsx)(i.A,{details:M}),s&&(0,v.jsx)(o.A,{diff:s}),y&&"fixed"!==P&&(0,v.jsxs)("div",{className:f.A["threat-section"],children:[(0,v.jsx)(n.Ay,{variant:"title-small",mb:2,children:__("How to fix it?","jetpack-protect")}),(0,v.jsx)(n.Ay,{mb:2,children:/* translators: Translates to Update to. %1$s: Name. %2$s: Fixed version */ /* translators: Translates to Update to. %1$s: Name. %2$s: Fixed version */
(0,l.sprintf)(__("Update to %1$s %2$s","jetpack-protect"),_,y)})]}),!t&&(0,v.jsx)("div",{className:f.A["threat-section"],children:H}),["ignored","current"].includes(P)&&(0,v.jsxs)("div",{className:f.A["threat-footer"],children:["ignored"===P&&(0,v.jsx)(r.A,{isDestructive:!0,variant:"secondary",onClick:e=>{e.preventDefault(),z({type:"UNIGNORE_THREAT",props:{id:k,label:C,title:N,icon:w,severity:E}})},children:__("Unignore threat","jetpack-protect")}),"current"===P&&(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(r.A,{isDestructive:!0,variant:"secondary",onClick:e=>{e.preventDefault(),z({type:"IGNORE_THREAT",props:{id:k,label:C,title:N,icon:w,severity:E}})},disabled:D||V,children:__("Ignore threat","jetpack-protect")}),A&&(0,v.jsx)(r.A,{disabled:D||V,onClick:e=>{e.preventDefault(),z({type:"FIX_THREAT",props:{id:k,signature:g,extension:c,fixable:A,label:C,icon:w,severity:E}})},children:__("Fix threat","jetpack-protect")})]})]})]})},x=({list:e,hideAutoFixColumn:t=!1})=>{const[s]=(0,c.A)(["sm","lg"],[null,"<"]);return(0,v.jsxs)(v.Fragment,{children:[!s&&(0,v.jsxs)("div",{className:f.A["accordion-header"],children:[(0,v.jsx)("span",{children:__("Details","jetpack-protect")}),(0,v.jsx)("span",{children:__("Severity","jetpack-protect")}),!t&&(0,v.jsx)("span",{children:__("Auto-fix","jetpack-protect")}),(0,v.jsx)("span",{})]}),(0,v.jsx)(g.A,{list:e,children:({currentItems:e})=>(0,v.jsx)(m.A,{children:e.map((({context:e,description:s,diff:r,extension:n,signature:a,filename:i,firstDetected:o,fixedIn:c,fixedOn:l,icon:d,fixable:u,id:h,label:p,name:m,severity:g,source:f,details:x,title:y,type:b,version:w,status:A})=>(0,v.jsx)(j,{context:e,description:s,diff:r,extension:n,signature:a,filename:i,firstDetected:o,fixedIn:c,fixedOn:l,icon:d,fixable:u,id:h,label:p,name:m,severity:g,source:f,details:x,title:y,type:b,version:w,status:A,hideAutoFixColumn:t},h)))})})]})}},993:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(4648),n=s(4314),a=s(1651),i=s(435),o=s(2072),c=s(1609),l=s(9701);const d=(e,t)=>t.severity-e.severity,u=(e,t)=>"object"==typeof e&&0===Object.keys(e).length?[]:Array.isArray(e)?e.map((e=>u(e,t))).flat():e?.threats.map((s=>({...s,...e,...t}))),h=({source:e,status:t}={source:"scan",status:"all"})=>{const[s,h]=(0,c.useState)("all"),{results:{plugins:p,themes:m,core:g,files:f,database:v}}=(0,l.A)({sourceType:e,filter:{status:t,key:s}}),{unsortedList:j,item:x}=(0,c.useMemo)((()=>{if(s&&"all"!==s){switch(s){case"core":return{unsortedList:u(g,{icon:r.A}),item:g};case"files":return{unsortedList:u({threats:f},{icon:n.A}),item:f};case"database":return{unsortedList:u({threats:v},{icon:a.A}),item:v}}const e=p.find((e=>e?.name===s));if(e)return{unsortedList:u(e,{icon:i.A}),item:e};const t=m.find((e=>e?.name===s));if(t)return{unsortedList:u(t,{icon:o.A}),item:t}}return{unsortedList:[...u(g,{icon:r.A}),...u(p,{icon:i.A}),...u(m,{icon:o.A}),...u({threats:f},{icon:n.A}),...u({threats:v},{icon:a.A})],item:null}}),[g,v,f,p,s,m]),y=e=>e.name&&e.version?`${e.name} (${e.version})`:e.filename?e.filename.split("/").pop():e.table?e.table:void 0,b=(0,c.useMemo)((()=>j.sort(d).map((e=>({label:y(e),...e})))),[j]);return(0,c.useEffect)((()=>{"all"!==s&&"all"!==t&&0===b.length&&h("all")}),[s,t,x,b]),{item:x,list:b,selected:s,setSelected:h}}},9623:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(7425),n=s(1112),a=s(8418),i=s(7723),o=s(1113),c=s(1609),l=s(7208),d=s(1009),u=s(4031),h=s(3119),p=s(790);const __=i.__,m=({id:e,title:t,label:s,icon:i,severity:m})=>{const{setModal:g}=(0,d.A)(),f=(0,l.A)(),[v,j]=(0,c.useState)(!1);return(0,p.jsxs)(u.A,{children:[(0,p.jsx)(r.Ay,{variant:"title-medium",mb:2,children:__("Do you really want to unignore this threat?","jetpack-protect")}),(0,p.jsx)(r.Ay,{mb:3,children:__("Jetpack will unignore the threat:","jetpack-protect")}),(0,p.jsxs)("div",{className:h.A.threat,children:[(0,p.jsx)(o.A,{icon:i,className:h.A.threat__icon}),(0,p.jsxs)("div",{className:h.A.threat__summary,children:[(0,p.jsx)(r.Ay,{className:h.A.threat__summary__label,mb:1,children:s}),(0,p.jsx)(r.Ay,{className:h.A.threat__summary__title,children:t})]}),(0,p.jsx)("div",{className:h.A.threat__severity,children:(0,p.jsx)(a.Z6,{severity:m})})]}),(0,p.jsxs)("div",{className:h.A.footer,children:[(0,p.jsx)(n.A,{variant:"secondary",onClick:e=>{e.preventDefault(),g({type:null})},children:__("Cancel","jetpack-protect")}),(0,p.jsx)(n.A,{isDestructive:!0,isLoading:v,onClick:async t=>{t.preventDefault(),j(!0),await f.mutateAsync(e),g({type:null}),j(!1)},children:__("Unignore threat","jetpack-protect")})]})]})}},4031:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(9384),n=s(1221),a=s(790);const i=({children:e})=>{const{isUserConnected:t,hasConnectedOwner:s}=(0,r.useConnection)();return t&&s?e:(0,a.jsx)(n.A,{})}},1221:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(7425),n=s(1112),a=s(9384),i=s(7723),o=s(1009),c=s(3041),l=s(7751),d=s(790);const __=i.__,u=()=>{const{setModal:e}=(0,o.A)(),{userIsConnecting:t,handleConnectUser:s}=(0,a.useConnection)({redirectUri:"admin.php?page=jetpack-protect"});return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(r.Ay,{variant:"title-medium",mb:2,children:__("User connection needed","jetpack-protect")}),(0,d.jsx)(c.A,{type:"info",message:__("Before Jetpack Protect can ignore and auto-fix threats on your site, a user connection is needed.","jetpack-protect")}),(0,d.jsx)(r.Ay,{mb:3,children:__("A user connection provides Jetpack Protect the access necessary to perform these tasks.","jetpack-protect")}),(0,d.jsx)(r.Ay,{mb:3,children:__("Once you’ve secured a user connection, all Jetpack Protect features will be available for use.","jetpack-protect")}),(0,d.jsxs)("div",{className:l.A.footer,children:[(0,d.jsx)(n.A,{variant:"secondary",onClick:t=>{t.preventDefault(),e({type:null})},children:__("Not now","jetpack-protect")}),(0,d.jsx)(n.A,{isExternalLink:!0,weight:"regular",isLoading:t,onClick:s,children:__("Connect your user account","jetpack-protect")})]})]})}},8140:(e,t,s)=>{"use strict";s.d(t,{De:()=>v,Gs:()=>g,J3:()=>r,JD:()=>i,Mm:()=>c,NW:()=>a,Oz:()=>u,Ss:()=>h,VE:()=>p,X9:()=>d,_R:()=>o,kD:()=>m,pQ:()=>n,vI:()=>l,yN:()=>f});const r="jetpack_scan",n="https://wordpress.org/support/plugin/jetpack-protect/",a="https://jetpack.com/contact-support/?rel=support",i="optimistically_scanning",o="idle",c="unavailable",l=["provisioning","scheduled","scanning",i],d="fixers",u="has plan",h="history",p="onboarding progress",m="product data",g="scan status",f="waf",v="account protection"},9676:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(5104),n=s(6185),a=s.n(n),i=s(295),o=s(8140);function c(){return(0,r.I)({queryKey:[o.De],queryFn:i.A.getAccountProtection,initialData:a()(window?.jetpackProtectInitialState?.accountProtection)})}},1729:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(7115),n=s(4613),a=s(7723),i=s(295),o=s(8140),c=s(4537);const __=a.__;function l(){const e=(0,r.jE)(),{showSavingNotice:t,showSuccessNotice:s,showErrorNotice:a}=(0,c.A)();return(0,n.n)({mutationFn:i.A.toggleAccountProtection,onMutate:()=>{t();const s=e.getQueryData([o.De]);return s&&e.setQueryData([o.De],{...s,isEnabled:!s.isEnabled}),{previousData:s}},onSuccess:()=>{s(__("Changes saved.","jetpack-protect"))},onError:(t,s,r)=>{r?.previousData&&e.setQueryData([o.De],r.previousData),a(__("An error occurred.","jetpack-protect"))},onSettled:()=>{e.invalidateQueries({queryKey:[o.De]})}})}},7534:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(7115),n=s(4613),a=s(295),i=s(8140);function o(){const e=(0,r.jE)();return(0,n.n)({mutationFn:a.A.completeOnboardingSteps,onMutate:t=>{e.setQueryData([i.VE],(e=>[...e,...t]))}})}},5780:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(5104),n=s(295),a=s(8140);function i(){return(0,r.I)({queryKey:[a.VE],queryFn:n.A.getOnboardingProgress,initialData:window?.jetpackProtectInitialState?.onboardingProgress||[]})}},169:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(7115),n=s(4613),a=s(7723),i=s(295),o=s(8140),c=s(4537);const __=a.__;function l(){const e=(0,r.jE)(),{showSuccessNotice:t,showErrorNotice:s}=(0,c.A)();return(0,n.n)({mutationFn:i.A.fixThreats,onSuccess:s=>{if(!1===s.ok)throw new Error(s.error);if(Object.values(s.threats).every((e=>"error"in e)))throw new Error;e.setQueryData([o.X9],s),t(__("We're hard at work fixing this threat in the background. Please check back shortly.","jetpack-protect"))},onError:()=>{s(__("An error occurred fixing threats.","jetpack-protect"))}})}},8064:(e,t,s)=>{"use strict";s.d(t,{A:()=>p});var r=s(9384),n=s(7115),a=s(5104),i=s(7723),o=s(1609),c=s(295),l=s(8140),d=s(3657),u=s(4537);const __=i.__,_n=i._n,h=window.jetpackProtectInitialState?.fixerStatus||{ok:!0,threats:{}};function p({threatIds:e,usePolling:t}){const s=(0,n.jE)(),{showSuccessNotice:p,showErrorNotice:m}=(0,u.A)(),{isRegistered:g}=(0,r.useConnection)({autoTrigger:!1,from:"protect",redirectUri:null,skipUserConnection:!0}),f=(0,o.useCallback)(((e,t)=>{if(e.length>0){
// Translators: %d is the number of threats, and %s is a list of threat IDs.
const t=_n("A threat could not be fixed.","%d threats could not be fixed.",e.length,"jetpack-protect");m((0,i.sprintf)(t,e.length))}else if(t.length>0){
// Translators: %d is the number of threats, and %s is a list of threat IDs.
const e=_n("Threat fixed successfully.","%d threats fixed successfully.",t.length,"jetpack-protect");p((0,i.sprintf)(e,t.length))}}),[m,p]),v=(0,a.I)({queryKey:[l.X9,e],queryFn:async()=>{const t=await c.A.getFixersStatus(e),r=s.getQueryData([l.X9]);if(!1===t.ok)throw new Error(t.error);const n=[],a=[];return Object.keys(t.threats||{}).forEach((e=>{const i=t.threats[e];if(!0===r.ok){const t=r.threats?.[e];t&&"in_progress"===t.status&&("in_progress"===i.status?!(0,d.UM)(t.lastUpdated)&&(0,d.UM)(i.lastUpdated)&&a.push(e):(s.invalidateQueries({queryKey:[l.Gs]}),s.invalidateQueries({queryKey:[l.Ss]}),"fixed"===i.status?n.push(e):a.push(e)))}})),f(a,n),t},retry:!1,refetchInterval(e){if(!t||!e.state.data)return!1;const s=e.state.data;if(!0===s.ok){if(Object.values(s.threats).some((e=>"status"in e&&"in_progress"===e.status&&!(0,d.UM)(e.lastUpdated))))return e.state.dataUpdateCount<5?5e3:15e3}return!1},initialData:h,enabled:g});return(0,o.useEffect)((()=>{v.isError&&v.error&&(s.setQueryData([l.X9],h),m(__("An error occurred while fetching fixers status.","jetpack-protect")))}),[v.isError,v.error,s,m]),v}},1859:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(9384),n=s(5104),a=s(6185),i=s.n(a),o=s(295),c=s(8140);function l(){const{isRegistered:e}=(0,r.useConnection)({autoTrigger:!1,from:"protect",redirectUri:null,skipUserConnection:!0});return(0,n.I)({queryKey:[c.Ss],queryFn:o.A.getScanHistory,initialData:i()(window.jetpackProtectInitialState?.scanHistory),enabled:e})}},8847:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(7115),n=s(4613),a=s(7723),i=s(295),o=s(8140),c=s(4537);const __=a.__;function l(){const e=(0,r.jE)(),{showSuccessNotice:t,showErrorNotice:s}=(0,c.A)();return(0,n.n)({mutationFn:async t=>{const s=await i.A.ignoreThreat(t);return await Promise.all([e.refetchQueries({queryKey:[o.Gs]}),e.refetchQueries({queryKey:[o.Ss]})]),s},onSuccess:()=>{t(__("Threat ignored.","jetpack-protect"))},onError:()=>{s(__("An error occurred ignoring the threat.","jetpack-protect"))}})}},2425:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>h,EV:()=>u});var r=s(9384),n=s(7115),a=s(5104),i=s(6185),o=s.n(i),c=s(295),l=s(8140);const d=e=>{if("idle"!==e.status)return!1;const t=Number(localStorage.getItem("last_requested_scan"));if(!t)return!1;if(t<Date.now()-3e5)return!1;return!(new Date(e.lastChecked+" UTC").getTime()>t)},u=e=>{const t=!e?.lastChecked&&[l._R,l.Mm].includes(e?.status),s=l.vI.indexOf(e?.status)>=0;return t||s};function h({usePolling:e}={}){const t=(0,n.jE)(),{isRegistered:s}=(0,r.useConnection)({autoTrigger:!1,from:"protect",redirectUri:null,skipUserConnection:!0});return(0,a.I)({queryKey:[l.Gs],queryFn:async()=>{const e=await c.A.getScanStatus();return d(e)?t.getQueryData([l.Gs]):e},initialData:o()(window?.jetpackProtectInitialState?.status),enabled:s,refetchInterval(t){if(!e)return!1;const s=t.state.dataUpdateCount<5?5e3:15e3;return!!u(t.state.data)&&s}})}},240:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(7115),n=s(4613),a=s(295),i=s(8140);function o(){const e=(0,r.jE)();return(0,n.n)({mutationFn:a.A.scan,onMutate(){e.setQueryData([i.Gs],(e=>({...e,status:i.JD})))},onSuccess(){e.setQueryData([i.Gs],(e=>({...e,status:i.JD}))),localStorage.setItem("last_requested_scan",Date.now().toString())},onError(){e.invalidateQueries({queryKey:[i.Gs]})}})}},7208:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(7115),n=s(4613),a=s(7723),i=s(295),o=s(8140),c=s(4537);const __=a.__;function l(){const e=(0,r.jE)(),{showSuccessNotice:t,showErrorNotice:s}=(0,c.A)();return(0,n.n)({mutationFn:async t=>{const s=await i.A.unIgnoreThreat(t);return await Promise.all([e.refetchQueries({queryKey:[o.Gs]}),e.refetchQueries({queryKey:[o.Ss]})]),s},onSuccess:()=>{t(__("Threat is no longer ignored.","jetpack-protect"))},onError:()=>{s(__("An error occurred un-ignoring the threat.","jetpack-protect"))}})}},6829:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(9384),n=s(5104),a=s(295),i=s(8140);function o(){const{isRegistered:e}=(0,r.useConnection)({autoTrigger:!1,from:"protect",redirectUri:null,skipUserConnection:!0});return(0,n.I)({queryKey:[i.Oz],queryFn:a.A.checkPlan,initialData:!!window?.jetpackProtectInitialState?.hasPlan,enabled:e})}},7651:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(5104),n=s(6185),a=s.n(n),i=s(295),o=s(8140);function c(){return(0,r.I)({queryKey:[o.kD],queryFn:i.A.getProductData,initialData:a()(window?.jetpackProtectInitialState?.jetpackScan)})}},9481:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(7115),n=s(4613),a=s(7723),i=s(295),o=s(8140),c=s(4537);const __=a.__;function l(){const e=(0,r.jE)(),{showSuccessNotice:t,showErrorNotice:s}=(0,c.A)();return(0,n.n)({mutationFn:i.A.toggleWaf,onSuccess:()=>{t(__("WAF module enabled.","jetpack-protect"))},onError:()=>{s(__("An error occurred enabling the WAF module.","jetpack-protect"))},onSettled:()=>{e.invalidateQueries({queryKey:[o.yN]})}})}},3990:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(7115),n=s(4613),a=s(6087),i=s(7723),o=s(6185),c=s.n(o),l=s(295),d=s(8140),u=s(4537);const __=i.__;function h(){const e=(0,r.jE)(),{showSuccessNotice:t,showSavingNotice:s,showErrorNotice:i}=(0,u.A)(),o=(0,a.useCallback)((e=>{switch(e.code){case"file_system_error":return __("A filesystem error occurred.","jetpack-protect");case"rules_api_error":return __("An error occurred retrieving the latest firewall rules from Jetpack.","jetpack-protect");default:return __("An error occurred.","jetpack-protect")}}),[]);return(0,n.n)({mutationFn:l.A.updateWaf,onMutate:t=>{s();const r=e.getQueryData([d.yN]);return e.setQueryData([d.yN],(e=>({...e,config:{...e.config,...c()(t)}}))),{initialValue:r}},onSuccess:()=>{t(__("Changes saved.","jetpack-protect"))},onError:(t,s,r)=>{e.setQueryData([d.yN],r.initialValue),i(o(t))}})}},5728:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(5104),n=s(6185),a=s.n(n),i=s(295),o=s(8140);function c(){return(0,r.I)({queryKey:[o.yN],queryFn:i.A.getWaf,initialData:a()(window?.jetpackProtectInitialState?.waf)})}},5632:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(7115),n=s(4613),a=s(295),i=s(8140);function o(){const e=(0,r.jE)();return(0,n.n)({mutationFn:a.A.wafUpgradeSeen,onMutate:()=>{e.setQueryData([i.yN],(e=>({...e,upgradeIsSeen:!0})))}})}},1226:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(372),n=s(9384),a=s(1609);const i=({pageViewEventName:e,pageViewNamespace:t="jetpack",pageViewSuffix:s="page_view",pageViewEventProperties:i={}}={})=>{const{isUserConnected:o,isRegistered:c,userConnectionData:l}=(0,n.useConnection)(),{login:d,ID:u}=l.currentUser?.wpcomUser||{},{tracks:h}=r.A,{recordEvent:p}=h,m=(0,a.useCallback)((async(e,t)=>{p(e,t)}),[p]),g=(0,a.useCallback)(((e,t,s=()=>{})=>(s="function"==typeof t?t:s,t="function"==typeof t?{}:t,()=>m(e,t).then(s))),[m]);(0,a.useEffect)((()=>{o&&u&&d&&r.A.initialize(u,d)}),[o,u,d]);const f=e?`${t}_${e}_${s}`:null;return(0,a.useEffect)((()=>{c&&f&&p(f,i)}),[]),{recordEvent:m,recordEventHandler:g}}},3657:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>l,UM:()=>o});var r=s(1609),n=s(169),a=s(8064),i=s(2425);const o=e=>{const t=new Date,s=new Date(e);return t.getTime()-s.getTime()>=864e5},c=e=>"status"in e&&"in_progress"===e.status&&o(e.lastUpdated);function l(){const{data:e}=(0,i.Ay)(),t=(0,n.A)(),{data:s}=(0,a.A)({threatIds:e.fixableThreatIds,usePolling:!0}),o=(0,r.useCallback)((e=>{if(!1===s.ok)return!1;const t=s.threats?.[e];return t&&"status"in t&&"in_progress"===t.status}),[s]),l=(0,r.useCallback)((e=>{if(!1===s.ok)return!1;const t=s?.threats?.[e];return!!t&&c(t)}),[s]);return{fixableThreatIds:e.fixableThreatIds,fixersStatus:s,fixThreats:t.mutateAsync,isLoading:t.isPending,isThreatFixInProgress:o,isThreatFixStale:l}}},1009:(e,t,s)=>{"use strict";s.d(t,{A:()=>o,Z:()=>i});var r=s(1609),n=s(790);const a=(0,r.createContext)({modal:null,setModal:null}),i=({children:e})=>{const[t,s]=(0,r.useState)({});return(0,n.jsx)(a.Provider,{value:{modal:t,setModal:s},children:e})};function o(){const{modal:e,setModal:t}=(0,r.useContext)(a);return{modal:e,setModal:t}}},4537:(e,t,s)=>{"use strict";s.d(t,{A:()=>h,m:()=>u});var r=s(6427),n=s(6087),a=s(7723),i=s(1609),o=s(8140),c=s(5925),l=s(790);const __=a.__,d=(0,i.createContext)(void 0),u=({children:e})=>{const[t,s]=(0,i.useState)(null);return(0,l.jsx)(d.Provider,{value:{notice:t,setNotice:s},children:e})};function h(){const{hasPlan:e}=(0,c.Ay)(),{notice:t,setNotice:s}=(0,i.useContext)(d),a=(0,i.useCallback)((()=>{s(null)}),[s]),u=(0,i.useCallback)((e=>{s({type:"success",dismissable:!0,duration:7500,message:e})}),[s]);return{notice:t,clearNotice:a,showSavingNotice:(0,i.useCallback)((e=>{s({type:"info",dismissable:!1,message:e||__("Saving Changes…","jetpack-protect")})}),[s]),showSuccessNotice:u,showErrorNotice:(0,i.useCallback)((t=>{s({type:"error",dismissable:!0,message:(0,l.jsxs)(l.Fragment,{children:[t||__("An error occurred.","jetpack-protect")," ",(0,n.createInterpolateElement)(__("Please try again or <supportLink>contact support</supportLink>.","jetpack-protect"),{supportLink:(0,l.jsx)(r.ExternalLink,{href:e?o.NW:o.pQ})})]})})}),[e,s])}}},1936:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>d,Ll:()=>l,OC:()=>c,dn:()=>o});var r=s(1609),n=s(7534),a=s(5780),i=s(790);const o=(0,r.createContext)([]),c=(0,r.createContext)([]),l=({children:e})=>{const[t,s]=(0,r.useState)([]);return(0,i.jsx)(c.Provider,{value:{renderedSteps:t,setRenderedSteps:s},children:e})},d=()=>{const e=(0,r.useContext)(o),{renderedSteps:t}=(0,r.useContext)(c),{data:s}=(0,a.A)(),i=(0,n.A)(),{currentStep:l,currentStepCount:d,stepsCount:u}=(0,r.useMemo)((()=>e.reduce(((e,r)=>(t.includes(r.id)&&(e.stepsCount++,e.currentStep||-1!==(s||[]).indexOf(r.id)||(e.currentStep=r,e.currentStepCount=e.stepsCount)),e)),{currentStep:null,currentStepCount:null,stepsCount:0})),[s,t,e]),h=(0,r.useCallback)((()=>{l&&i.mutate([l.id])}),[l,i]),p=(0,r.useCallback)((()=>{const t=e.reduce(((e,t)=>(t.id.startsWith("free-")&&e.push(t.id),e)),[]);i.mutate(t)}),[e,i]),m=(0,r.useCallback)((()=>{const t=e.reduce(((e,t)=>(t.id.startsWith("paid-")&&e.push(t.id),e)),[]);i.mutate(t)}),[e,i]),g=(0,r.useCallback)((()=>{l.id.startsWith("paid-")?m():p()}),[p,m,l]);return{progress:s,stepsCount:u,currentStep:l,currentStepCount:d,completeCurrentStep:h,completeAllCurrentSteps:g}}},5925:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>h,sT:()=>d});var r=s(9384),n=s(1609),a=s(295),i=s(8140),o=s(6829),c=s(790);const l=(0,n.createContext)({hasCheckoutStarted:!1,setHasCheckoutStarted:()=>{}}),d=({children:e})=>{const[t,s]=(0,n.useState)(!1);return(0,c.jsx)(l.Provider,{value:{hasCheckoutStarted:t,setHasCheckoutStarted:s},children:e})},u=()=>(0,n.useContext)(l);function h({redirectUrl:e}={}){const{adminUrl:t}=window.jetpackProtectInitialState||{},{data:s,isLoading:c}=(0,o.A)(),{hasCheckoutStarted:l,setHasCheckoutStarted:d}=u(),{run:h}=(0,r.useProductCheckoutWorkflow)({productSlug:i.J3,redirectUrl:e||t,siteProductAvailabilityHandler:a.A.checkPlan,useBlogIdSuffix:!0,connectAfterCheckout:!1,from:()=>"protect"});return{hasPlan:s,upgradePlan:(0,n.useCallback)((()=>{d(!0),h()}),[h,d]),isLoading:c||l}}},9701:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(7723),n=s(1609),a=s(1859),i=s(2425),o=s(7651);const __=r.__,c=["all","core","plugins","themes","files","database"],l=(e,t,s)=>Array.isArray(e)?e.filter((e=>(!t.status||"all"===t.status||e.status===t.status)&&(!t.key||"all"===t.key||t.key===s))):[];function d({sourceType:e,filter:t}={sourceType:"scan",filter:{status:null,key:null}}){const{data:s}=(0,i.Ay)(),{data:r}=(0,a.A)(),{data:d}=(0,o.A)(),{counts:u,results:h,error:p,lastChecked:m,hasUncheckedItems:g}=(0,n.useMemo)((()=>{const n="history"===e?{...r}:{...s},a={results:{core:[],plugins:[],themes:[],files:[],database:[]},counts:{all:{threats:0,core:0,plugins:0,themes:0,files:0,database:0},current:{threats:0,core:0,plugins:0,themes:0,files:0,database:0}},error:null,lastChecked:n.lastChecked||null,hasUncheckedItems:n.hasUncheckedItems||!1},i=(e,s)=>{if(!Array.isArray(e))return[];e.forEach((e=>{a.counts.all[s]+=e?.threats?.length||0,a.counts.all.threats+=e?.threats?.length||0;const r=l(e?.threats||[],t,c.includes(t.key)?s:e?.name);a.results[s].push({...e,threats:r}),a.counts.current[s]+=r.length,a.counts.current.threats+=r.length}))},o=(e,s)=>{if(!Array.isArray(e))return[];a.counts.all[s]+=e.length,a.counts.all.threats+=e.length;const r=l(e,t,s);a.results[s]=[...a.results[s],...r],a.counts.current[s]+=r.length,a.counts.current.threats+=r.length};let d=Array.isArray(n.core)?n.core:[];return n?.core?.threats&&(d=[n.core]),i(d,"core"),i(n?.plugins,"plugins"),i(n?.themes,"themes"),o(n?.files,"files"),o(n?.database,"database"),n.error&&(a.error={message:n.errorMessage||__("An error occurred.","jetpack-protect"),code:n.errorCode||500}),a}),[r,e,s,t]);return{results:h,counts:u,error:p,lastChecked:m,hasUncheckedItems:g,jetpackScan:d}}},6516:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(1609),n=s(9481),a=s(3990),i=s(5728),o=s(1226);const c=()=>{const{recordEvent:e}=(0,o.A)(),{data:t}=(0,i.A)(),s=(0,a.A)(),c=(0,n.A)(),l=(0,r.useCallback)((async()=>{c.mutate()}),[c]),d=(0,r.useCallback)((async()=>!!t.isEnabled||await l()),[l,t.isEnabled]),u=(0,r.useCallback)((async()=>{const r=!t.config.jetpackWafAutomaticRules;await d(),await s.mutateAsync({jetpack_waf_automatic_rules:r}),e(r?"jetpack_protect_automatic_rules_enabled":"jetpack_protect_automatic_rules_disabled")}),[d,e,t.config.jetpackWafAutomaticRules,s]),h=(0,r.useCallback)((async()=>{const r=!t.config.jetpackWafIpAllowListEnabled;await s.mutateAsync({jetpack_waf_ip_allow_list_enabled:r}),e(r?"jetpack_protect_ip_allow_list_enabled":"jetpack_protect_ip_allow_list_disabled")}),[e,t.config.jetpackWafIpAllowListEnabled,s]),p=(0,r.useCallback)((async t=>{await s.mutateAsync({jetpack_waf_ip_allow_list:t}),e("jetpack_protect_ip_allow_list_updated")}),[e,s]),m=(0,r.useCallback)((async()=>{const r=!t.config.jetpackWafIpBlockListEnabled;await d(),await s.mutateAsync({jetpack_waf_ip_block_list_enabled:r}),e(r?"jetpack_protect_ip_block_list_enabled":"jetpack_protect_ip_block_list_disabled")}),[d,e,t.config.jetpackWafIpBlockListEnabled,s]),g=(0,r.useCallback)((async t=>{await d(),await s.mutateAsync({jetpack_waf_ip_block_list:t}),e("jetpack_protect_ip_block_list_updated")}),[d,s,e]),f=(0,r.useCallback)((async()=>{const r=!t.config.bruteForceProtection;await s.mutateAsync({brute_force_protection:r}),e(r?"jetpack_protect_brute_force_protection_enabled":"jetpack_protect_brute_force_protection_disabled")}),[e,t.config.bruteForceProtection,s]),v=(0,r.useCallback)((async()=>{const r=!t.config.jetpackWafShareData,n={jetpack_waf_share_data:r};r||(n.jetpack_waf_share_debug_data=!1),await s.mutateAsync(n),e(n?"jetpack_protect_share_data_enabled":"jetpack_protect_share_data_disabled")}),[e,t.config.jetpackWafShareData,s]),j=(0,r.useCallback)((async()=>{const r=!t.config.jetpackWafShareDebugData,n={jetpack_waf_share_debug_data:r};r&&(n.jetpack_waf_share_data=!0),await s.mutateAsync(n),e(r?"jetpack_protect_share_debug_data_enabled":"jetpack_protect_share_debug_data_disabled")}),[e,t.config.jetpackWafShareDebugData,s]);return{...t,isUpdating:s.isPending,isToggling:c.isPending,toggleWaf:l,toggleAutomaticRules:u,toggleIpAllowList:h,saveIpAllowList:p,toggleIpBlockList:m,saveIpBlockList:g,toggleBruteForceProtection:f,toggleShareData:v,toggleShareDebugData:j}}},6088:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(7425),n=s(1158),a=s(7723),i=s(1609),o=s(7031),c=s(6516),l=s(9347),d=s(5546),u=s(790);const __=a.__,h=()=>{const{config:{jetpackWafAutomaticRules:e,automaticRulesAvailable:t,standaloneMode:s,bruteForceProtection:a},isEnabled:h,wafSupported:p,isToggling:m}=(0,c.A)(),g=m?"loading":(p?h:a)?"on":"off",f=(0,i.useMemo)((()=>"on"===g?s?__("Standalone mode","jetpack-protect"):__("Active","jetpack-protect",0):__("Inactive","jetpack-protect")),[g,s]),v=(0,i.useMemo)((()=>"on"===g?(0,u.jsxs)(u.Fragment,{children:[!p&&__("Brute force protection is active","jetpack-protect"),p&&(e?__("Automatic firewall is on","jetpack-protect"):__("Firewall is on","jetpack-protect",0))]}):"off"===g?(0,u.jsxs)(u.Fragment,{children:[!p&&__("Brute force protection is disabled","jetpack-protect"),p&&(t?__("Automatic firewall is off","jetpack-protect"):__("Firewall is off","jetpack-protect",0))]}):"loading"===g?__("Automatic firewall is being set up","jetpack-protect"):null),[g,p,e,t]),j=(0,i.useMemo)((()=>"loading"===g?(0,u.jsx)(r.Ay,{children:__("Please wait…","jetpack-protect")}):(0,u.jsx)(d.A,{})),[g]);return(0,u.jsx)(o.A,{main:(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(n.A,{status:"on"===g?"active":"inactive",label:f}),(0,u.jsx)(o.A.Heading,{children:v}),(0,u.jsx)(o.A.Subheading,{children:j})]}),secondary:p&&(0,u.jsx)(l.A,{})})}},8567:(e,t,s)=>{"use strict";s.d(t,{A:()=>v});var r=s(7425),n=s(1112),a=s(8316),i=s(5918),o=s(8509),c=s(7723),l=s(1609),d=s(1009),u=s(4537),h=s(6516),p=s(4257),m=s(790);const __=c.__,g=()=>{const{setModal:e}=(0,d.A)();return(0,m.jsxs)("div",{className:p.A["standalone-mode"],children:[(0,m.jsx)(r.hE,{mb:0,children:__("Standalone mode","jetpack-protect")}),(0,m.jsxs)("div",{children:[(0,m.jsx)(r.Ay,{children:__("Learn how you can execute the firewall before WordPress initializes.","jetpack-protect")}),(0,m.jsx)(r.Ay,{children:__("This mode offers the most protection.","jetpack-protect")})]}),(0,m.jsx)(n.A,{variant:"link",isExternalLink:!0,weight:"regular",onClick:t=>{t.preventDefault(),e({type:"STANDALONE_MODE"})},children:__("Learn more","jetpack-protect")})]})},f=()=>{const{config:e,isUpdating:t,toggleShareData:s,toggleShareDebugData:n}=(0,h.A)(),{jetpackWafShareData:i,jetpackWafShareDebugData:o}=e||{},{showSuccessNotice:c,showErrorNotice:d}=(0,u.A)(),g=(0,l.useCallback)((async()=>{try{await s(),c(__("Changes saved.","jetpack-protect"))}catch{d()}}),[s,c,d]),f=(0,l.useCallback)((async()=>{try{await n(),c(__("Changes saved.","jetpack-protect"))}catch{d()}}),[n,c,d]);return(0,m.jsxs)("div",{className:p.A["share-data"],children:[(0,m.jsx)(r.hE,{mb:0,children:__("Share data with Jetpack","jetpack-protect")}),(0,m.jsx)(a.A,{className:p.A["share-data-toggle"],checked:!!i,onChange:g,disabled:t,size:"small",label:__("Share basic data","jetpack-protect"),help:__("Allow Jetpack to collect basic data from blocked requests to improve firewall protection and accuracy.","jetpack-protect")}),(0,m.jsx)(a.A,{className:p.A["share-data-toggle"],checked:!!o,onChange:f,disabled:t,size:"small",label:__("Share detailed data","jetpack-protect"),help:__("Allow Jetpack to collect detailed data from blocked requests to enhance firewall protection and accuracy.","jetpack-protect")})]})},v=()=>{const{isEnabled:e}=(0,h.A)();return(0,m.jsx)("div",{className:p.A.footer,children:(0,m.jsxs)(i.A,{horizontalSpacing:8,horizontalGap:7,children:[(0,m.jsx)(o.A,{sm:12,md:6,lg:7,children:(0,m.jsx)(g,{})}),e&&(0,m.jsx)(o.A,{sm:12,md:6,lg:5,children:(0,m.jsx)(f,{})})]})})}},9347:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var r=s(442),n=s(7425),a=s(3390),i=s(7723),o=s(1113),c=s(5302),l=s(9648),d=s(1609),u=s(5925),h=s(6516),p=s(4257),m=s(790);const __=i.__,g=()=>{const{hasPlan:e}=(0,u.Ay)(),{config:{bruteForceProtection:t},isEnabled:s,wafSupported:g,stats:f}=(0,h.A)(),[v]=(0,r.A)(["sm","lg"],[null,"<"]),j=g?s:t,{currentDay:x,thirtyDays:y}=f?f.blockedRequests:{currentDay:0,thirtyDays:0},b=!j||!e,w=(0,d.useMemo)((()=>({className:b?p.A.disabled:p.A.active,variant:v?"horizontal":"square"})),[b,v]),A=(0,d.useCallback)((({icon:t})=>(0,m.jsxs)("span",{className:p.A["stat-card-icon"],children:[(0,m.jsx)(o.A,{icon:t}),!v&&!e&&(0,m.jsx)(n.Ay,{variant:"label",children:__("Paid feature","jetpack-protect")})]})),[v,e]),k=(0,d.useCallback)((({period:e,units:t})=>v?(0,m.jsx)("span",{children:(0,i.sprintf)(/* translators: Translates to Blocked requests last %1$d: Number of units. %2$s: Unit of time (hours, days, etc) */
__("Blocked requests last %1$d %2$s","jetpack-protect"),e,t)}):(0,m.jsxs)("span",{className:p.A["stat-card-label"],children:[(0,m.jsx)("span",{children:__("Blocked requests","jetpack-protect")}),(0,m.jsx)("br",{}),(0,m.jsx)("span",{children:(0,i.sprintf)(/* translators: Translates to Last %1$d: Number of units. %2$s: Unit of time (hours, days, etc) */
__("Last %1$d %2$s","jetpack-protect"),e,t)})]})),[v]),C=(0,d.useMemo)((()=>({...w,icon:(0,m.jsx)(A,{icon:c.A}),label:(0,m.jsx)(k,{period:24,units:"hours"}),value:b?0:x})),[w,A,k,b,x]),_=(0,d.useMemo)((()=>({...w,icon:(0,m.jsx)(A,{icon:l.A}),label:(0,m.jsx)(k,{period:30,units:"days"}),value:b?0:y})),[w,A,k,b,y]);return(0,m.jsxs)("div",{className:p.A["stat-card-wrapper"],children:[(0,m.jsx)(a.A,{...C}),(0,m.jsx)(a.A,{..._})]})}},5546:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(597),n=s(7425),a=s(7723),i=s(1609),o=s(5925),c=s(6516),l=s(3969),d=s(4257),u=s(790);const __=a.__,h=()=>{const{hasPlan:e}=(0,o.Ay)(),{config:{jetpackWafAutomaticRules:t,jetpackWafIpBlockListEnabled:s,jetpackWafIpAllowListEnabled:a,automaticRulesAvailable:h,bruteForceProtection:p},wafSupported:m}=(0,c.A)(),g=s||a,f=(0,i.useMemo)((()=>({allRules:m&&t&&g,automaticRules:m&&t&&!g,manualRules:m&&!t&&g,noRules:m&&!t&&!g})),[m,t,g]),v=(0,i.useMemo)((()=>{const e=[];return m&&p&&e.push(__("Brute force protection is active.","jetpack-protect")),f.noRules&&e.push(__("There are no firewall rules applied.","jetpack-protect")),f.automaticRules&&e.push(__("Automatic firewall rules apply.","jetpack-protect")),f.manualRules&&e.push(__("Only manual IP list rules apply.","jetpack-protect")),f.allRules&&e.push(__("All firewall rules apply.","jetpack-protect")),e.join(" ")}),[m,p,f]),j=(0,i.useMemo)((()=>h?__("The free version of the firewall does not receive updates to automatic security rules.","jetpack-protect",0):__("The free version of the firewall only allows for use of manual rules.","jetpack-protect")),[h]);return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsxs)("div",{className:d.A["firewall-subheading"],children:[(0,u.jsx)(n.Ay,{children:v}),!e&&(f.automaticRules||f.manualRules||f.allRules)?(0,u.jsx)(r.A,{className:d.A["icon-tooltip"],iconCode:"help-outline",iconSize:20,iconClassName:d.A["icon-tooltip__icon"],placement:"top",hoverShow:!0,children:(0,u.jsx)(n.Ay,{children:j})}):null]}),!e&&m&&(0,u.jsx)(l.A,{})]})}},3969:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(1112),n=s(7723),a=s(1609),i=s(1226),o=s(5925),c=s(6516),l=s(4257),d=s(790);const __=n.__,u=()=>{const{recordEvent:e}=(0,i.A)(),{adminUrl:t}=window.jetpackProtectInitialState||{},s=t+"#/firewall",{upgradePlan:n}=(0,o.Ay)({redirectUrl:s}),{config:{automaticRulesAvailable:u}}=(0,c.A)(),h=(0,a.useCallback)((()=>{e("jetpack_protect_waf_header_get_scan_link_click"),n()}),[e,n]);return(0,d.jsx)(r.A,{className:l.A["upgrade-prompt-button"],onClick:h,children:u?__("Upgrade to update automatic security rules","jetpack-protect",0):__("Upgrade to enable automatic firewall protection","jetpack-protect")})}},1671:(e,t,s)=>{"use strict";s.d(t,{A:()=>P});var r=s(442),n=s(7656),a=s(7425),i=s(1112),o=s(8316),c=s(4437),l=s(5918),d=s(8509),u=s(6427),h=s(6087),p=s(7723),m=s(1113),g=s(1249),f=s(6154),v=s.n(f),j=s(1609),x=s(1186),y=s(4491),b=s(8140),w=s(5632),A=s(1226),k=s(5925),C=s(6516),_=s(5285),S=s(6088),N=s(8567),M=s(4257),L=s(790);const __=p.__,E=window?.jetpackProtectInitialState?.adminUrl,P=()=>{const[e]=(0,r.A)(["sm","lg"],[null,"<"]),{config:{jetpackWafAutomaticRules:t,jetpackWafIpBlockListEnabled:s,jetpackWafIpAllowListEnabled:f,jetpackWafIpBlockList:P,jetpackWafIpAllowList:R,automaticRulesAvailable:z,bruteForceProtection:F},currentIp:O,isEnabled:I,upgradeIsSeen:D,displayUpgradeBadge:V,wafSupported:H,isUpdating:T,stats:B,toggleAutomaticRules:$,toggleIpAllowList:q,saveIpAllowList:U,toggleIpBlockList:Q,saveIpBlockList:J,toggleBruteForceProtection:W,toggleWaf:Z}=(0,C.A)(),{hasPlan:G}=(0,k.Ay)(),{upgradePlan:K}=(0,k.Ay)({redirectUrl:`${E}#/firewall`}),{recordEvent:X}=(0,A.A)(),Y=(0,w.A)(),{automaticRulesLastUpdated:ee}=B,[te,se]=(0,j.useState)(!1),[re,ne]=(0,j.useState)({jetpack_waf_ip_block_list:P,jetpack_waf_ip_allow_list:R}),ae=I&&!T,ie=I&&(G||z),oe=!T&&f,ce=re.jetpack_waf_ip_block_list!==P,le=re.jetpack_waf_ip_allow_list!==R,de=!!re.jetpack_waf_ip_block_list,ue=!!re.jetpack_waf_ip_allow_list,he=I&&s,pe=(0,j.useCallback)((()=>{X("jetpack_protect_waf_page_get_scan_link_click"),K()}),[X,K]),me=(0,j.useCallback)((e=>{const{value:t,id:s}=e.target;ne({...re,[s]:t})}),[re]),ge=(0,j.useCallback)((()=>{ne((e=>({...e,jetpack_waf_automatic_rules:!e.jetpack_waf_automatic_rules})));try{$(),se(!1)}catch{se(!0),ne((e=>({...e,jetpack_waf_automatic_rules:!e.jetpack_waf_automatic_rules})))}}),[$]),fe=(0,j.useCallback)((async()=>{await J(re.jetpack_waf_ip_block_list)}),[J,re.jetpack_waf_ip_block_list]),ve=(0,j.useCallback)((async()=>{await U(re.jetpack_waf_ip_allow_list)}),[U,re.jetpack_waf_ip_allow_list]),je=(0,j.useCallback)((()=>{Y.mutate()}),[Y]),xe=(0,j.useMemo)((()=>re.jetpack_waf_ip_allow_list?.includes(O)),[re.jetpack_waf_ip_allow_list,O]),ye=(0,j.useCallback)((()=>{const e=re.jetpack_waf_ip_allow_list.length>0?`${re.jetpack_waf_ip_allow_list}\n${O}`:O;ne((t=>({...t,jetpack_waf_ip_allow_list:e})))}),[re.jetpack_waf_ip_allow_list,O]);(0,j.useEffect)((()=>{T||ne({jetpack_waf_ip_block_list:P,jetpack_waf_ip_allow_list:R})}),[P,R,T]),(0,A.A)({pageViewEventName:"protect_waf",pageViewEventProperties:{has_plan:G}});const be=(0,L.jsx)(n.A,{level:"error",title:"Jetpack Firewall is currently disabled.",children:(0,L.jsx)(a.Ay,{children:__("Re-enable the Firewall to continue.","jetpack-protect")}),actions:[(0,L.jsx)(i.A,{variant:"link",onClick:Z,isLoading:T,disabled:T,children:__("Enable Firewall","jetpack-protect")},"enable")],hideCloseButton:!0}),we=(0,L.jsxs)(L.Fragment,{children:[(0,L.jsxs)("div",{className:`${M.A["toggle-section"]} ${ie?"":M.A["toggle-section--disabled"]}`,children:[(0,L.jsxs)("div",{className:M.A["toggle-section__control"],children:[(0,L.jsx)(o.A,{checked:!!ie&&t,onChange:ge,disabled:!ae||!ie||T}),G&&!1===D&&(0,L.jsx)(u.Popover,{noArrow:!1,offset:8,position:"top right",inline:!0,children:(0,L.jsxs)("div",{className:M.A.popover,children:[(0,L.jsxs)("div",{className:M.A.popover__header,children:[(0,L.jsx)(a.Ay,{className:M.A.popover__title,variant:"title-small",children:__("Thanks for upgrading!","jetpack-protect")}),(0,L.jsx)(i.A,{className:M.A.popover__button,variant:"icon",children:(0,L.jsx)(m.A,{onClick:je,icon:g.A,size:24,"aria-label":__("Close Window","jetpack-protect")})})]}),(0,L.jsx)(a.Ay,{className:M.A.popover__description,variant:"body",mt:2,mb:3,children:__("Turn on Automatic firewall protection to apply the latest security rules.","jetpack-protect")}),(0,L.jsx)("div",{className:M.A.popover__footer,children:(0,L.jsx)(i.A,{onClick:je,children:__("Got it","jetpack-protect")})})]})})]}),(0,L.jsxs)("div",{className:M.A["toggle-section__content"],children:[(0,L.jsxs)("div",{className:M.A["toggle-section__title"],children:[(0,L.jsx)(a.Ay,{variant:"title-medium",mb:2,children:__("Automatic firewall protection","jetpack-protect")}),!e&&G&&V&&(0,L.jsx)("span",{className:M.A.badge,children:__("NOW AVAILABLE","jetpack-protect")})]}),(0,L.jsx)(a.Ay,{children:__("Block untrusted traffic by scanning every request made to your site. Jetpack’s security rules are always up-to-date to protect against the latest threats.","jetpack-protect")}),(0,L.jsxs)("div",{className:M.A["toggle-section__details"],children:[t&&ee&&!te&&(0,L.jsxs)("div",{className:M.A["automatic-rules-stats"],children:[(0,L.jsx)(a.Ay,{className:M.A["automatic-rules-stats__version"],variant:"body-small",children:__("Automatic security rules installed.","jetpack-protect")}),(0,L.jsx)(a.Ay,{className:M.A["automatic-rules-stats__last-updated"],variant:"body-small",children:(0,p.sprintf)(
// translators: placeholder is the date latest rules were updated i.e. "September 23, 2022".
__("Last updated on %s.","jetpack-protect"),v().unix(ee).format("MMMM D, YYYY"))})]}),te&&(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(a.Ay,{className:M.A["automatic-rules-stats__failed-install"],variant:"body-small",mt:2,children:__("Failed to update automatic firewall rules.","jetpack-protect")}),(0,L.jsx)(i.A,{variant:"link",href:G?b.NW:b.pQ,children:(0,L.jsx)(a.Ay,{variant:"body-small",children:__("Contact support","jetpack-protect")})})]})]})]})]}),!G&&(0,L.jsx)("div",{className:M.A["upgrade-trigger-section"],children:(0,L.jsx)(c.A,{className:M.A["upgrade-trigger"],description:ie?__("Your site is not receiving the latest updates to automatic rules","jetpack-protect",0):__("Set up automatic rules with one click","jetpack-protect"),cta:ie?__("Upgrade to keep your site secure with up-to-date firewall rules","jetpack-protect",0):__("Upgrade to enable automatic firewall protection","jetpack-protect"),onClick:pe})})]}),Ae=(0,L.jsxs)("div",{className:M.A["toggle-section"],children:[(0,L.jsx)("div",{className:M.A["toggle-section__control"],children:(0,L.jsx)(o.A,{checked:F,onChange:W,disabled:T})}),(0,L.jsxs)("div",{className:M.A["toggle-section__content"],children:[(0,L.jsx)(a.Ay,{variant:"title-medium",mb:2,children:__("Brute force protection","jetpack-protect")}),(0,L.jsx)(a.Ay,{children:__("Prevent bots and hackers from attempting to log in to your website with common username and password combinations.","jetpack-protect")})]})]}),ke=(0,L.jsxs)("div",{className:`${M.A["toggle-section"]} ${I?"":M.A["toggle-section--disabled"]}`,children:[(0,L.jsx)("div",{className:M.A["toggle-section__control"],children:(0,L.jsx)(o.A,{checked:he,onChange:Q,disabled:!ae})}),(0,L.jsxs)("div",{className:M.A["toggle-section__content"],children:[(0,L.jsx)(a.Ay,{variant:"title-medium",mb:2,children:__("Block IP addresses","jetpack-protect")}),(0,L.jsx)(a.Ay,{mb:1,children:__("Stop specific visitors from accessing your site by their IP address.","jetpack-protect")}),(he||de)&&(0,L.jsxs)("div",{className:M.A["manual-rules-section"],children:[(0,L.jsx)(y.A,{id:"jetpack_waf_ip_block_list",placeholder:__("Example:","jetpack-protect")+"\n12.12.12.1\n12.12.12.2",rows:3,value:re.jetpack_waf_ip_block_list,onChange:me,disabled:!ae||!he}),he&&(0,L.jsx)(a.Ay,{variant:"body-extra-small",mt:1,children:__("IPv4 and IPv6 supported. Separate IPs with commas, spaces, or new lines. To specify a range, use CIDR notation (i.e. **********/24) or enter the low value and high value separated by a dash (i.e. **********–************).","jetpack-protect")})]}),he&&(0,L.jsx)("div",{className:M.A["block-list-button-container"],children:(0,L.jsx)(i.A,{onClick:fe,isLoading:T,disabled:!ae||!ce,children:__("Save block list","jetpack-protect")})})]})]}),Ce=(0,L.jsx)(L.Fragment,{children:(0,L.jsxs)("div",{className:M.A["toggle-section"],children:[(0,L.jsx)("div",{className:M.A["toggle-section__control"],children:(0,L.jsx)(o.A,{checked:f,onChange:q,disabled:T})}),(0,L.jsxs)("div",{className:M.A["toggle-section__content"],children:[(0,L.jsx)(a.Ay,{variant:"title-medium",mb:2,children:__("Trusted IP addresses","jetpack-protect")}),(0,L.jsx)(a.Ay,{mb:1,children:__("IP addresses added to this list are always allowed to access your site, regardless of any other Jetpack security settings.","jetpack-protect")}),(f||ue)&&(0,L.jsxs)("div",{className:M.A["manual-rules-section"],children:[(0,L.jsx)(y.A,{id:"jetpack_waf_ip_allow_list",placeholder:__("Example:","jetpack-protect")+"\n12.12.12.1\n12.12.12.2",rows:3,value:re.jetpack_waf_ip_allow_list,onChange:me,disabled:!oe}),f&&(0,L.jsx)(a.Ay,{variant:"body-extra-small",mt:1,children:__("IPv4 and IPv6 supported. Separate IPs with commas, spaces, or new lines. To specify a range, use CIDR notation (i.e. **********/24) or enter the low value and high value separated by a dash (i.e. **********–************).","jetpack-protect")})]}),f&&(0,L.jsxs)("div",{className:M.A["allow-list-button-container"],children:[(0,L.jsxs)("div",{children:[(0,L.jsx)(a.Ay,{variant:"body-small",className:M.A["allow-list-current-ip"],children:(0,h.createInterpolateElement)((0,p.sprintf)(
// translators: placeholder is the user's current IP address.
__("Your current IP: <strong>%s</strong>","jetpack-protect"),O),{strong:(0,L.jsx)("strong",{})})}),(0,L.jsx)(i.A,{variant:"secondary",size:"small",onClick:ye,disabled:!oe||xe||T,children:__("+ Add to Allow List","jetpack-protect")})]}),(0,L.jsx)(i.A,{onClick:ve,isLoading:T,disabled:T||!le,children:__("Save allow list","jetpack-protect")})]})]})]})});return(0,L.jsxs)(x.A,{children:[(0,L.jsx)(S.A,{}),(0,L.jsxs)(l.A,{className:M.A.container,horizontalSpacing:8,horizontalGap:4,children:[H&&!I&&(0,L.jsxs)(d.A,{children:[be," "]}),(0,L.jsx)(d.A,{children:(0,L.jsxs)("div",{className:M.A["toggle-wrapper"],children:[H&&we,Ae,H&&(0,L.jsxs)(L.Fragment,{children:[ke,(0,L.jsx)("div",{className:M.A.divider})]}),Ce]})})]}),H?(0,L.jsx)(N.A,{}):(0,L.jsx)(_.A,{})]})}},2014:(e,t,s)=>{"use strict";s.d(t,{A:()=>f});var r=s(1158),n=s(7425),a=s(8443),i=s(7723),o=s(1609),c=s(880),l=s(7031),d=s(2652),u=s(9889),h=s(993),p=s(9701),m=s(5787),g=s(790);const __=i.__,f=()=>{const{filter:e="all"}=(0,c.g)(),{list:t}=(0,h.A)({source:"history",status:e}),{counts:s,error:f}=(0,p.A)({sourceType:"history",filter:{status:e}}),{threats:v}=s.all,j=(0,o.useMemo)((()=>t.length?t.reduce(((e,t)=>new Date(t.firstDetected)<new Date(e.firstDetected)?t:e)).firstDetected:null),[t]);return f?(0,g.jsx)(d.A,{baseErrorMessage:__("We are having problems loading your history.","jetpack-protect"),errorMessage:f?.message,errorCode:f?.code}):(0,g.jsx)(l.A,{main:(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(r.A,{status:"active",label:__("Active","jetpack-protect")}),(0,g.jsx)(l.A.Heading,{showIcon:!0,children:v>0?(0,i.sprintf)(/* translators: %s: Total number of threats  */
__("%1$s previously active %2$s","jetpack-protect"),v,1===v?"threat":"threats"):__("No previously active threats","jetpack-protect")}),(0,g.jsx)(l.A.Subheading,{children:(0,g.jsx)(n.Ay,{children:j?(0,g.jsx)("span",{className:m.A["subheading-content"],children:(0,i.sprintf)(/* translators: %s: Oldest first detected date */
__("%s - Today","jetpack-protect"),(0,a.dateI18n)("F jS g:i A",j,!1))}):__("Most recent results","jetpack-protect")})}),(0,g.jsx)("div",{className:m.A["scan-navigation"],children:(0,g.jsx)(u.A,{})})]})})}},5717:(e,t,s)=>{"use strict";s.d(t,{A:()=>A});var r=s(5640),n=s(5918),a=s(8509),i=s(7425),o=s(7723),c=s(1609),l=s(880),d=s(1186),u=s(7827),h=s(9395),p=s(6742),m=s(993),g=s(1226),f=s(5925),v=s(9701),j=s(5285),x=s(2014),y=s(2621),b=s(5787),w=s(790);const __=o.__,_n=o._n,A=()=>{(0,g.A)({pageViewEventName:"protect_scan_history"});const{hasPlan:e}=(0,f.Ay)(),{filter:t="all"}=(0,l.g)(),{item:s,list:A,selected:k,setSelected:C}=(0,m.A)({source:"history",status:t}),{counts:_,error:S}=(0,v.A)({sourceType:"history",filter:{status:t}}),{threats:N}=_.all,{counts:M}=(0,v.A)({sourceType:"history",filter:{status:"fixed",key:k}}),{threats:L}=M.current,{counts:E}=(0,v.A)({sourceType:"history",filter:{status:"ignored",key:k}}),{threats:P}=E.current,R=(0,c.useCallback)((()=>{switch(k){case"all":if(1===A.length)switch(t){case"fixed":return __("All fixed threats","jetpack-protect");case"ignored":return __("All ignored threats","jetpack-protect",0);default:return __("All threats","jetpack-protect")}switch(t){case"fixed":return(0,o.sprintf)(/* translators: placeholder is the amount of fixed threats found on the site. */
__("All %s fixed threats","jetpack-protect"),A.length);case"ignored":return(0,o.sprintf)(/* translators: placeholder is the amount of ignored threats found on the site. */
__("All %s ignored threats","jetpack-protect"),A.length);default:return(0,o.sprintf)(/* translators: placeholder is the amount of threats found on the site. */
__("All %s threats","jetpack-protect"),A.length)}case"core":switch(t){case"fixed":return(0,o.sprintf)(/* translators: placeholder is the amount of fixed WordPress threats found on the site. */
_n("%1$s fixed WordPress threat","%1$s fixed WordPress threats",A.length,"jetpack-protect"),A.length);case"ignored":return(0,o.sprintf)(/* translators: placeholder is the amount of ignored WordPress threats found on the site. */
_n("%1$s ignored WordPress threat","%1$s ignored WordPress threats",A.length,"jetpack-protect"),A.length);default:return(0,o.sprintf)(/* translators: placeholder is the amount of WordPress threats found on the site. */
_n("%1$s WordPress threat","%1$s WordPress threats",A.length,"jetpack-protect"),A.length)}case"files":switch(t){case"fixed":return(0,o.sprintf)(/* translators: placeholder is the amount of fixed file threats found on the site. */
_n("%1$s fixed file threat","%1$s fixed file threats",A.length,"jetpack-protect"),A.length);case"ignored":return(0,o.sprintf)(/* translators: placeholder is the amount of ignored file threats found on the site. */
_n("%1$s ignored file threat","%1$s ignored file threats",A.length,"jetpack-protect"),A.length);default:return(0,o.sprintf)(/* translators: placeholder is the amount of file threats found on the site. */
_n("%1$s file threat","%1$s file threats",A.length,"jetpack-protect"),A.length)}case"database":switch(t){case"fixed":return(0,o.sprintf)(/* translators: placeholder is the amount of fixed database threats found on the site. */
_n("%1$s fixed database threat","%1$s fixed database threats",A.length,"jetpack-protect"),A.length);case"ignored":return(0,o.sprintf)(/* translators: placeholder is the amount of ignored database threats found on the site. */
_n("%1$s ignored database threat","%1$s ignored database threats",A.length,"jetpack-protect"),A.length);default:return(0,o.sprintf)(/* translators: placeholder is the amount of database threats found on the site. */
_n("%1$s database threat","%1$s database threats",A.length,"jetpack-protect"),A.length)}default:switch(t){case"fixed":return(0,o.sprintf)(/* translators: Translates to "123 fixed threats in Example Plugin (1.2.3)" */
_n("%1$s fixed threat in %2$s %3$s","%1$s fixed threats in %2$s %3$s",A.length,"jetpack-protect"),A.length,s?.name,s?.version);case"ignored":return(0,o.sprintf)(/* translators: Translates to "123 ignored threats in Example Plugin (1.2.3)" */
_n("%1$s ignored threat in %2$s %3$s","%1$s ignored threats in %2$s %3$s",A.length,"jetpack-protect"),A.length,s?.name,s?.version);default:return(0,o.sprintf)(/* translators: Translates to "123 threats in Example Plugin (1.2.3)" */
_n("%1$s threat in %2$s %3$s","%1$s threats in %2$s %3$s",A.length,"jetpack-protect"),A.length,s?.name,s?.version)}}}),[k,A.length,t,s?.name,s?.version]);return e?0===A.length&&"all"!==t?(0,w.jsx)(l.C5,{to:"/scan/history"}):(0,w.jsxs)(d.A,{children:[(0,w.jsx)(x.A,{}),(!S||N)&&(0,w.jsx)(r.A,{children:(0,w.jsx)(n.A,{horizontalSpacing:7,horizontalGap:4,children:(0,w.jsx)(a.A,{children:(0,w.jsxs)(n.A,{fluid:!0,horizontalSpacing:0,horizontalGap:3,children:[(0,w.jsx)(a.A,{lg:4,children:(0,w.jsx)(h.A,{selected:k,onSelect:C,sourceType:"history",statusFilter:t})}),(0,w.jsx)(a.A,{lg:8,children:A.length>0?(0,w.jsxs)("div",{children:[(0,w.jsxs)("div",{className:b.A["list-header"],children:[(0,w.jsx)(i.hE,{className:b.A["list-title"],children:R()}),(0,w.jsx)("div",{className:b.A["list-header__controls"],children:(0,w.jsx)(y.A,{numFixed:L,numIgnored:P})})]}),(0,w.jsx)(p.A,{list:A,hideAutoFixColumn:!0})]}):(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)("div",{className:b.A["list-header"],children:(0,w.jsx)("div",{className:b.A["list-header__controls"],children:(0,w.jsx)(y.A,{})})}),(0,w.jsxs)("div",{className:b.A.empty,children:[(0,w.jsx)(u.A,{}),(0,w.jsx)(i.H3,{weight:"bold",mt:8,children:__("Don't worry about a thing","jetpack-protect")}),(0,w.jsx)(i.Ay,{mb:4,children:(0,o.sprintf)(/* translators: %s: Filter type */
__("There are no%sthreats in your scan history.","jetpack-protect"),"all"===t?" ":` ${t} `)})]})]})})]})})})}),(0,w.jsx)(j.A,{})]}):(0,w.jsx)(l.C5,{to:"/scan"})}},2621:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(7723),n=s(1609),a=s(880),i=s(4907),o=s(790);const __=r.__;function c({numFixed:e,numIgnored:t}){const s=(0,a.Zp)(),{filter:r="all"}=(0,a.g)(),c=(0,n.useCallback)((e=>()=>s(e)),[s]);return(0,o.jsxs)(i.A,{children:[(0,o.jsx)(i.A.Button,{variant:"all"===r?"primary":"secondary",onClick:c("/scan/history"),children:__("All","jetpack-protect")}),(0,o.jsx)(i.A.Button,{variant:"fixed"===r?"primary":"secondary",onClick:c("/scan/history/fixed"),disabled:!e,children:__("Fixed","jetpack-protect")}),(0,o.jsx)(i.A.Button,{variant:"ignored"===r?"primary":"secondary",onClick:c("/scan/history/ignored"),disabled:!t,children:__("Ignored","jetpack-protect")})]})}},5148:(e,t,s)=>{"use strict";s.d(t,{A:()=>v});var r=s(5640),n=s(5918),a=s(8509),i=s(1186),o=s(5661),c=s(2425),l=s(1226),d=s(1936),u=s(5925),h=s(9701),p=s(3411),m=s(4446),g=s(5285),f=s(790);const v=()=>{const{hasPlan:e}=(0,u.Ay)(),{counts:{current:{threats:t}},lastChecked:s}=(0,h.A)(),{data:v}=(0,c.Ay)({usePolling:!0});let j;return j=v.error?"error":s?"active":"in_progress",(0,l.A)({pageViewEventName:"protect_admin",pageViewEventProperties:{check_status:j,has_plan:e}}),(0,f.jsx)(d.dn.Provider,{value:p.A,children:(0,f.jsxs)(i.A,{children:[(0,f.jsx)(m.A,{}),(!v.error||t)&&(0,f.jsx)(r.A,{children:(0,f.jsx)(n.A,{horizontalSpacing:7,horizontalGap:4,children:(0,f.jsx)(a.A,{children:(0,f.jsx)(o.A,{})})})}),(0,f.jsx)(g.A,{})]})})}},3411:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var r=s(7425),n=s(1112),a=s(3924),i=s(6087),o=s(7723),c=s(1226),l=s(5925),d=s(790);const __=o.__,{siteSuffix:u}=window.jetpackProtectInitialState,h=__("Your scan results","jetpack-protect"),p=(0,d.jsx)(r.Ay,{children:__("Navigate through the results of the scan on your WordPress installation, plugins, themes, and other files","jetpack-protect")}),m=e=>{const{upgradePlan:t}=(0,l.Ay)(),{recordEvent:s}=(0,c.A)(),r=(0,i.useCallback)((()=>{s("jetpack_protect_onboarding_get_scan_link_click"),t()}),[s,t]);return(0,d.jsx)(n.A,{variant:"link",weight:"regular",onClick:r,...e})},g=[{id:"free-scan-results",title:h,description:p},{id:"free-daily-scans",title:__("Daily automated scans","jetpack-protect"),description:(0,d.jsx)(r.Ay,{children:(0,i.createInterpolateElement)(__("We run daily automated scans. Do you want to be able to scan manually? <upgradeLink>Upgrade</upgradeLink>","jetpack-protect"),{upgradeLink:(0,d.jsx)(m,{})})})},{id:"paid-scan-results",title:h,description:p},{id:"paid-fix-all-threats",title:__("Auto-fix with one click","jetpack-protect"),description:(0,d.jsxs)(r.Ay,{children:[__("Jetpack Protect offers one-click fixes for most threats. Press this button to be safe again.","jetpack-protect"),(0,d.jsx)("br",{}),(0,d.jsx)("br",{}),(0,i.createInterpolateElement)(__("Note that you'll have to <credentialsLink>input your server credentials</credentialsLink> first.","jetpack-protect"),{credentialsLink:(0,d.jsx)(n.A,{variant:"link",weight:"regular",href:(0,a.A)("jetpack-settings-security-credentials",{site:u})})})]})},{id:"paid-understand-severity",title:__("Understand severity","jetpack-protect"),description:(0,d.jsx)(r.Ay,{children:__("Learn how critical these threats are for the security of your site by glancing at the severity labels.","jetpack-protect")})},{id:"paid-daily-and-manual-scans",title:__("Daily & manual scanning","jetpack-protect"),description:(0,d.jsx)(r.Ay,{children:__("We run daily automated scans but you can also run on-demand scans if you want to check the latest status.","jetpack-protect")})}]},4446:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var r=s(442),n=s(1158),a=s(7425),i=s(8443),o=s(7723),c=s(1609),l=s(7031),d=s(2652),u=s(4254),h=s(9889),p=s(2425),m=s(5925),g=s(9701),f=s(3752),v=s(6852),j=s(790);const __=o.__,_n=o._n,x=()=>{const{hasPlan:e}=(0,m.Ay)(),[t]=(0,r.A)("sm"),{counts:{current:{threats:s}},lastChecked:x}=(0,g.A)(),{data:y}=(0,p.Ay)(),[b,w]=(0,c.useState)(null);let A=null;return x&&(A=new Date(x+" UTC").getTime()),(0,p.EV)(y)?(0,j.jsx)(f.A,{}):y.error?(0,j.jsx)(d.A,{baseErrorMessage:__("We are having problems scanning your site.","jetpack-protect"),errorMessage:y.errorMessage,errorCode:y.errorCode}):(0,j.jsx)(l.A,{main:(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(n.A,{status:"active",label:__("Active","jetpack-protect")}),(0,j.jsx)(l.A.Heading,{showIcon:!0,children:s>0?(0,o.sprintf)(/* translators: %s: Total number of threats/vulnerabilities */
__("%1$s %2$s found","jetpack-protect"),s,e?_n("threat","threats",s,"jetpack-protect"):_n("vulnerability","vulnerabilities",s,"jetpack-protect")):(0,o.sprintf)(/* translators: %s: Pluralized type of threat/vulnerability */
__("No %s found","jetpack-protect"),e?__("threats","jetpack-protect"):__("vulnerabilities","jetpack-protect",0))}),(0,j.jsx)(l.A.Subheading,{children:(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(a.Ay,{ref:w,children:A?(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("span",{className:v.A["subheading-content"],children:(0,i.dateI18n)("F jS g:i A",A,!1)})," ",__("results","jetpack-protect")]}):__("Most recent results","jetpack-protect")}),!e&&(0,j.jsx)(u.A,{id:"free-daily-scans",position:t?"bottom":"middle right",anchor:b})]})}),(0,j.jsx)("div",{className:v.A["scan-navigation"],children:(0,j.jsx)(h.A,{})})]})})}},5285:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var r=s(3924),n=s(7425),a=s(1112),i=s(4437),o=s(5918),c=s(8509),l=s(7723),d=s(1609),u=s(3127),h=s(1226),p=s(5925),m=s(6516),g=s(6852),f=s(790);const __=l.__,v=()=>{const{recordEvent:e}=(0,h.A)(),{hasPlan:t,upgradePlan:s}=(0,p.Ay)(),{siteSuffix:o,blogID:c}=window.jetpackProtectInitialState||{},l=(0,d.useCallback)((()=>{e("jetpack_protect_footer_get_scan_link_click"),s()}),[e,s]);if(t){const e=(0,r.A)("jetpack-scan-dash",{site:c??o});return(0,f.jsxs)("div",{className:g.A["product-section"],children:[(0,f.jsx)(n.hE,{children:__("Get access to our Cloud","jetpack-protect")}),(0,f.jsx)(n.Ay,{mb:3,children:__("With your Protect upgrade, you have free access to scan your site on our Cloud, so you can be aware and fix your threats even if your site goes down.","jetpack-protect")}),(0,f.jsx)(a.A,{variant:"secondary",weight:"regular",href:e,children:__("Go to Cloud","jetpack-protect")})]})}return(0,f.jsxs)("div",{className:g.A["product-section"],children:[(0,f.jsx)(n.hE,{children:__("Advanced scan results","jetpack-protect")}),(0,f.jsx)(n.Ay,{mb:3,children:__("Upgrade Jetpack Protect to get advanced scan tools, including one-click fixes for most threats and malware scanning.","jetpack-protect")}),(0,f.jsx)(i.A,{description:__("Looking for advanced scan results and one-click fixes?","jetpack-protect"),cta:__("Upgrade Jetpack Protect now","jetpack-protect"),onClick:l})]})},j=()=>{const{hasPlan:e}=(0,p.Ay)(),{globalStats:t}=(0,m.A)(),s=parseInt(t?.totalVulnerabilities),i=isNaN(s)?"50,000":s.toLocaleString();if(e){const e=(0,r.A)("protect-footer-learn-more-scan");return(0,f.jsxs)("div",{className:g.A["info-section"],children:[(0,f.jsx)(n.hE,{children:__("Line-by-line scanning","jetpack-protect")}),(0,f.jsxs)(n.Ay,{mb:2,children:[__("We actively review line-by-line of your site files to identify threats and vulnerabilities. Jetpack monitors millions of websites to keep your site secure all the time.","jetpack-protect")," ",(0,f.jsx)(a.A,{variant:"link",target:"_blank",weight:"regular",href:e,children:__("Learn more","jetpack-protect")})]})]})}const o=(0,r.A)("jetpack-protect-footer-learn-more");return(0,f.jsxs)("div",{className:g.A["info-section"],children:[(0,f.jsx)(n.hE,{children:(0,l.sprintf)(
// translators: placeholder is the number of total vulnerabilities i.e. "22,000".
__("Over %s listed vulnerabilities","jetpack-protect"),i)}),(0,f.jsx)(n.Ay,{mb:3,children:(0,l.sprintf)(
// translators: placeholder is the number of total vulnerabilities i.e. "22,000".
__("Every day we check your plugin, theme, and WordPress versions against our %s listed vulnerabilities powered by WPScan, an Automattic brand.","jetpack-protect"),i)}),(0,f.jsx)(a.A,{variant:"link",isExternalLink:!0,href:o,weight:"regular",children:__("Learn more","jetpack-protect")})]})},x=()=>{const{waf:e}=window.jetpackProtectInitialState||{};return e.wafSupported?(0,f.jsx)(u.A,{main:(0,f.jsx)(v,{}),secondary:(0,f.jsx)(j,{}),preserveSecondaryOnMobile:!0}):(0,f.jsx)(o.A,{horizontalSpacing:0,horizontalGap:0,fluid:!1,children:(0,f.jsx)(c.A,{children:(0,f.jsx)(j,{})})})}},3752:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(7425),n=s(7723),a=s(7031),i=s(9671),o=s(4256),c=s(9889),l=s(2425),d=s(5925),u=s(6516),h=s(6852),p=s(790);const __=n.__,m=()=>{const{hasPlan:e}=(0,d.Ay)(),{globalStats:t}=(0,u.A)(),{data:s}=(0,l.Ay)({usePolling:!0}),m=parseInt(t?.totalVulnerabilities||"0"),g=isNaN(m)?"50,000":m.toLocaleString();return(0,p.jsx)(a.A,{main:(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(a.A.Heading,{children:__("Your results will be ready soon","jetpack-protect")}),(0,p.jsx)(a.A.Subheading,{children:(0,p.jsxs)(p.Fragment,{children:[e&&(0,p.jsx)(o.A,{className:h.A.progress,value:s?.currentProgress,total:100}),(0,p.jsx)(r.Ay,{children:(0,n.sprintf)(
// translators: placeholder is the number of total vulnerabilities i.e. "22,000".
__("We are scanning for security threats from our more than %s listed vulnerabilities, powered by WPScan. This could take a minute or two.","jetpack-protect"),g)})]})}),(0,p.jsx)("div",{className:h.A["scan-navigation"],children:(0,p.jsx)(c.A,{})})]}),secondary:(0,p.jsx)(i.A,{}),preserveSecondaryOnMobile:!1,spacing:4})}},9580:(e,t,s)=>{"use strict";s.d(t,{A:()=>k});var r=s(8316),n=s(7425),a=s(7656),i=s(1112),o=s(3924),c=s(766),l=s(5918),d=s(8509),u=s(6427),h=s(6087),p=s(7723),m=s(1113),g=s(9783),f=s(1609),v=s(1186),j=s(9676),x=s(1729),y=s(1226),b=s(5925),w=s(2952),A=s(790);const __=p.__,k=()=>{const{hasPlan:e}=(0,b.Ay)(),{data:t}=(0,j.A)(),s=(0,x.A)(),p=(0,f.useCallback)((async()=>{s.mutate()}),[s]);(0,y.A)({pageViewEventName:"protect_account_protection",pageViewEventProperties:{has_plan:e}});const k=(0,A.jsxs)("div",{className:w.A["toggle-section"],children:[(0,A.jsx)("div",{className:w.A["toggle-section__control"],children:(0,A.jsx)(r.A,{checked:t.isSupported&&!t.hasUnsupportedJetpackVersion&&t.isEnabled,onChange:p,disabled:!t.isSupported||t.hasUnsupportedJetpackVersion||s.isPending})}),(0,A.jsxs)("div",{className:w.A["toggle-section__content"],children:[(0,A.jsx)(n.Ay,{variant:"title-medium",children:__("Account protection","jetpack-protect")}),!t.isSupported&&(0,A.jsx)(a.A,{level:"warning",hideCloseButton:!0,className:w.A["toggle-section__alert"],title:(0,A.jsx)(n.Ay,{children:__("This feature has been disabled by your site administrator or hosting provider.","jetpack-protect")}),actions:[(0,A.jsx)(i.A,{variant:"link",isExternalLink:!0,href:(0,o.A)("jetpack-account-protection",{anchor:"unsupported-environments"}),children:__("Learn more","jetpack-protect")},"learn-more")]}),t.isSupported&&t.hasUnsupportedJetpackVersion&&(0,A.jsx)(a.A,{level:"warning",hideCloseButton:!0,className:w.A["toggle-section__alert"],title:(0,A.jsx)(n.Ay,{children:__("This feature has been disabled because the Jetpack Protect plugin is installed with an unsupported version of the Jetpack plugin. Please update the Jetpack plugin to version 14.5 or later to enable this feature.","jetpack-protect")}),actions:[(0,A.jsx)(i.A,{variant:"link",isExternalLink:!0,href:(0,o.A)("jetpack-account-protection",{anchor:"requirements"}),children:__("Learn more","jetpack-protect")},"learn-more")]}),(0,A.jsx)(n.Ay,{className:w.A["toggle-section__description"],children:(0,h.createInterpolateElement)(__("Enabling this setting enhances account security by detecting compromised passwords and enforcing additional verification when needed. Learn more about <link>how this protects your site</link>.","jetpack-protect"),{link:(0,A.jsx)(u.ExternalLink,{href:(0,o.A)("jetpack-account-protection")})})}),(0,A.jsx)(n.Ay,{children:__("Protect your site with advanced password detection and profile management protection.","jetpack-protect")}),!t.isEnabled&&t.isSupported&&(0,A.jsxs)(n.Ay,{className:w.A["toggle-section__info"],children:[(0,A.jsx)(m.A,{icon:g.A}),(0,h.createInterpolateElement)(__("Jetpack recommends enabling this feature to enhance account security. <link>Learn about the risks</link>.","jetpack-protect"),{link:(0,A.jsx)(u.ExternalLink,{href:(0,o.A)("jetpack-account-protection-risks")})})]})]})]});return(0,A.jsx)(v.A,{children:(0,A.jsx)(c.A,{children:(0,A.jsx)(l.A,{className:w.A.container,horizontalSpacing:8,horizontalGap:4,children:(0,A.jsx)(d.A,{children:(0,A.jsx)("div",{className:w.A["toggle-wrapper"],children:k})})})})})}},2970:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var r=s(2947),n=s(1608),a=s(7425),i=s(766),o=s(5918),c=s(8509),l=s(6087),d=s(7723),u=s(9374),h=s(1226),p=s(3728),m=s(790);const __=d.__,g=()=>((0,h.A)({pageViewEventName:"protect_interstitial"}),(0,m.jsx)(r.A,{moduleName:__("Jetpack Protect","jetpack-protect"),header:(0,m.jsxs)("div",{className:p.A["protect-header"],children:[(0,m.jsx)(n.A,{}),(0,m.jsx)(a.Ay,{variant:"body-small",children:(0,l.createInterpolateElement)(__("Already have an existing plan or license key? <a>Click here to get started</a>","jetpack-protect"),{a:(0,m.jsx)("a",{href:"admin.php?page=my-jetpack#/add-license"})})})]}),children:(0,m.jsx)(i.A,{children:(0,m.jsx)(o.A,{horizontalSpacing:3,horizontalGap:3,children:(0,m.jsx)(c.A,{sm:4,md:8,lg:12,children:(0,m.jsx)(u.A,{})})})})}))},6992:e=>{"use strict";e.exports={consumer_slug:"jetpack-protect"}},9384:e=>{"use strict";e.exports=window.JetpackConnection},7999:e=>{"use strict";e.exports=window.JetpackScriptDataModule},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},6154:e=>{"use strict";e.exports=window.moment},1455:e=>{"use strict";e.exports=window.wp.apiFetch},6427:e=>{"use strict";e.exports=window.wp.components},9491:e=>{"use strict";e.exports=window.wp.compose},8443:e=>{"use strict";e.exports=window.wp.date},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},5573:e=>{"use strict";e.exports=window.wp.primitives},3832:e=>{"use strict";e.exports=window.wp.url},2464:(e,t,s)=>{"use strict";s.d(t,{m:()=>a});var r=s(5530),n=s(698),a=new class extends r.Q{#e;#t;#s;constructor(){super(),this.#s=e=>{if(!n.S$&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#s=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}}},9101:(e,t,s)=>{"use strict";s.d(t,{PL:()=>n});var r=s(698);function n(e){return{onFetch:(t,s)=>{const n=t.options,o=t.fetchOptions?.meta?.fetchMore?.direction,c=t.state.data?.pages||[],l=t.state.data?.pageParams||[];let d={pages:[],pageParams:[]},u=0;const h=async()=>{let s=!1;const h=(0,r.ZM)(t.options,t.fetchOptions),p=async(e,n,a)=>{if(s)return Promise.reject();if(null==n&&e.pages.length)return Promise.resolve(e);const i={client:t.client,queryKey:t.queryKey,pageParam:n,direction:a?"backward":"forward",meta:t.options.meta};var o;o=i,Object.defineProperty(o,"signal",{enumerable:!0,get:()=>(t.signal.aborted?s=!0:t.signal.addEventListener("abort",(()=>{s=!0})),t.signal)});const c=await h(i),{maxPages:l}=t.options,d=a?r.ZZ:r.y9;return{pages:d(e.pages,c,l),pageParams:d(e.pageParams,n,l)}};if(o&&c.length){const e="backward"===o,t={pages:c,pageParams:l},s=(e?i:a)(n,t);d=await p(t,s,e)}else{const t=e??c.length;do{const e=0===u?l[0]??n.initialPageParam:a(n,d);if(u>0&&null==e)break;d=await p(d,e),u++}while(u<t)}return d};t.options.persister?t.fetchFn=()=>t.options.persister?.(h,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s):t.fetchFn=h}}}function a(e,{pages:t,pageParams:s}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,s[r],s):void 0}function i(e,{pages:t,pageParams:s}){return t.length>0?e.getPreviousPageParam?.(t[0],t,s[0],s):void 0}},4352:(e,t,s)=>{"use strict";s.d(t,{$:()=>o,s:()=>i});var r=s(1795),n=s(1450),a=s(4610),i=class extends n.k{#r;#n;#a;constructor(e){super(),this.mutationId=e.mutationId,this.#n=e.mutationCache,this.#r=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#r.includes(e)||(this.#r.push(e),this.clearGcTimeout(),this.#n.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#r=this.#r.filter((t=>t!==e)),this.scheduleGc(),this.#n.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#r.length||("pending"===this.state.status?this.scheduleGc():this.#n.remove(this))}continue(){return this.#a?.continue()??this.execute(this.state.variables)}async execute(e){const t=()=>{this.#i({type:"continue"})};this.#a=(0,a.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#i({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#i({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#n.canRun(this)});const s="pending"===this.state.status,r=!this.#a.canStart();try{if(s)t();else{this.#i({type:"pending",variables:e,isPaused:r}),await(this.#n.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#i({type:"pending",context:t,variables:e,isPaused:r})}const n=await this.#a.start();return await(this.#n.config.onSuccess?.(n,e,this.state.context,this)),await(this.options.onSuccess?.(n,e,this.state.context)),await(this.#n.config.onSettled?.(n,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(n,null,e,this.state.context)),this.#i({type:"success",data:n}),n}catch(t){try{throw await(this.#n.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#n.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#i({type:"error",error:t})}}finally{this.#n.runNext(this)}}#i(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),r.jG.batch((()=>{this.#r.forEach((t=>{t.onMutationUpdate(e)})),this.#n.notify({mutation:this,type:"updated",action:e})}))}};function o(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},7708:(e,t,s)=>{"use strict";s.d(t,{q:()=>o});var r=s(1795),n=s(4352),a=s(698),i=s(5530),o=class extends i.Q{constructor(e={}){super(),this.config=e,this.#o=new Set,this.#c=new Map,this.#l=0}#o;#c;#l;build(e,t,s){const r=new n.s({mutationCache:this,mutationId:++this.#l,options:e.defaultMutationOptions(t),state:s});return this.add(r),r}add(e){this.#o.add(e);const t=c(e);if("string"==typeof t){const s=this.#c.get(t);s?s.push(e):this.#c.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#o.delete(e)){const t=c(e);if("string"==typeof t){const s=this.#c.get(t);if(s)if(s.length>1){const t=s.indexOf(e);-1!==t&&s.splice(t,1)}else s[0]===e&&this.#c.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){const t=c(e);if("string"==typeof t){const s=this.#c.get(t),r=s?.find((e=>"pending"===e.state.status));return!r||r===e}return!0}runNext(e){const t=c(e);if("string"==typeof t){const s=this.#c.get(t)?.find((t=>t!==e&&t.state.isPaused));return s?.continue()??Promise.resolve()}return Promise.resolve()}clear(){r.jG.batch((()=>{this.#o.forEach((e=>{this.notify({type:"removed",mutation:e})})),this.#o.clear(),this.#c.clear()}))}getAll(){return Array.from(this.#o)}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,a.nJ)(t,e)))}findAll(e={}){return this.getAll().filter((t=>(0,a.nJ)(e,t)))}notify(e){r.jG.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){const e=this.getAll().filter((e=>e.state.isPaused));return r.jG.batch((()=>Promise.all(e.map((e=>e.continue().catch(a.lQ))))))}};function c(e){return e.options.scope?.id}},4158:(e,t,s)=>{"use strict";s.d(t,{_:()=>o});var r=s(4352),n=s(1795),a=s(5530),i=s(698),o=class extends a.Q{#d;#u=void 0;#h;#p;constructor(e,t){super(),this.#d=e,this.setOptions(t),this.bindMethods(),this.#m()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#d.defaultMutationOptions(e),(0,i.f8)(this.options,t)||this.#d.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#h,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,i.EN)(t.mutationKey)!==(0,i.EN)(this.options.mutationKey)?this.reset():"pending"===this.#h?.state.status&&this.#h.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#h?.removeObserver(this)}onMutationUpdate(e){this.#m(),this.#g(e)}getCurrentResult(){return this.#u}reset(){this.#h?.removeObserver(this),this.#h=void 0,this.#m(),this.#g()}mutate(e,t){return this.#p=t,this.#h?.removeObserver(this),this.#h=this.#d.getMutationCache().build(this.#d,this.options),this.#h.addObserver(this),this.#h.execute(e)}#m(){const e=this.#h?.state??(0,r.$)();this.#u={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#g(e){n.jG.batch((()=>{if(this.#p&&this.hasListeners()){const t=this.#u.variables,s=this.#u.context;"success"===e?.type?(this.#p.onSuccess?.(e.data,t,s),this.#p.onSettled?.(e.data,null,t,s)):"error"===e?.type&&(this.#p.onError?.(e.error,t,s),this.#p.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach((e=>{e(this.#u)}))}))}}},1795:(e,t,s)=>{"use strict";s.d(t,{jG:()=>n});var r=e=>setTimeout(e,0);var n=function(){let e=[],t=0,s=e=>{e()},n=e=>{e()},a=r;const i=r=>{t?e.push(r):a((()=>{s(r)}))};return{batch:r=>{let i;t++;try{i=r()}finally{t--,t||(()=>{const t=e;e=[],t.length&&a((()=>{n((()=>{t.forEach((e=>{s(e)}))}))}))})()}return i},batchCalls:e=>(...t)=>{i((()=>{e(...t)}))},schedule:i,setNotifyFunction:e=>{s=e},setBatchNotifyFunction:e=>{n=e},setScheduler:e=>{a=e}}}()},5873:(e,t,s)=>{"use strict";s.d(t,{t:()=>a});var r=s(5530),n=s(698),a=new class extends r.Q{#f=!0;#t;#s;constructor(){super(),this.#s=e=>{if(!n.S$&&window.addEventListener){const t=()=>e(!0),s=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#t||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#s=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#f!==e&&(this.#f=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#f}}},4135:(e,t,s)=>{"use strict";s.d(t,{X:()=>o,k:()=>c});var r=s(698),n=s(1795),a=s(4610),i=s(1450),o=class extends i.k{#v;#j;#x;#d;#a;#y;#b;constructor(e){super(),this.#b=!1,this.#y=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#d=e.client,this.#x=this.#d.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#v=function(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,s=void 0!==t,r=s?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#v,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#a?.promise}setOptions(e){this.options={...this.#y,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#x.remove(this)}setData(e,t){const s=(0,r.pl)(this.state.data,e,this.options);return this.#i({data:s,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),s}setState(e,t){this.#i({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#a?.promise;return this.#a?.cancel(e),t?t.then(r.lQ).catch(r.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#v)}isActive(){return this.observers.some((e=>!1!==(0,r.Eh)(e.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===r.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((e=>e.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,r.j3)(this.state.dataUpdatedAt,e)}onFocus(){const e=this.observers.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#a?.continue()}onOnline(){const e=this.observers.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#a?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#x.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter((t=>t!==e)),this.observers.length||(this.#a&&(this.#b?this.#a.cancel({revert:!0}):this.#a.cancelRetry()),this.scheduleGc()),this.#x.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#i({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#a)return this.#a.continueRetry(),this.#a.promise;if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find((e=>e.options.queryFn));e&&this.setOptions(e.options)}const s=new AbortController,n=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#b=!0,s.signal)})},i={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#d,state:this.state,fetchFn:()=>{const e=(0,r.ZM)(this.options,t),s={client:this.#d,queryKey:this.queryKey,meta:this.meta};return n(s),this.#b=!1,this.options.persister?this.options.persister(e,s,this):e(s)}};n(i),this.options.behavior?.onFetch(i,this),this.#j=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===i.fetchOptions?.meta||this.#i({type:"fetch",meta:i.fetchOptions?.meta});const o=e=>{(0,a.wm)(e)&&e.silent||this.#i({type:"error",error:e}),(0,a.wm)(e)||(this.#x.config.onError?.(e,this),this.#x.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#a=(0,a.II)({initialPromise:t?.initialPromise,fn:i.fetchFn,abort:s.abort.bind(s),onSuccess:e=>{if(void 0!==e){try{this.setData(e)}catch(e){return void o(e)}this.#x.config.onSuccess?.(e,this),this.#x.config.onSettled?.(e,this.state.error,this),this.scheduleGc()}else o(new Error(`${this.queryHash} data is undefined`))},onError:o,onFail:(e,t)=>{this.#i({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#i({type:"pause"})},onContinue:()=>{this.#i({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#a.start()}#i(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...c(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=e.error;return(0,a.wm)(s)&&s.revert&&this.#j?{...this.#j,fetchStatus:"idle"}:{...t,error:s,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),n.jG.batch((()=>{this.observers.forEach((e=>{e.onQueryUpdate()})),this.#x.notify({query:this,type:"updated",action:e})}))}};function c(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,a.v_)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},3205:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var r=s(698),n=s(4135),a=s(1795),i=s(5530),o=class extends i.Q{constructor(e={}){super(),this.config=e,this.#w=new Map}#w;build(e,t,s){const a=t.queryKey,i=t.queryHash??(0,r.F$)(a,t);let o=this.get(i);return o||(o=new n.X({client:e,queryKey:a,queryHash:i,options:e.defaultQueryOptions(t),state:s,defaultOptions:e.getQueryDefaults(a)}),this.add(o)),o}add(e){this.#w.has(e.queryHash)||(this.#w.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#w.get(e.queryHash);t&&(e.destroy(),t===e&&this.#w.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){a.jG.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#w.get(e)}getAll(){return[...this.#w.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,r.MK)(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>(0,r.MK)(e,t))):t}notify(e){a.jG.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){a.jG.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){a.jG.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}}},116:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var r=s(698),n=s(3205),a=s(7708),i=s(2464),o=s(5873),c=s(1795),l=s(9101),d=class{#A;#n;#y;#k;#C;#_;#S;#N;constructor(e={}){this.#A=e.queryCache||new n.$,this.#n=e.mutationCache||new a.q,this.#y=e.defaultOptions||{},this.#k=new Map,this.#C=new Map,this.#_=0}mount(){this.#_++,1===this.#_&&(this.#S=i.m.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#A.onFocus())})),this.#N=o.t.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#A.onOnline())})))}unmount(){this.#_--,0===this.#_&&(this.#S?.(),this.#S=void 0,this.#N?.(),this.#N=void 0)}isFetching(e){return this.#A.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#n.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#A.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),s=this.#A.build(this,t),n=s.state.data;return void 0===n?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime((0,r.d2)(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(n))}getQueriesData(e){return this.#A.findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,s){const n=this.defaultQueryOptions({queryKey:e}),a=this.#A.get(n.queryHash),i=a?.state.data,o=(0,r.Zw)(t,i);if(void 0!==o)return this.#A.build(this,n).setData(o,{...s,manual:!0})}setQueriesData(e,t,s){return c.jG.batch((()=>this.#A.findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,s)]))))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#A.get(t.queryHash)?.state}removeQueries(e){const t=this.#A;c.jG.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const s=this.#A;return c.jG.batch((()=>(s.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries({type:"active",...e},t))))}cancelQueries(e,t={}){const s={revert:!0,...t},n=c.jG.batch((()=>this.#A.findAll(e).map((e=>e.cancel(s)))));return Promise.all(n).then(r.lQ).catch(r.lQ)}invalidateQueries(e,t={}){return c.jG.batch((()=>(this.#A.findAll(e).forEach((e=>{e.invalidate()})),"none"===e?.refetchType?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))))}refetchQueries(e,t={}){const s={...t,cancelRefetch:t.cancelRefetch??!0},n=c.jG.batch((()=>this.#A.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,s);return s.throwOnError||(t=t.catch(r.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(n).then(r.lQ)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const s=this.#A.build(this,t);return s.isStaleByTime((0,r.d2)(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(r.lQ).catch(r.lQ)}fetchInfiniteQuery(e){return e.behavior=(0,l.PL)(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(r.lQ).catch(r.lQ)}ensureInfiniteQueryData(e){return e.behavior=(0,l.PL)(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return o.t.isOnline()?this.#n.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#A}getMutationCache(){return this.#n}getDefaultOptions(){return this.#y}setDefaultOptions(e){this.#y=e}setQueryDefaults(e,t){this.#k.set((0,r.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#k.values()],s={};return t.forEach((t=>{(0,r.Cp)(e,t.queryKey)&&Object.assign(s,t.defaultOptions)})),s}setMutationDefaults(e,t){this.#C.set((0,r.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#C.values()],s={};return t.forEach((t=>{(0,r.Cp)(e,t.mutationKey)&&Object.assign(s,t.defaultOptions)})),s}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#y.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,r.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===r.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#y.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#A.clear(),this.#n.clear()}}},3397:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var r=s(2464),n=s(1795),a=s(4135),i=s(5530),o=s(2415),c=s(698),l=class extends i.Q{constructor(e,t){super(),this.options=t,this.#d=e,this.#M=null,this.#L=(0,o.T)(),this.options.experimental_prefetchInRender||this.#L.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#d;#E=void 0;#P=void 0;#u=void 0;#R;#z;#L;#M;#F;#O;#I;#D;#V;#H;#T=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#E.addObserver(this),d(this.#E,this.options)?this.#B():this.updateResult(),this.#$())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return u(this.#E,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return u(this.#E,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#q(),this.#U(),this.#E.removeObserver(this)}setOptions(e){const t=this.options,s=this.#E;if(this.options=this.#d.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,c.Eh)(this.options.enabled,this.#E))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#Q(),this.#E.setOptions(this.options),t._defaulted&&!(0,c.f8)(this.options,t)&&this.#d.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#E,observer:this});const r=this.hasListeners();r&&h(this.#E,s,this.options,t)&&this.#B(),this.updateResult(),!r||this.#E===s&&(0,c.Eh)(this.options.enabled,this.#E)===(0,c.Eh)(t.enabled,this.#E)&&(0,c.d2)(this.options.staleTime,this.#E)===(0,c.d2)(t.staleTime,this.#E)||this.#J();const n=this.#W();!r||this.#E===s&&(0,c.Eh)(this.options.enabled,this.#E)===(0,c.Eh)(t.enabled,this.#E)&&n===this.#H||this.#Z(n)}getOptimisticResult(e){const t=this.#d.getQueryCache().build(this.#d,e),s=this.createResult(t,e);return function(e,t){if(!(0,c.f8)(e.getCurrentResult(),t))return!0;return!1}(this,s)&&(this.#u=s,this.#z=this.options,this.#R=this.#E.state),s}getCurrentResult(){return this.#u}trackResult(e,t){return new Proxy(e,{get:(e,s)=>(this.trackProp(s),t?.(s),Reflect.get(e,s))})}trackProp(e){this.#T.add(e)}getCurrentQuery(){return this.#E}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#d.defaultQueryOptions(e),s=this.#d.getQueryCache().build(this.#d,t);return s.fetch().then((()=>this.createResult(s,t)))}fetch(e){return this.#B({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#u)))}#B(e){this.#Q();let t=this.#E.fetch(this.options,e);return e?.throwOnError||(t=t.catch(c.lQ)),t}#J(){this.#q();const e=(0,c.d2)(this.options.staleTime,this.#E);if(c.S$||this.#u.isStale||!(0,c.gn)(e))return;const t=(0,c.j3)(this.#u.dataUpdatedAt,e)+1;this.#D=setTimeout((()=>{this.#u.isStale||this.updateResult()}),t)}#W(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#E):this.options.refetchInterval)??!1}#Z(e){this.#U(),this.#H=e,!c.S$&&!1!==(0,c.Eh)(this.options.enabled,this.#E)&&(0,c.gn)(this.#H)&&0!==this.#H&&(this.#V=setInterval((()=>{(this.options.refetchIntervalInBackground||r.m.isFocused())&&this.#B()}),this.#H))}#$(){this.#J(),this.#Z(this.#W())}#q(){this.#D&&(clearTimeout(this.#D),this.#D=void 0)}#U(){this.#V&&(clearInterval(this.#V),this.#V=void 0)}createResult(e,t){const s=this.#E,r=this.options,n=this.#u,i=this.#R,l=this.#z,u=e!==s?e.state:this.#P,{state:m}=e;let g,f={...m},v=!1;if(t._optimisticResults){const n=this.hasListeners(),i=!n&&d(e,t),o=n&&h(e,s,t,r);(i||o)&&(f={...f,...(0,a.k)(m.data,e.options)}),"isRestoring"===t._optimisticResults&&(f.fetchStatus="idle")}let{error:j,errorUpdatedAt:x,status:y}=f;g=f.data;let b=!1;if(void 0!==t.placeholderData&&void 0===g&&"pending"===y){let e;n?.isPlaceholderData&&t.placeholderData===l?.placeholderData?(e=n.data,b=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#I?.state.data,this.#I):t.placeholderData,void 0!==e&&(y="success",g=(0,c.pl)(n?.data,e,t),v=!0)}if(t.select&&void 0!==g&&!b)if(n&&g===i?.data&&t.select===this.#F)g=this.#O;else try{this.#F=t.select,g=t.select(g),g=(0,c.pl)(n?.data,g,t),this.#O=g,this.#M=null}catch(e){this.#M=e}this.#M&&(j=this.#M,g=this.#O,x=Date.now(),y="error");const w="fetching"===f.fetchStatus,A="pending"===y,k="error"===y,C=A&&w,_=void 0!==g,S={status:y,fetchStatus:f.fetchStatus,isPending:A,isSuccess:"success"===y,isError:k,isInitialLoading:C,isLoading:C,data:g,dataUpdatedAt:f.dataUpdatedAt,error:j,errorUpdatedAt:x,failureCount:f.fetchFailureCount,failureReason:f.fetchFailureReason,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>u.dataUpdateCount||f.errorUpdateCount>u.errorUpdateCount,isFetching:w,isRefetching:w&&!A,isLoadingError:k&&!_,isPaused:"paused"===f.fetchStatus,isPlaceholderData:v,isRefetchError:k&&_,isStale:p(e,t),refetch:this.refetch,promise:this.#L};if(this.options.experimental_prefetchInRender){const t=e=>{"error"===S.status?e.reject(S.error):void 0!==S.data&&e.resolve(S.data)},r=()=>{const e=this.#L=S.promise=(0,o.T)();t(e)},n=this.#L;switch(n.status){case"pending":e.queryHash===s.queryHash&&t(n);break;case"fulfilled":"error"!==S.status&&S.data===n.value||r();break;case"rejected":"error"===S.status&&S.error===n.reason||r()}}return S}updateResult(){const e=this.#u,t=this.createResult(this.#E,this.options);if(this.#R=this.#E.state,this.#z=this.options,void 0!==this.#R.data&&(this.#I=this.#E),(0,c.f8)(t,e))return;this.#u=t;this.#g({listeners:(()=>{if(!e)return!0;const{notifyOnChangeProps:t}=this.options,s="function"==typeof t?t():t;if("all"===s||!s&&!this.#T.size)return!0;const r=new Set(s??this.#T);return this.options.throwOnError&&r.add("error"),Object.keys(this.#u).some((t=>{const s=t;return this.#u[s]!==e[s]&&r.has(s)}))})()})}#Q(){const e=this.#d.getQueryCache().build(this.#d,this.options);if(e===this.#E)return;const t=this.#E;this.#E=e,this.#P=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#$()}#g(e){n.jG.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#u)})),this.#d.getQueryCache().notify({query:this.#E,type:"observerResultsUpdated"})}))}};function d(e,t){return function(e,t){return!1!==(0,c.Eh)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)}(e,t)||void 0!==e.state.data&&u(e,t,t.refetchOnMount)}function u(e,t,s){if(!1!==(0,c.Eh)(t.enabled,e)){const r="function"==typeof s?s(e):s;return"always"===r||!1!==r&&p(e,t)}return!1}function h(e,t,s,r){return(e!==t||!1===(0,c.Eh)(r.enabled,e))&&(!s.suspense||"error"!==e.state.status)&&p(e,s)}function p(e,t){return!1!==(0,c.Eh)(t.enabled,e)&&e.isStaleByTime((0,c.d2)(t.staleTime,e))}},1450:(e,t,s)=>{"use strict";s.d(t,{k:()=>n});var r=s(698),n=class{#G;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,r.gn)(this.gcTime)&&(this.#G=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(r.S$?1/0:3e5))}clearGcTimeout(){this.#G&&(clearTimeout(this.#G),this.#G=void 0)}}},4610:(e,t,s)=>{"use strict";s.d(t,{II:()=>u,v_:()=>c,wm:()=>d});var r=s(2464),n=s(5873),a=s(2415),i=s(698);function o(e){return Math.min(1e3*2**e,3e4)}function c(e){return"online"!==(e??"online")||n.t.isOnline()}var l=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function d(e){return e instanceof l}function u(e){let t,s=!1,d=0,u=!1;const h=(0,a.T)(),p=()=>r.m.isFocused()&&("always"===e.networkMode||n.t.isOnline())&&e.canRun(),m=()=>c(e.networkMode)&&e.canRun(),g=s=>{u||(u=!0,e.onSuccess?.(s),t?.(),h.resolve(s))},f=s=>{u||(u=!0,e.onError?.(s),t?.(),h.reject(s))},v=()=>new Promise((s=>{t=e=>{(u||p())&&s(e)},e.onPause?.()})).then((()=>{t=void 0,u||e.onContinue?.()})),j=()=>{if(u)return;let t;const r=0===d?e.initialPromise:void 0;try{t=r??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(g).catch((t=>{if(u)return;const r=e.retry??(i.S$?0:3),n=e.retryDelay??o,a="function"==typeof n?n(d,t):n,c=!0===r||"number"==typeof r&&d<r||"function"==typeof r&&r(d,t);!s&&c?(d++,e.onFail?.(d,t),(0,i.yy)(a).then((()=>p()?void 0:v())).then((()=>{s?f(t):j()}))):f(t)}))};return{promise:h,cancel:t=>{u||(f(new l(t)),e.abort?.())},continue:()=>(t?.(),h),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:m,start:()=>(m()?j():v().then(j),h)}}},5530:(e,t,s)=>{"use strict";s.d(t,{Q:()=>r});var r=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},2415:(e,t,s)=>{"use strict";function r(){let e,t;const s=new Promise(((s,r)=>{e=s,t=r}));function r(e){Object.assign(s,e),delete s.resolve,delete s.reject}return s.status="pending",s.catch((()=>{})),s.resolve=t=>{r({status:"fulfilled",value:t}),e(t)},s.reject=e=>{r({status:"rejected",reason:e}),t(e)},s}s.d(t,{T:()=>r})},698:(e,t,s)=>{"use strict";s.d(t,{Cp:()=>m,EN:()=>p,Eh:()=>l,F$:()=>h,MK:()=>d,S$:()=>r,ZM:()=>C,ZZ:()=>A,Zw:()=>a,d2:()=>c,f8:()=>f,gn:()=>i,hT:()=>k,j3:()=>o,lQ:()=>n,nJ:()=>u,pl:()=>b,y9:()=>w,yy:()=>y});var r="undefined"==typeof window||"Deno"in globalThis;function n(){}function a(e,t){return"function"==typeof e?e(t):e}function i(e){return"number"==typeof e&&e>=0&&e!==1/0}function o(e,t){return Math.max(e+(t||0)-Date.now(),0)}function c(e,t){return"function"==typeof e?e(t):e}function l(e,t){return"function"==typeof e?e(t):e}function d(e,t){const{type:s="all",exact:r,fetchStatus:n,predicate:a,queryKey:i,stale:o}=e;if(i)if(r){if(t.queryHash!==h(i,t.options))return!1}else if(!m(t.queryKey,i))return!1;if("all"!==s){const e=t.isActive();if("active"===s&&!e)return!1;if("inactive"===s&&e)return!1}return("boolean"!=typeof o||t.isStale()===o)&&((!n||n===t.state.fetchStatus)&&!(a&&!a(t)))}function u(e,t){const{exact:s,status:r,predicate:n,mutationKey:a}=e;if(a){if(!t.options.mutationKey)return!1;if(s){if(p(t.options.mutationKey)!==p(a))return!1}else if(!m(t.options.mutationKey,a))return!1}return(!r||t.state.status===r)&&!(n&&!n(t))}function h(e,t){return(t?.queryKeyHashFn||p)(e)}function p(e){return JSON.stringify(e,((e,t)=>j(t)?Object.keys(t).sort().reduce(((e,s)=>(e[s]=t[s],e)),{}):t))}function m(e,t){return e===t||typeof e==typeof t&&(!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&Object.keys(t).every((s=>m(e[s],t[s]))))}function g(e,t){if(e===t)return e;const s=v(e)&&v(t);if(s||j(e)&&j(t)){const r=s?e:Object.keys(e),n=r.length,a=s?t:Object.keys(t),i=a.length,o=s?[]:{};let c=0;for(let n=0;n<i;n++){const i=s?n:a[n];(!s&&r.includes(i)||s)&&void 0===e[i]&&void 0===t[i]?(o[i]=void 0,c++):(o[i]=g(e[i],t[i]),o[i]===e[i]&&void 0!==e[i]&&c++)}return n===i&&c===n?e:o}return t}function f(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}function v(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function j(e){if(!x(e))return!1;const t=e.constructor;if(void 0===t)return!0;const s=t.prototype;return!!x(s)&&(!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype)}function x(e){return"[object Object]"===Object.prototype.toString.call(e)}function y(e){return new Promise((t=>{setTimeout(t,e)}))}function b(e,t,s){return"function"==typeof s.structuralSharing?s.structuralSharing(e,t):!1!==s.structuralSharing?g(e,t):t}function w(e,t,s=0){const r=[...e,t];return s&&r.length>s?r.slice(1):r}function A(e,t,s=0){const r=[t,...e];return s&&r.length>s?r.slice(0,-1):r}var k=Symbol();function C(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==k?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))}},1462:(e,t,s)=>{"use strict";s.d(t,{E:()=>r});var r=function(){return null}},7115:(e,t,s)=>{"use strict";s.d(t,{Ht:()=>o,jE:()=>i});var r=s(1609),n=s(790),a=r.createContext(void 0),i=e=>{const t=r.useContext(a);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},o=({client:e,children:t})=>(r.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),(0,n.jsx)(a.Provider,{value:e,children:t}))},9690:(e,t,s)=>{"use strict";s.d(t,{h:()=>i});var r=s(1609);s(790);function n(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var a=r.createContext(n()),i=()=>r.useContext(a)},4804:(e,t,s)=>{"use strict";s.d(t,{$1:()=>o,LJ:()=>a,wZ:()=>i});var r=s(1609),n=s(5492),a=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},i=e=>{r.useEffect((()=>{e.clearReset()}),[e])},o=({result:e,errorResetBoundary:t,throwOnError:s,query:r,suspense:a})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(a&&void 0===e.data||(0,n.G)(s,[e.error,r]))},4796:(e,t,s)=>{"use strict";s.d(t,{w:()=>a});var r=s(1609),n=r.createContext(!1),a=()=>r.useContext(n);n.Provider},937:(e,t,s)=>{"use strict";s.d(t,{EU:()=>a,iL:()=>i,jv:()=>r,nE:()=>n});var r=e=>{const t=e.staleTime;e.suspense&&(e.staleTime="function"==typeof t?(...e)=>Math.max(t(...e),1e3):Math.max(t??1e3,1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},n=(e,t)=>e.isLoading&&e.isFetching&&!t,a=(e,t)=>e?.suspense&&t.isPending,i=(e,t,s)=>t.fetchOptimistic(e).catch((()=>{s.clearReset()}))},4611:(e,t,s)=>{"use strict";s.d(t,{t:()=>h});var r=s(1609),n=s(1795),a=s(698),i=s(7115),o=s(9690),c=s(4804),l=s(4796),d=s(937),u=s(5492);function h(e,t,s){const h=(0,i.jE)(s),p=(0,l.w)(),m=(0,o.h)(),g=h.defaultQueryOptions(e);h.getDefaultOptions().queries?._experimental_beforeQuery?.(g),g._optimisticResults=p?"isRestoring":"optimistic",(0,d.jv)(g),(0,c.LJ)(g,m),(0,c.wZ)(m);const f=!h.getQueryCache().get(g.queryHash),[v]=r.useState((()=>new t(h,g))),j=v.getOptimisticResult(g),x=!p&&!1!==e.subscribed;if(r.useSyncExternalStore(r.useCallback((e=>{const t=x?v.subscribe(n.jG.batchCalls(e)):u.l;return v.updateResult(),t}),[v,x]),(()=>v.getCurrentResult()),(()=>v.getCurrentResult())),r.useEffect((()=>{v.setOptions(g)}),[g,v]),(0,d.EU)(g,j))throw(0,d.iL)(g,v,m);if((0,c.$1)({result:j,errorResetBoundary:m,throwOnError:g.throwOnError,query:h.getQueryCache().get(g.queryHash),suspense:g.suspense}))throw j.error;if(h.getDefaultOptions().queries?._experimental_afterQuery?.(g,j),g.experimental_prefetchInRender&&!a.S$&&(0,d.nE)(j,p)){const e=f?(0,d.iL)(g,v,m):h.getQueryCache().get(g.queryHash)?.promise;e?.catch(u.l).finally((()=>{v.updateResult()}))}return g.notifyOnChangeProps?j:v.trackResult(j)}},4613:(e,t,s)=>{"use strict";s.d(t,{n:()=>c});var r=s(1609),n=s(4158),a=s(1795),i=s(7115),o=s(5492);function c(e,t){const s=(0,i.jE)(t),[c]=r.useState((()=>new n._(s,e)));r.useEffect((()=>{c.setOptions(e)}),[c,e]);const l=r.useSyncExternalStore(r.useCallback((e=>c.subscribe(a.jG.batchCalls(e))),[c]),(()=>c.getCurrentResult()),(()=>c.getCurrentResult())),d=r.useCallback(((e,t)=>{c.mutate(e,t).catch(o.l)}),[c]);if(l.error&&(0,o.G)(c.options.throwOnError,[l.error]))throw l.error;return{...l,mutate:d,mutateAsync:l.mutate}}},5104:(e,t,s)=>{"use strict";s.d(t,{I:()=>a});var r=s(3397),n=s(4611);function a(e,t){return(0,n.t)(e,r.$,t)}},5492:(e,t,s)=>{"use strict";function r(e,t){return"function"==typeof e?e(...t):!!e}function n(){}s.d(t,{G:()=>r,l:()=>n})},3022:(e,t,s)=>{"use strict";function r(e){var t,s,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(s=r(e[t]))&&(n&&(n+=" "),n+=s)}else for(s in e)e[s]&&(n&&(n+=" "),n+=s);return n}s.d(t,{A:()=>n});const n=function(){for(var e,t,s=0,n="",a=arguments.length;s<a;s++)(e=arguments[s])&&(t=r(e))&&(n&&(n+=" "),n+=t);return n}},880:(e,t,s)=>{"use strict";s.d(t,{BV:()=>be,C5:()=>je,I9:()=>st,Zp:()=>se,g:()=>re,k2:()=>at,qh:()=>xe,zy:()=>Y});var r=s(1609),n=(s(3172),"popstate");function a(e={}){return h((function(e,t){let{pathname:s="/",search:r="",hash:n=""}=u(e.location.hash.substring(1));return s.startsWith("/")||s.startsWith(".")||(s="/"+s),l("",{pathname:s,search:r,hash:n},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){let s=e.document.querySelector("base"),r="";if(s&&s.getAttribute("href")){let t=e.location.href,s=t.indexOf("#");r=-1===s?t:t.slice(0,s)}return r+"#"+("string"==typeof t?t:d(t))}),(function(e,t){o("/"===e.pathname.charAt(0),`relative pathnames are not supported in hash history.push(${JSON.stringify(t)})`)}),e)}function i(e,t){if(!1===e||null==e)throw new Error(t)}function o(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(e){}}}function c(e,t){return{usr:e.state,key:e.key,idx:t}}function l(e,t,s=null,r){return{pathname:"string"==typeof e?e:e.pathname,search:"",hash:"",..."string"==typeof t?u(t):t,state:s,key:t&&t.key||r||Math.random().toString(36).substring(2,10)}}function d({pathname:e="/",search:t="",hash:s=""}){return t&&"?"!==t&&(e+="?"===t.charAt(0)?t:"?"+t),s&&"#"!==s&&(e+="#"===s.charAt(0)?s:"#"+s),e}function u(e){let t={};if(e){let s=e.indexOf("#");s>=0&&(t.hash=e.substring(s),e=e.substring(0,s));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function h(e,t,s,r={}){let{window:a=document.defaultView,v5Compat:i=!1}=r,o=a.history,d="POP",u=null,h=m();function m(){return(o.state||{idx:null}).idx}function g(){d="POP";let e=m(),t=null==e?null:e-h;h=e,u&&u({action:d,location:v.location,delta:t})}function f(e){return p(e)}null==h&&(h=0,o.replaceState({...o.state,idx:h},""));let v={get action(){return d},get location(){return e(a,o)},listen(e){if(u)throw new Error("A history only accepts one active listener");return a.addEventListener(n,g),u=e,()=>{a.removeEventListener(n,g),u=null}},createHref:e=>t(a,e),createURL:f,encodeLocation(e){let t=f(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){d="PUSH";let r=l(v.location,e,t);s&&s(r,e),h=m()+1;let n=c(r,h),p=v.createHref(r);try{o.pushState(n,"",p)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;a.location.assign(p)}i&&u&&u({action:d,location:v.location,delta:1})},replace:function(e,t){d="REPLACE";let r=l(v.location,e,t);s&&s(r,e),h=m();let n=c(r,h),a=v.createHref(r);o.replaceState(n,"",a),i&&u&&u({action:d,location:v.location,delta:0})},go:e=>o.go(e)};return v}function p(e,t=!1){let s="http://localhost";"undefined"!=typeof window&&(s="null"!==window.location.origin?window.location.origin:window.location.href),i(s,"No window.location.(origin|href) available to create URL");let r="string"==typeof e?e:d(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=s+r),new URL(r,s)}new WeakMap;function m(e,t,s="/"){return g(e,t,s,!1)}function g(e,t,s,r){let n=L(("string"==typeof t?u(t):t).pathname||"/",s);if(null==n)return null;let a=f(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let s=e.length===t.length&&e.slice(0,-1).every(((e,s)=>e===t[s]));return s?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(a);let i=null;for(let e=0;null==i&&e<a.length;++e){let t=M(n);i=_(a[e],t,r)}return i}function f(e,t=[],s=[],r=""){let n=(e,n,a)=>{let o={relativePath:void 0===a?e.path||"":a,caseSensitive:!0===e.caseSensitive,childrenIndex:n,route:e};o.relativePath.startsWith("/")&&(i(o.relativePath.startsWith(r),`Absolute route path "${o.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),o.relativePath=o.relativePath.slice(r.length));let c=F([r,o.relativePath]),l=s.concat(o);e.children&&e.children.length>0&&(i(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${c}".`),f(e.children,t,l,c)),(null!=e.path||e.index)&&t.push({path:c,score:C(c,e.index),routesMeta:l})};return e.forEach(((e,t)=>{if(""!==e.path&&e.path?.includes("?"))for(let s of v(e.path))n(e,t,s);else n(e,t)})),t}function v(e){let t=e.split("/");if(0===t.length)return[];let[s,...r]=t,n=s.endsWith("?"),a=s.replace(/\?$/,"");if(0===r.length)return n?[a,""]:[a];let i=v(r.join("/")),o=[];return o.push(...i.map((e=>""===e?a:[a,e].join("/")))),n&&o.push(...i),o.map((t=>e.startsWith("/")&&""===t?"/":t))}var j=/^:[\w-]+$/,x=3,y=2,b=1,w=10,A=-2,k=e=>"*"===e;function C(e,t){let s=e.split("/"),r=s.length;return s.some(k)&&(r+=A),t&&(r+=y),s.filter((e=>!k(e))).reduce(((e,t)=>e+(j.test(t)?x:""===t?b:w)),r)}function _(e,t,s=!1){let{routesMeta:r}=e,n={},a="/",i=[];for(let e=0;e<r.length;++e){let o=r[e],c=e===r.length-1,l="/"===a?t:t.slice(a.length)||"/",d=S({path:o.relativePath,caseSensitive:o.caseSensitive,end:c},l),u=o.route;if(!d&&c&&s&&!r[r.length-1].route.index&&(d=S({path:o.relativePath,caseSensitive:o.caseSensitive,end:!1},l)),!d)return null;Object.assign(n,d.params),i.push({params:n,pathname:F([a,d.pathname]),pathnameBase:O(F([a,d.pathnameBase])),route:u}),"/"!==d.pathnameBase&&(a=F([a,d.pathnameBase]))}return i}function S(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[s,r]=N(e.path,e.caseSensitive,e.end),n=t.match(s);if(!n)return null;let a=n[0],i=a.replace(/(.)\/+$/,"$1"),o=n.slice(1);return{params:r.reduce(((e,{paramName:t,isOptional:s},r)=>{if("*"===t){let e=o[r]||"";i=a.slice(0,a.length-e.length).replace(/(.)\/+$/,"$1")}const n=o[r];return e[t]=s&&!n?void 0:(n||"").replace(/%2F/g,"/"),e}),{}),pathname:a,pathnameBase:i,pattern:e}}function N(e,t=!1,s=!0){o("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,s)=>(r.push({paramName:t,isOptional:null!=s}),s?"/?([^\\/]+)?":"/([^\\/]+)")));return e.endsWith("*")?(r.push({paramName:"*"}),n+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?n+="\\/*$":""!==e&&"/"!==e&&(n+="(?:(?=\\/|$))"),[new RegExp(n,t?void 0:"i"),r]}function M(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return o(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function L(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let s=t.endsWith("/")?t.length-1:t.length,r=e.charAt(s);return r&&"/"!==r?null:e.slice(s)||"/"}function E(e,t,s,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${s}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function P(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function R(e){let t=P(e);return t.map(((e,s)=>s===t.length-1?e.pathname:e.pathnameBase))}function z(e,t,s,r=!1){let n;"string"==typeof e?n=u(e):(n={...e},i(!n.pathname||!n.pathname.includes("?"),E("?","pathname","search",n)),i(!n.pathname||!n.pathname.includes("#"),E("#","pathname","hash",n)),i(!n.search||!n.search.includes("#"),E("#","search","hash",n)));let a,o=""===e||""===n.pathname,c=o?"/":n.pathname;if(null==c)a=s;else{let e=t.length-1;if(!r&&c.startsWith("..")){let t=c.split("/");for(;".."===t[0];)t.shift(),e-=1;n.pathname=t.join("/")}a=e>=0?t[e]:"/"}let l=function(e,t="/"){let{pathname:s,search:r="",hash:n=""}="string"==typeof e?u(e):e,a=s?s.startsWith("/")?s:function(e,t){let s=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?s.length>1&&s.pop():"."!==e&&s.push(e)})),s.length>1?s.join("/"):"/"}(s,t):t;return{pathname:a,search:I(r),hash:D(n)}}(n,a),d=c&&"/"!==c&&c.endsWith("/"),h=(o||"."===c)&&s.endsWith("/");return l.pathname.endsWith("/")||!d&&!h||(l.pathname+="/"),l}var F=e=>e.join("/").replace(/\/\/+/g,"/"),O=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),I=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",D=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function V(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}var H=["POST","PUT","PATCH","DELETE"],T=(new Set(H),["GET",...H]);new Set(T),Symbol("ResetLoaderData");var B=r.createContext(null);B.displayName="DataRouter";var $=r.createContext(null);$.displayName="DataRouterState";var q=r.createContext({isTransitioning:!1});q.displayName="ViewTransition";var U=r.createContext(new Map);U.displayName="Fetchers";var Q=r.createContext(null);Q.displayName="Await";var J=r.createContext(null);J.displayName="Navigation";var W=r.createContext(null);W.displayName="Location";var Z=r.createContext({outlet:null,matches:[],isDataRoute:!1});Z.displayName="Route";var G=r.createContext(null);G.displayName="RouteError";var K=!0;function X(){return null!=r.useContext(W)}function Y(){return i(X(),"useLocation() may be used only in the context of a <Router> component."),r.useContext(W).location}var ee="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function te(e){r.useContext(J).static||r.useLayoutEffect(e)}function se(){let{isDataRoute:e}=r.useContext(Z);return e?function(){let{router:e}=he("useNavigate"),t=me("useNavigate"),s=r.useRef(!1);return te((()=>{s.current=!0})),r.useCallback((async(r,n={})=>{o(s.current,ee),s.current&&("number"==typeof r?e.navigate(r):await e.navigate(r,{fromRouteId:t,...n}))}),[e,t])}():function(){i(X(),"useNavigate() may be used only in the context of a <Router> component.");let e=r.useContext(B),{basename:t,navigator:s}=r.useContext(J),{matches:n}=r.useContext(Z),{pathname:a}=Y(),c=JSON.stringify(R(n)),l=r.useRef(!1);return te((()=>{l.current=!0})),r.useCallback(((r,n={})=>{if(o(l.current,ee),!l.current)return;if("number"==typeof r)return void s.go(r);let i=z(r,JSON.parse(c),a,"path"===n.relative);null==e&&"/"!==t&&(i.pathname="/"===i.pathname?t:F([t,i.pathname])),(n.replace?s.replace:s.push)(i,n.state,n)}),[t,s,c,a,e])}()}r.createContext(null);function re(){let{matches:e}=r.useContext(Z),t=e[e.length-1];return t?t.params:{}}function ne(e,{relative:t}={}){let{matches:s}=r.useContext(Z),{pathname:n}=Y(),a=JSON.stringify(R(s));return r.useMemo((()=>z(e,JSON.parse(a),n,"path"===t)),[e,a,n,t])}function ae(e,t,s,n){i(X(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a}=r.useContext(J),{matches:c}=r.useContext(Z),l=c[c.length-1],d=l?l.params:{},h=l?l.pathname:"/",p=l?l.pathnameBase:"/",g=l&&l.route;if(K){let e=g&&g.path||"";ve(h,!g||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${h}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let f,v=Y();if(t){let e="string"==typeof t?u(t):t;i("/"===p||e.pathname?.startsWith(p),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${p}" but pathname "${e.pathname}" was given in the \`location\` prop.`),f=e}else f=v;let j=f.pathname||"/",x=j;if("/"!==p){let e=p.replace(/^\//,"").split("/");x="/"+j.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=m(e,{pathname:x});K&&(o(g||null!=y,`No routes matched location "${f.pathname}${f.search}${f.hash}" `),o(null==y||void 0!==y[y.length-1].route.element||void 0!==y[y.length-1].route.Component||void 0!==y[y.length-1].route.lazy,`Matched leaf route at location "${f.pathname}${f.search}${f.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`));let b=de(y&&y.map((e=>Object.assign({},e,{params:Object.assign({},d,e.params),pathname:F([p,a.encodeLocation?a.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?p:F([p,a.encodeLocation?a.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),c,s,n);return t&&b?r.createElement(W.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...f},navigationType:"POP"}},b):b}function ie(){let e=ge(),t=V(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),s=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:n},i={padding:"2px 4px",backgroundColor:n},o=null;return K&&(console.error("Error handled by React Router default ErrorBoundary:",e),o=r.createElement(r.Fragment,null,r.createElement("p",null,"💿 Hey developer 👋"),r.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",r.createElement("code",{style:i},"ErrorBoundary")," or"," ",r.createElement("code",{style:i},"errorElement")," prop on your route."))),r.createElement(r.Fragment,null,r.createElement("h2",null,"Unexpected Application Error!"),r.createElement("h3",{style:{fontStyle:"italic"}},t),s?r.createElement("pre",{style:a},s):null,o)}var oe=r.createElement(ie,null),ce=class extends r.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?r.createElement(Z.Provider,{value:this.props.routeContext},r.createElement(G.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function le({routeContext:e,match:t,children:s}){let n=r.useContext(B);return n&&n.static&&n.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=t.route.id),r.createElement(Z.Provider,{value:e},s)}function de(e,t=[],s=null,n=null){if(null==e){if(!s)return null;if(s.errors)e=s.matches;else{if(0!==t.length||s.initialized||!(s.matches.length>0))return null;e=s.matches}}let a=e,o=s?.errors;if(null!=o){let e=a.findIndex((e=>e.route.id&&void 0!==o?.[e.route.id]));i(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),a=a.slice(0,Math.min(a.length,e+1))}let c=!1,l=-1;if(s)for(let e=0;e<a.length;e++){let t=a[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(l=e),t.route.id){let{loaderData:e,errors:r}=s,n=t.route.loader&&!e.hasOwnProperty(t.route.id)&&(!r||void 0===r[t.route.id]);if(t.route.lazy||n){c=!0,a=l>=0?a.slice(0,l+1):[a[0]];break}}}return a.reduceRight(((e,n,i)=>{let d,u=!1,h=null,p=null;s&&(d=o&&n.route.id?o[n.route.id]:void 0,h=n.route.errorElement||oe,c&&(l<0&&0===i?(ve("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),u=!0,p=null):l===i&&(u=!0,p=n.route.hydrateFallbackElement||null)));let m=t.concat(a.slice(0,i+1)),g=()=>{let t;return t=d?h:u?p:n.route.Component?r.createElement(n.route.Component,null):n.route.element?n.route.element:e,r.createElement(le,{match:n,routeContext:{outlet:e,matches:m,isDataRoute:null!=s},children:t})};return s&&(n.route.ErrorBoundary||n.route.errorElement||0===i)?r.createElement(ce,{location:s.location,revalidation:s.revalidation,component:h,error:d,children:g(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):g()}),null)}function ue(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function he(e){let t=r.useContext(B);return i(t,ue(e)),t}function pe(e){let t=r.useContext($);return i(t,ue(e)),t}function me(e){let t=function(e){let t=r.useContext(Z);return i(t,ue(e)),t}(e),s=t.matches[t.matches.length-1];return i(s.route.id,`${e} can only be used on routes that contain a unique "id"`),s.route.id}function ge(){let e=r.useContext(G),t=pe("useRouteError"),s=me("useRouteError");return void 0!==e?e:t.errors?.[s]}var fe={};function ve(e,t,s){t||fe[e]||(fe[e]=!0,o(!1,s))}r.memo((function({routes:e,future:t,state:s}){return ae(e,void 0,s,t)}));function je({to:e,replace:t,state:s,relative:n}){i(X(),"<Navigate> may be used only in the context of a <Router> component.");let{static:a}=r.useContext(J);o(!a,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:c}=r.useContext(Z),{pathname:l}=Y(),d=se(),u=z(e,R(c),l,"path"===n),h=JSON.stringify(u);return r.useEffect((()=>{d(JSON.parse(h),{replace:t,state:s,relative:n})}),[d,h,n,t,s]),null}function xe(e){i(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function ye({basename:e="/",children:t=null,location:s,navigationType:n="POP",navigator:a,static:c=!1}){i(!X(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=e.replace(/^\/*/,"/"),d=r.useMemo((()=>({basename:l,navigator:a,static:c,future:{}})),[l,a,c]);"string"==typeof s&&(s=u(s));let{pathname:h="/",search:p="",hash:m="",state:g=null,key:f="default"}=s,v=r.useMemo((()=>{let e=L(h,l);return null==e?null:{location:{pathname:e,search:p,hash:m,state:g,key:f},navigationType:n}}),[l,h,p,m,g,f,n]);return o(null!=v,`<Router basename="${l}"> is not able to match the URL "${h}${p}${m}" because it does not start with the basename, so the <Router> won't render anything.`),null==v?null:r.createElement(J.Provider,{value:d},r.createElement(W.Provider,{children:t,value:v}))}function be({children:e,location:t}){return ae(we(e),t)}r.Component;function we(e,t=[]){let s=[];return r.Children.forEach(e,((e,n)=>{if(!r.isValidElement(e))return;let a=[...t,n];if(e.type===r.Fragment)return void s.push.apply(s,we(e.props.children,a));i(e.type===xe,`[${"string"==typeof e.type?e.type:e.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),i(!e.props.index||!e.props.children,"An index route cannot have child routes.");let o={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(o.children=we(e.props.children,a)),s.push(o)})),s}var Ae="get",ke="application/x-www-form-urlencoded";function Ce(e){return null!=e&&"string"==typeof e.tagName}var _e=null;var Se=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Ne(e){return null==e||Se.has(e)?e:(o(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${ke}"`),null)}function Me(e,t){let s,r,n,a,i;if(Ce(o=e)&&"form"===o.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?L(i,t):null,s=e.getAttribute("method")||Ae,n=Ne(e.getAttribute("enctype"))||ke,a=new FormData(e)}else if(function(e){return Ce(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Ce(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let o=e.getAttribute("formaction")||i.getAttribute("action");if(r=o?L(o,t):null,s=e.getAttribute("formmethod")||i.getAttribute("method")||Ae,n=Ne(e.getAttribute("formenctype"))||Ne(i.getAttribute("enctype"))||ke,a=new FormData(i,e),!function(){if(null===_e)try{new FormData(document.createElement("form"),0),_e=!1}catch(e){_e=!0}return _e}()){let{name:t,type:s,value:r}=e;if("image"===s){let e=t?`${t}.`:"";a.append(`${e}x`,"0"),a.append(`${e}y`,"0")}else t&&a.append(t,r)}}else{if(Ce(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');s=Ae,r=null,n=ke,i=e}var o;return a&&"text/plain"===n&&(i=a,a=void 0),{action:r,method:s.toLowerCase(),encType:n,formData:a,body:i}}function Le(e,t){if(!1===e||null==e)throw new Error(t)}async function Ee(e,t){if(e.id in t)return t[e.id];try{let s=await import(e.module);return t[e.id]=s,s}catch(t){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(t),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}function Pe(e){return null!=e&&"string"==typeof e.page}function Re(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"==typeof e.imageSrcSet&&"string"==typeof e.imageSizes:"string"==typeof e.rel&&"string"==typeof e.href)}function ze(e,t,s,r,n,a){let i=(e,t)=>!s[t]||e.route.id!==s[t].route.id,o=(e,t)=>s[t].pathname!==e.pathname||s[t].route.path?.endsWith("*")&&s[t].params["*"]!==e.params["*"];return"assets"===a?t.filter(((e,t)=>i(e,t)||o(e,t))):"data"===a?t.filter(((t,a)=>{let c=r.routes[t.route.id];if(!c||!c.hasLoader)return!1;if(i(t,a)||o(t,a))return!0;if(t.route.shouldRevalidate){let r=t.route.shouldRevalidate({currentUrl:new URL(n.pathname+n.search+n.hash,window.origin),currentParams:s[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"==typeof r)return r}return!0})):[]}function Fe(e,t,{includeHydrateFallback:s}={}){return r=e.map((e=>{let r=t.routes[e.route.id];if(!r)return[];let n=[r.module];return r.clientActionModule&&(n=n.concat(r.clientActionModule)),r.clientLoaderModule&&(n=n.concat(r.clientLoaderModule)),s&&r.hydrateFallbackModule&&(n=n.concat(r.hydrateFallbackModule)),r.imports&&(n=n.concat(r.imports)),n})).flat(1),[...new Set(r)];var r}function Oe(e,t){let s=new Set,r=new Set(t);return e.reduce(((e,n)=>{if(t&&!Pe(n)&&"script"===n.as&&n.href&&r.has(n.href))return e;let a=JSON.stringify(function(e){let t={},s=Object.keys(e).sort();for(let r of s)t[r]=e[r];return t}(n));return s.has(a)||(s.add(a),e.push({key:a,link:n})),e}),[])}function Ie(e){return{__html:e}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");"undefined"!=typeof window?window:"undefined"!=typeof globalThis&&globalThis;Symbol("SingleFetchRedirect");var De=new Set([100,101,204,205]);function Ve(e,t){let s="string"==typeof e?new URL(e,"undefined"==typeof window?"server://singlefetch/":window.location.origin):e;return"/"===s.pathname?s.pathname="_root.data":t&&"/"===L(s.pathname,t)?s.pathname=`${t.replace(/\/$/,"")}/_root.data`:s.pathname=`${s.pathname.replace(/\/$/,"")}.data`,s}r.Component;function He({error:e,isOutsideRemixApp:t}){console.error(e);let s,n=r.createElement("script",{dangerouslySetInnerHTML:{__html:'\n        console.log(\n          "💿 Hey developer 👋. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."\n        );\n      '}});if(V(e))return r.createElement(Te,{title:"Unhandled Thrown Response!"},r.createElement("h1",{style:{fontSize:"24px"}},e.status," ",e.statusText),K?n:null);if(e instanceof Error)s=e;else{let t=null==e?"Unknown Error":"object"==typeof e&&"toString"in e?e.toString():JSON.stringify(e);s=new Error(t)}return r.createElement(Te,{title:"Application Error!",isOutsideRemixApp:t},r.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),r.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},s.stack),n)}function Te({title:e,renderScripts:t,isOutsideRemixApp:s,children:n}){let{routeModules:a}=Qe();return a.root?.Layout&&!s?n:r.createElement("html",{lang:"en"},r.createElement("head",null,r.createElement("meta",{charSet:"utf-8"}),r.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),r.createElement("title",null,e)),r.createElement("body",null,r.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},n,t?r.createElement(Ye,null):null)))}function Be(e,t){return"lazy"===e.mode&&!0===t}function $e(){let e=r.useContext(B);return Le(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function qe(){let e=r.useContext($);return Le(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Ue=r.createContext(void 0);function Qe(){let e=r.useContext(Ue);return Le(e,"You must render this element inside a <HydratedRouter> element"),e}function Je(e,t){return s=>{e&&e(s),s.defaultPrevented||t(s)}}function We(e,t,s){if(s&&!Xe)return[e[0]];if(t){let s=e.findIndex((e=>void 0!==t[e.route.id]));return e.slice(0,s+1)}return e}function Ze({page:e,...t}){let{router:s}=$e(),n=r.useMemo((()=>m(s.routes,e,s.basename)),[s.routes,e,s.basename]);return n?r.createElement(Ke,{page:e,matches:n,...t}):null}function Ge(e){let{manifest:t,routeModules:s}=Qe(),[n,a]=r.useState([]);return r.useEffect((()=>{let r=!1;return async function(e,t,s){return Oe((await Promise.all(e.map((async e=>{let r=t.routes[e.route.id];if(r){let e=await Ee(r,s);return e.links?e.links():[]}return[]})))).flat(1).filter(Re).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"})))}(e,t,s).then((e=>{r||a(e)})),()=>{r=!0}}),[e,t,s]),n}function Ke({page:e,matches:t,...s}){let n=Y(),{manifest:a,routeModules:i}=Qe(),{basename:o}=$e(),{loaderData:c,matches:l}=qe(),d=r.useMemo((()=>ze(e,t,l,a,n,"data")),[e,t,l,a,n]),u=r.useMemo((()=>ze(e,t,l,a,n,"assets")),[e,t,l,a,n]),h=r.useMemo((()=>{if(e===n.pathname+n.search+n.hash)return[];let s=new Set,r=!1;if(t.forEach((e=>{let t=a.routes[e.route.id];t&&t.hasLoader&&(!d.some((t=>t.route.id===e.route.id))&&e.route.id in c&&i[e.route.id]?.shouldRevalidate||t.hasClientLoader?r=!0:s.add(e.route.id))})),0===s.size)return[];let l=Ve(e,o);return r&&s.size>0&&l.searchParams.set("_routes",t.filter((e=>s.has(e.route.id))).map((e=>e.route.id)).join(",")),[l.pathname+l.search]}),[o,c,n,a,d,t,e,i]),p=r.useMemo((()=>Fe(u,a)),[u,a]),m=Ge(u);return r.createElement(r.Fragment,null,h.map((e=>r.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...s}))),p.map((e=>r.createElement("link",{key:e,rel:"modulepreload",href:e,...s}))),m.map((({key:e,link:t})=>r.createElement("link",{key:e,...t}))))}Ue.displayName="FrameworkContext";var Xe=!1;function Ye(e){let{manifest:t,serverHandoffString:s,isSpaMode:n,renderMeta:a,routeDiscovery:i,ssr:o}=Qe(),{router:c,static:l,staticContext:d}=$e(),{matches:u}=qe(),h=Be(i,o);a&&(a.didRenderScripts=!0);let p=We(u,null,n);r.useEffect((()=>{Xe=!0}),[]);let g=r.useMemo((()=>{let n=d?`window.__reactRouterContext = ${s};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());`:" ",a=l?`${t.hmr?.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${h?"":`import ${JSON.stringify(t.url)}`};\n${p.map(((e,s)=>{let r=`route${s}`,n=t.routes[e.route.id];Le(n,`Route ${e.route.id} not found in manifest`);let{clientActionModule:a,clientLoaderModule:i,clientMiddlewareModule:o,hydrateFallbackModule:c,module:l}=n,d=[...a?[{module:a,varName:`${r}_clientAction`}]:[],...i?[{module:i,varName:`${r}_clientLoader`}]:[],...o?[{module:o,varName:`${r}_clientMiddleware`}]:[],...c?[{module:c,varName:`${r}_HydrateFallback`}]:[],{module:l,varName:`${r}_main`}];return 1===d.length?`import * as ${r} from ${JSON.stringify(l)};`:[d.map((e=>`import * as ${e.varName} from "${e.module}";`)).join("\n"),`const ${r} = {${d.map((e=>`...${e.varName}`)).join(",")}};`].join("\n")})).join("\n")}\n  ${h?`window.__reactRouterManifest = ${JSON.stringify(function({sri:e,...t},s){let r=new Set(s.state.matches.map((e=>e.route.id))),n=s.state.location.pathname.split("/").filter(Boolean),a=["/"];for(n.pop();n.length>0;)a.push(`/${n.join("/")}`),n.pop();a.forEach((e=>{let t=m(s.routes,e,s.basename);t&&t.forEach((e=>r.add(e.route.id)))}));let i=[...r].reduce(((e,s)=>Object.assign(e,{[s]:t.routes[s]})),{});return{...t,routes:i,sri:!!e||void 0}}(t,c),null,2)};`:""}\n  window.__reactRouterRouteModules = {${p.map(((e,t)=>`${JSON.stringify(e.route.id)}:route${t}`)).join(",")}};\n\nimport(${JSON.stringify(t.entry.module)});`:" ";return r.createElement(r.Fragment,null,r.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ie(n),type:void 0}),r.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ie(a),type:"module",async:!0}))}),[]),f=Xe?[]:(v=t.entry.imports.concat(Fe(p,t,{includeHydrateFallback:!0})),[...new Set(v)]);var v;let j="object"==typeof t.sri?t.sri:{};return Xe?null:r.createElement(r.Fragment,null,"object"==typeof t.sri?r.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:j})}}):null,h?null:r.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:j[t.url],suppressHydrationWarning:!0}),r.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:j[t.entry.module],suppressHydrationWarning:!0}),f.map((t=>r.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin,integrity:j[t],suppressHydrationWarning:!0}))),g)}function et(...e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}var tt="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;try{tt&&(window.__reactRouterVersion="7.6.2")}catch(e){}function st({basename:e,children:t,window:s}){let n=r.useRef();null==n.current&&(n.current=a({window:s,v5Compat:!0}));let i=n.current,[o,c]=r.useState({action:i.action,location:i.location}),l=r.useCallback((e=>{r.startTransition((()=>c(e)))}),[c]);return r.useLayoutEffect((()=>i.listen(l)),[i,l]),r.createElement(ye,{basename:e,children:t,location:o.location,navigationType:o.action,navigator:i})}var rt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,nt=r.forwardRef((function({onClick:e,discover:t="render",prefetch:s="none",relative:n,reloadDocument:a,replace:c,state:l,target:u,to:h,preventScrollReset:p,viewTransition:m,...g},f){let v,{basename:j}=r.useContext(J),x="string"==typeof h&&rt.test(h),y=!1;if("string"==typeof h&&x&&(v=h,tt))try{let e=new URL(window.location.href),t=h.startsWith("//")?new URL(e.protocol+h):new URL(h),s=L(t.pathname,j);t.origin===e.origin&&null!=s?h=s+t.search+t.hash:y=!0}catch(e){o(!1,`<Link to="${h}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let b=function(e,{relative:t}={}){i(X(),"useHref() may be used only in the context of a <Router> component.");let{basename:s,navigator:n}=r.useContext(J),{hash:a,pathname:o,search:c}=ne(e,{relative:t}),l=o;return"/"!==s&&(l="/"===o?s:F([s,o])),n.createHref({pathname:l,search:c,hash:a})}(h,{relative:n}),[w,A,k]=function(e,t){let s=r.useContext(Ue),[n,a]=r.useState(!1),[i,o]=r.useState(!1),{onFocus:c,onBlur:l,onMouseEnter:d,onMouseLeave:u,onTouchStart:h}=t,p=r.useRef(null);r.useEffect((()=>{if("render"===e&&o(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{o(e.isIntersecting)}))}),{threshold:.5});return p.current&&e.observe(p.current),()=>{e.disconnect()}}}),[e]),r.useEffect((()=>{if(n){let e=setTimeout((()=>{o(!0)}),100);return()=>{clearTimeout(e)}}}),[n]);let m=()=>{a(!0)},g=()=>{a(!1),o(!1)};return s?"intent"!==e?[i,p,{}]:[i,p,{onFocus:Je(c,m),onBlur:Je(l,g),onMouseEnter:Je(d,m),onMouseLeave:Je(u,g),onTouchStart:Je(h,m)}]:[!1,p,{}]}(s,g),C=function(e,{target:t,replace:s,state:n,preventScrollReset:a,relative:i,viewTransition:o}={}){let c=se(),l=Y(),u=ne(e,{relative:i});return r.useCallback((r=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(r,t)){r.preventDefault();let t=void 0!==s?s:d(l)===d(u);c(e,{replace:t,state:n,preventScrollReset:a,relative:i,viewTransition:o})}}),[l,c,u,s,n,t,e,a,i,o])}(h,{replace:c,state:l,target:u,preventScrollReset:p,relative:n,viewTransition:m});let _=r.createElement("a",{...g,...k,href:v||b,onClick:y||a?e:function(t){e&&e(t),t.defaultPrevented||C(t)},ref:et(f,A),target:u,"data-discover":x||"render"!==t?void 0:"true"});return w&&!x?r.createElement(r.Fragment,null,_,r.createElement(Ze,{page:b})):_}));nt.displayName="Link";var at=r.forwardRef((function({"aria-current":e="page",caseSensitive:t=!1,className:s="",end:n=!1,style:a,to:o,viewTransition:c,children:l,...d},u){let h=ne(o,{relative:d.relative}),p=Y(),m=r.useContext($),{navigator:g,basename:f}=r.useContext(J),v=null!=m&&function(e,t={}){let s=r.useContext(q);i(null!=s,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:n}=ct("useViewTransitionState"),a=ne(e,{relative:t.relative});if(!s.isTransitioning)return!1;let o=L(s.currentLocation.pathname,n)||s.currentLocation.pathname,c=L(s.nextLocation.pathname,n)||s.nextLocation.pathname;return null!=S(a.pathname,c)||null!=S(a.pathname,o)}(h)&&!0===c,j=g.encodeLocation?g.encodeLocation(h).pathname:h.pathname,x=p.pathname,y=m&&m.navigation&&m.navigation.location?m.navigation.location.pathname:null;t||(x=x.toLowerCase(),y=y?y.toLowerCase():null,j=j.toLowerCase()),y&&f&&(y=L(y,f)||y);const b="/"!==j&&j.endsWith("/")?j.length-1:j.length;let w,A=x===j||!n&&x.startsWith(j)&&"/"===x.charAt(b),k=null!=y&&(y===j||!n&&y.startsWith(j)&&"/"===y.charAt(j.length)),C={isActive:A,isPending:k,isTransitioning:v},_=A?e:void 0;w="function"==typeof s?s(C):[s,A?"active":null,k?"pending":null,v?"transitioning":null].filter(Boolean).join(" ");let N="function"==typeof a?a(C):a;return r.createElement(nt,{...d,"aria-current":_,className:w,ref:u,style:N,to:o,viewTransition:c},"function"==typeof l?l(C):l)}));at.displayName="NavLink";var it=r.forwardRef((({discover:e="render",fetcherKey:t,navigate:s,reloadDocument:n,replace:a,state:o,method:c=Ae,action:l,onSubmit:u,relative:h,preventScrollReset:p,viewTransition:m,...g},f)=>{let v=ut(),j=function(e,{relative:t}={}){let{basename:s}=r.useContext(J),n=r.useContext(Z);i(n,"useFormAction must be used inside a RouteContext");let[a]=n.matches.slice(-1),o={...ne(e||".",{relative:t})},c=Y();if(null==e){o.search=c.search;let e=new URLSearchParams(o.search),t=e.getAll("index");if(t.some((e=>""===e))){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let s=e.toString();o.search=s?`?${s}`:""}}e&&"."!==e||!a.route.index||(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index");"/"!==s&&(o.pathname="/"===o.pathname?s:F([s,o.pathname]));return d(o)}(l,{relative:h}),x="get"===c.toLowerCase()?"get":"post",y="string"==typeof l&&rt.test(l);return r.createElement("form",{ref:f,method:x,action:j,onSubmit:n?u:e=>{if(u&&u(e),e.defaultPrevented)return;e.preventDefault();let r=e.nativeEvent.submitter,n=r?.getAttribute("formmethod")||c;v(r||e.currentTarget,{fetcherKey:t,method:n,navigate:s,replace:a,state:o,relative:h,preventScrollReset:p,viewTransition:m})},...g,"data-discover":y||"render"!==e?void 0:"true"})}));function ot(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ct(e){let t=r.useContext(B);return i(t,ot(e)),t}it.displayName="Form";var lt=0,dt=()=>`__${String(++lt)}__`;function ut(){let{router:e}=ct("useSubmit"),{basename:t}=r.useContext(J),s=me("useRouteId");return r.useCallback((async(r,n={})=>{let{action:a,method:i,encType:o,formData:c,body:l}=Me(r,t);if(!1===n.navigate){let t=n.fetcherKey||dt();await e.fetch(t,s,n.action||a,{preventScrollReset:n.preventScrollReset,formData:c,body:l,formMethod:n.method||i,formEncType:n.encType||o,flushSync:n.flushSync})}else await e.navigate(n.action||a,{preventScrollReset:n.preventScrollReset,formData:c,body:l,formMethod:n.method||i,formEncType:n.encType||o,replace:n.replace,state:n.state,fromRouteId:s,flushSync:n.flushSync,viewTransition:n.viewTransition})}),[e,t,s])}},5448:(e,t,s)=>{"use strict";s.d(t,{Z6:()=>r.A});s(1636),s(3796);var r=s(7082);s(4081)}},t={};function s(r){var n=t[r];if(void 0!==n)return n.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,s),a.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var r in t)s.o(t,r)&&!s.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=s(723),t=s(116),r=s(7115),n=s(1462),a=s(6087),i=s(1609),o=s(880),c=s(4440),l=s(5409),d=s(1009),u=s(4537),h=s(1936),p=s(5925),m=s(1671),g=s(5148),f=s(5717),v=s(9580),j=s(2970),x=(s(6281),s(790));const y=new t.E({defaultOptions:{queries:{staleTime:1/0}}});function b(){const e=(0,o.zy)();return(0,i.useEffect)((()=>window.scrollTo(0,0)),[e]),null}!function(){const t=document.getElementById("jetpack-protect-root");if(null===t)return;const s=(0,x.jsxs)(r.Ht,{client:y,children:[(0,x.jsx)(e.Ay,{children:(0,x.jsx)(u.m,{children:(0,x.jsx)(d.Z,{children:(0,x.jsx)(p.sT,{children:(0,x.jsxs)(h.Ll,{children:[(0,x.jsxs)(o.I9,{children:[(0,x.jsx)(b,{}),(0,x.jsxs)(o.BV,{children:[(0,x.jsx)(o.qh,{path:"/settings",element:(0,x.jsx)(v.A,{})}),(0,x.jsx)(o.qh,{path:"/setup",element:(0,x.jsx)(j.A,{})}),(0,x.jsx)(o.qh,{path:"/scan",element:(0,x.jsx)(g.A,{})}),(0,x.jsx)(o.qh,{path:"/scan/history",element:(0,x.jsx)(l.A,{children:(0,x.jsx)(f.A,{})})}),(0,x.jsx)(o.qh,{path:"/scan/history/:filter",element:(0,x.jsx)(l.A,{children:(0,x.jsx)(f.A,{})})}),(0,x.jsx)(o.qh,{path:"/firewall",element:(0,x.jsx)(m.A,{})}),(0,x.jsx)(o.qh,{path:"*",element:(0,x.jsx)(o.C5,{to:"/scan",replace:!0})})]})]}),(0,x.jsx)(c.A,{})]})})})})}),(0,x.jsx)(n.E,{initialIsOpen:!1})]});a.createRoot(t).render(s)}()})()})();