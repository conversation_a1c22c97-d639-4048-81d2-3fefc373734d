# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.9.0] - 2025-07-21
### Added
- Forms: Include multistep form in Jetpack and WordPress.com plans. [#44309]

## [0.8.0] - 2025-05-05
### Added
- Forms: Add feature/block field-file support to Personal and Complete plans. [#43177]

## [0.7.1] - 2025-04-28
### Changed
- Internal updates.

## [0.7.0] - 2025-03-31
### Added
- Add `ai-seo-enhancer` as a feature supported by the Business plan. [#42731]

## [0.6.1] - 2025-03-21
### Changed
- Internal updates.

## [0.6.0] - 2025-03-18
### Changed
- Add `Plans::get_plan_short_name()` for WordPress.com environments. [#42485]

## [0.5.6] - 2025-03-17
### Changed
- Internal updates.

## [0.5.5] - 2025-03-12
### Changed
- Internal updates.

## [0.5.4] - 2025-03-05
### Changed
- Internal updates.

## [0.5.3] - 2025-02-24
### Changed
- Update dependencies.

## [0.5.2] - 2025-02-03
### Changed
- Internal updates.

## [0.5.1] - 2024-11-25
### Changed
- Updated dependencies. [#40286]

## [0.5.0] - 2024-11-14
### Removed
- General: Update minimum PHP version to 7.2. [#40147]

## [0.4.13] - 2024-11-04
### Added
- Enable test coverage. [#39961]

## [0.4.12] - 2024-10-21
### Fixed
- Fixed the site features for Simple sites. [#39817]

## [0.4.11] - 2024-09-23
### Changed
- Update dependencies.

## [0.4.10] - 2024-09-05
### Changed
- Update dependencies.

## [0.4.9] - 2024-09-05
### Changed
- Update dependencies.

## [0.4.8] - 2024-08-23
### Changed
- Updated package dependencies. [#39004]

## [0.4.7] - 2024-05-06
### Fixed
- Correctly reference `Store_Product_List` class in `Plans::get_plans()`. [#37201]

## [0.4.6] - 2024-04-25
### Changed
- Update dependencies.

## [0.4.5] - 2024-04-22
### Changed
- Internal updates.

## [0.4.4] - 2024-04-08
### Changed
- Internal updates.

## [0.4.3] - 2024-03-12
### Changed
- Internal updates.

## [0.4.2] - 2024-02-12
### Added
- Plan features: add "sharing block" to the list of features supported in the free plan. [#35577]

## [0.4.1] - 2024-01-18
### Added
- Support the new creator plan in our plan checks [#35071]

## [0.4.0] - 2023-11-20
### Changed
- Updated required PHP version to >= 7.0. [#34192]

## [0.3.5] - 2023-10-24

- Updated package dependencies.

## [0.3.4] - 2023-08-23
### Changed
- Updated package dependencies. [#32605]

## [0.3.3] - 2023-06-19
### Added
- Add the Advanced SEO feature to the list of free features. [#31213]

## [0.3.2] - 2023-06-12
### Changed
- Remove Jetpack-the-plugin dependencies from Current Plan class. [#31207]

## [0.3.1] - 2023-06-05
### Fixed
- Revert gating removal for simple payments [#31067]

## [0.3.0] - 2023-05-11
### Changed
- Make Earn products free for all Jetpack plans [#30432]

## [0.2.12] - 2023-05-08
### Changed
- Make Premium Content accessible on Free plans. [#30398]

## [0.2.11] - 2023-04-10
### Added
- Add Jetpack Autoloader package suggestion. [#29988]

## [0.2.10] - 2023-02-20
### Changed
- Minor internal updates.

## [0.2.9] - 2023-01-25
### Changed
- Minor internal updates.

## [0.2.8] - 2022-12-02
### Changed
- Updated package dependencies. [#27688]

## [0.2.7] - 2022-11-22
### Changed
- Updated package dependencies. [#27043]

## [0.2.6] - 2022-11-07
### Changed
- Updated package dependencies. [#27278]

## [0.2.5] - 2022-10-25
### Changed
- Updated package dependencies. [#26705]

## [0.2.4] - 2022-09-20
### Changed
- Updated package dependencies.

## [0.2.3] - 2022-09-08
### Changed
- Updated package dependencies.

## [0.2.2] - 2022-08-30
### Changed
- Updated package dependencies. [#25694]

## [0.2.1] - 2022-08-23
### Changed
- Updated package dependencies. [#25628]

## [0.2.0] - 2022-08-16
### Added
- Tweaked the supports method of the plans package to refresh the plan data. [#25347]

## [0.1.3] - 2022-08-03
### Changed
- Updated package dependencies. [#25300, #25315]

## [0.1.2] - 2022-07-26
### Changed
- Add mirror repository information to package info. [#25142]
- Updated package dependencies. [#25158]

## 0.1.1 - 2022-07-19
### Changed
- Updated package dependencies.

## 0.1.0 - 2022-07-06
### Added
- Add support for WordPress.com Starter plan. [#24496]
- Package created. [#23503]

### Changed
- Renaming master to trunk. [#24661]
- Updated package dependencies.

### Deprecated
- Moved the options class into Connection. [#24095]

[0.9.0]: https://github.com/Automattic/jetpack-plans/compare/v0.8.0...v0.9.0
[0.8.0]: https://github.com/Automattic/jetpack-plans/compare/v0.7.1...v0.8.0
[0.7.1]: https://github.com/Automattic/jetpack-plans/compare/v0.7.0...v0.7.1
[0.7.0]: https://github.com/Automattic/jetpack-plans/compare/v0.6.1...v0.7.0
[0.6.1]: https://github.com/Automattic/jetpack-plans/compare/v0.6.0...v0.6.1
[0.6.0]: https://github.com/Automattic/jetpack-plans/compare/v0.5.6...v0.6.0
[0.5.6]: https://github.com/Automattic/jetpack-plans/compare/v0.5.5...v0.5.6
[0.5.5]: https://github.com/Automattic/jetpack-plans/compare/v0.5.4...v0.5.5
[0.5.4]: https://github.com/Automattic/jetpack-plans/compare/v0.5.3...v0.5.4
[0.5.3]: https://github.com/Automattic/jetpack-plans/compare/v0.5.2...v0.5.3
[0.5.2]: https://github.com/Automattic/jetpack-plans/compare/v0.5.1...v0.5.2
[0.5.1]: https://github.com/Automattic/jetpack-plans/compare/v0.5.0...v0.5.1
[0.5.0]: https://github.com/Automattic/jetpack-plans/compare/v0.4.13...v0.5.0
[0.4.13]: https://github.com/Automattic/jetpack-plans/compare/v0.4.12...v0.4.13
[0.4.12]: https://github.com/Automattic/jetpack-plans/compare/v0.4.11...v0.4.12
[0.4.11]: https://github.com/Automattic/jetpack-plans/compare/v0.4.10...v0.4.11
[0.4.10]: https://github.com/Automattic/jetpack-plans/compare/v0.4.9...v0.4.10
[0.4.9]: https://github.com/Automattic/jetpack-plans/compare/v0.4.8...v0.4.9
[0.4.8]: https://github.com/Automattic/jetpack-plans/compare/v0.4.7...v0.4.8
[0.4.7]: https://github.com/Automattic/jetpack-plans/compare/v0.4.6...v0.4.7
[0.4.6]: https://github.com/Automattic/jetpack-plans/compare/v0.4.5...v0.4.6
[0.4.5]: https://github.com/Automattic/jetpack-plans/compare/v0.4.4...v0.4.5
[0.4.4]: https://github.com/Automattic/jetpack-plans/compare/v0.4.3...v0.4.4
[0.4.3]: https://github.com/Automattic/jetpack-plans/compare/v0.4.2...v0.4.3
[0.4.2]: https://github.com/Automattic/jetpack-plans/compare/v0.4.1...v0.4.2
[0.4.1]: https://github.com/Automattic/jetpack-plans/compare/v0.4.0...v0.4.1
[0.4.0]: https://github.com/Automattic/jetpack-plans/compare/v0.3.5...v0.4.0
[0.3.5]: https://github.com/Automattic/jetpack-plans/compare/v0.3.4...v0.3.5
[0.3.4]: https://github.com/Automattic/jetpack-plans/compare/v0.3.3...v0.3.4
[0.3.3]: https://github.com/Automattic/jetpack-plans/compare/v0.3.2...v0.3.3
[0.3.2]: https://github.com/Automattic/jetpack-plans/compare/v0.3.1...v0.3.2
[0.3.1]: https://github.com/Automattic/jetpack-plans/compare/v0.3.0...v0.3.1
[0.3.0]: https://github.com/Automattic/jetpack-plans/compare/v0.2.12...v0.3.0
[0.2.12]: https://github.com/Automattic/jetpack-plans/compare/v0.2.11...v0.2.12
[0.2.11]: https://github.com/Automattic/jetpack-plans/compare/v0.2.10...v0.2.11
[0.2.10]: https://github.com/Automattic/jetpack-plans/compare/v0.2.9...v0.2.10
[0.2.9]: https://github.com/Automattic/jetpack-plans/compare/v0.2.8...v0.2.9
[0.2.8]: https://github.com/Automattic/jetpack-plans/compare/v0.2.7...v0.2.8
[0.2.7]: https://github.com/Automattic/jetpack-plans/compare/v0.2.6...v0.2.7
[0.2.6]: https://github.com/Automattic/jetpack-plans/compare/v0.2.5...v0.2.6
[0.2.5]: https://github.com/Automattic/jetpack-plans/compare/v0.2.4...v0.2.5
[0.2.4]: https://github.com/Automattic/jetpack-plans/compare/v0.2.3...v0.2.4
[0.2.3]: https://github.com/Automattic/jetpack-plans/compare/v0.2.2...v0.2.3
[0.2.2]: https://github.com/Automattic/jetpack-plans/compare/v0.2.1...v0.2.2
[0.2.1]: https://github.com/Automattic/jetpack-plans/compare/v0.2.0...v0.2.1
[0.2.0]: https://github.com/Automattic/jetpack-plans/compare/v0.1.3...v0.2.0
[0.1.3]: https://github.com/Automattic/jetpack-plans/compare/v0.1.2...v0.1.3
[0.1.2]: https://github.com/Automattic/jetpack-plans/compare/v0.1.1...v0.1.2
