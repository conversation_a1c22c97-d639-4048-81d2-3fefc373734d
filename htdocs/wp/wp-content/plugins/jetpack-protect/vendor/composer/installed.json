{"packages": [{"name": "automattic/jetpack-a8c-mc-stats", "version": "v3.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-a8c-mc-stats.git", "reference": "60401a41c714d93f7a31e36493260ad6683ca4be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-a8c-mc-stats/zipball/60401a41c714d93f7a31e36493260ad6683ca4be", "reference": "60401a41c714d93f7a31e36493260ad6683ca4be", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-04-28T15:12:40+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-a8c-mc-stats", "branch-alias": {"dev-trunk": "3.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-a8c-mc-stats/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Used to record internal usage stats for Automattic. Not visible to site owners.", "support": {"source": "https://github.com/Automattic/jetpack-a8c-mc-stats/tree/v3.0.5"}, "install-path": "../../jetpack_vendor/automattic/jetpack-a8c-mc-stats"}, {"name": "automattic/jetpack-account-protection", "version": "v0.2.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-account-protection.git", "reference": "bd162ef2fa751c79d5ffe6b006c6e3be6800abf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-account-protection/zipball/bd162ef2fa751c79d5ffe6b006c6e3be6800abf4", "reference": "bd162ef2fa751c79d5ffe6b006c6e3be6800abf4", "shasum": ""}, "require": {"automattic/jetpack-connection": "^6.16.1", "automattic/jetpack-constants": "^3.0.8", "automattic/jetpack-logo": "^3.0.5", "automattic/jetpack-status": "^6.0.0", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-07-28T19:50:31+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-account-protection", "mirror-repo": "Automattic/jetpack-account-protection", "branch-alias": {"dev-trunk": "0.2.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-account-protection/compare/v${old}...v${new}"}, "version-constants": {"::PACKAGE_VERSION": "src/class-account-protection.php"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Account protection", "support": {"source": "https://github.com/Automattic/jetpack-account-protection/tree/v0.2.6"}, "install-path": "../../jetpack_vendor/automattic/jetpack-account-protection"}, {"name": "automattic/jetpack-admin-ui", "version": "v0.5.10", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-admin-ui.git", "reference": "3f4201c45ae670083fe214f821b07793c732f6e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-admin-ui/zipball/3f4201c45ae670083fe214f821b07793c732f6e1", "reference": "3f4201c45ae670083fe214f821b07793c732f6e1", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-logo": "^3.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-06-06T19:42:39+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-admin-ui", "mirror-repo": "Automattic/jetpack-admin-ui", "branch-alias": {"dev-trunk": "0.5.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-admin-ui/compare/${old}...${new}"}, "version-constants": {"::PACKAGE_VERSION": "src/class-admin-menu.php"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Generic Jetpack wp-admin UI elements", "support": {"source": "https://github.com/Automattic/jetpack-admin-ui/tree/v0.5.10"}, "install-path": "../../jetpack_vendor/automattic/jetpack-admin-ui"}, {"name": "automattic/jetpack-assets", "version": "v4.3.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-assets.git", "reference": "aace1c839f0f3473ecf6d1987b3535edab0767ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-assets/zipball/aace1c839f0f3473ecf6d1987b3535edab0767ce", "reference": "aace1c839f0f3473ecf6d1987b3535edab0767ce", "shasum": ""}, "require": {"automattic/jetpack-constants": "^3.0.8", "automattic/jetpack-status": "^6.0.0", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "brain/monkey": "^2.6.2", "wikimedia/testing-access-wrapper": "^1.0 || ^2.0 || ^3.0", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-07-30T14:11:06+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-assets", "mirror-repo": "Automattic/jetpack-assets", "branch-alias": {"dev-trunk": "4.3.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-assets/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"files": ["actions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Asset management utilities for Jetpack ecosystem packages", "support": {"source": "https://github.com/Automattic/jetpack-assets/tree/v4.3.1"}, "install-path": "../../jetpack_vendor/automattic/jetpack-assets"}, {"name": "automattic/jetpack-autoloader", "version": "v5.0.9", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-autoloader.git", "reference": "c9e9b82cc515d9ed093fa0ff21245f277aeceb4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-autoloader/zipball/c9e9b82cc515d9ed093fa0ff21245f277aeceb4e", "reference": "c9e9b82cc515d9ed093fa0ff21245f277aeceb4e", "shasum": ""}, "require": {"composer-plugin-api": "^2.2", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "composer/composer": "^2.2", "yoast/phpunit-polyfills": "^4.0.0"}, "time": "2025-07-28T19:49:50+00:00", "type": "composer-plugin", "extra": {"class": "Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin", "autotagger": true, "mirror-repo": "Automattic/jetpack-autoloader", "branch-alias": {"dev-trunk": "5.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-autoloader/compare/v${old}...v${new}"}, "version-constants": {"::VERSION": "src/AutoloadGenerator.php"}}, "installation-source": "dist", "autoload": {"psr-4": {"Automattic\\Jetpack\\Autoloader\\": "src"}, "classmap": ["src/AutoloadGenerator.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Creates a custom autoloader for a plugin or theme.", "keywords": ["autoload", "autoloader", "composer", "jetpack", "plugin", "wordpress"], "support": {"source": "https://github.com/Automattic/jetpack-autoloader/tree/v5.0.9"}, "install-path": "../automattic/jetpack-autoloader"}, {"name": "automattic/jetpack-backup-helper-script-manager", "version": "v0.3.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-backup-helper-script-manager.git", "reference": "02ce3954fed40f964d6bcff7ad2aae2e85cd78f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-backup-helper-script-manager/zipball/02ce3954fed40f964d6bcff7ad2aae2e85cd78f9", "reference": "02ce3954fed40f964d6bcff7ad2aae2e85cd78f9", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-04-28T15:12:50+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-backup-helper-script-manager", "branch-alias": {"dev-trunk": "0.3.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-backup-helper-script-manager/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Install / delete helper script for backup and transport server. Not visible to site owners.", "support": {"source": "https://github.com/Automattic/jetpack-backup-helper-script-manager/tree/v0.3.7"}, "install-path": "../../jetpack_vendor/automattic/jetpack-backup-helper-script-manager"}, {"name": "automattic/jetpack-boost-core", "version": "v0.3.11", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-boost-core.git", "reference": "26a131e021da4024afd9ae0fafa15b302725e813"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-boost-core/zipball/26a131e021da4024afd9ae0fafa15b302725e813", "reference": "26a131e021da4024afd9ae0fafa15b302725e813", "shasum": ""}, "require": {"automattic/jetpack-connection": "^6.13.7", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-06-23T12:07:28+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-boost-core", "mirror-repo": "Automattic/jetpack-boost-core", "branch-alias": {"dev-trunk": "0.3.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-boost-core/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Core functionality for boost and relevant packages to depend on", "support": {"source": "https://github.com/Automattic/jetpack-boost-core/tree/v0.3.11"}, "install-path": "../../jetpack_vendor/automattic/jetpack-boost-core"}, {"name": "automattic/jetpack-boost-speed-score", "version": "v0.4.10", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-boost-speed-score.git", "reference": "9ca87099a6c876a926132360f024b50eb2da63fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-boost-speed-score/zipball/9ca87099a6c876a926132360f024b50eb2da63fb", "reference": "9ca87099a6c876a926132360f024b50eb2da63fb", "shasum": ""}, "require": {"automattic/jetpack-boost-core": "^0.3.11", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "brain/monkey": "^2.6", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-07-21T15:11:09+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-boost-speed-score", "mirror-repo": "Automattic/jetpack-boost-speed-score", "branch-alias": {"dev-trunk": "0.4.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-boost-speed-score/compare/v${old}...v${new}"}, "version-constants": {"::PACKAGE_VERSION": "src/class-speed-score.php"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "A package that handles the API to generate the speed score.", "support": {"source": "https://github.com/Automattic/jetpack-boost-speed-score/tree/v0.4.10"}, "install-path": "../../jetpack_vendor/automattic/jetpack-boost-speed-score"}, {"name": "automattic/jetpack-composer-plugin", "version": "v4.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-composer-plugin.git", "reference": "e0a5ad3a32802ec156cb38c6280e2c12bfc63ffe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-composer-plugin/zipball/e0a5ad3a32802ec156cb38c6280e2c12bfc63ffe", "reference": "e0a5ad3a32802ec156cb38c6280e2c12bfc63ffe", "shasum": ""}, "require": {"composer-plugin-api": "^2.2", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "composer/composer": "^2.2", "yoast/phpunit-polyfills": "^4.0.0"}, "time": "2025-04-28T15:12:46+00:00", "type": "composer-plugin", "extra": {"class": "Automattic\\Jetpack\\Composer\\Plugin", "autotagger": true, "mirror-repo": "Automattic/jetpack-composer-plugin", "branch-alias": {"dev-trunk": "4.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-composer-plugin/compare/v${old}...v${new}"}, "plugin-modifies-install-path": true}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "A custom installer plugin for Composer to move Jetpack packages out of `vendor/` so WordPress's translation infrastructure will find their strings.", "keywords": ["composer", "i18n", "jetpack", "plugin"], "support": {"source": "https://github.com/Automattic/jetpack-composer-plugin/tree/v4.0.5"}, "install-path": "../automattic/jetpack-composer-plugin"}, {"name": "automattic/jetpack-config", "version": "v3.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-config.git", "reference": "2704e4122684c6553c618cca76e5d84cd524738c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-config/zipball/2704e4122684c6553c618cca76e5d84cd524738c", "reference": "2704e4122684c6553c618cca76e5d84cd524738c", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-account-protection": "@dev", "automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-connection": "@dev", "automattic/jetpack-import": "@dev", "automattic/jetpack-jitm": "@dev", "automattic/jetpack-post-list": "@dev", "automattic/jetpack-publicize": "@dev", "automattic/jetpack-search": "@dev", "automattic/jetpack-stats": "@dev", "automattic/jetpack-stats-admin": "@dev", "automattic/jetpack-sync": "@dev", "automattic/jetpack-videopress": "@dev", "automattic/jetpack-waf": "@dev", "automattic/jetpack-yoast-promo": "@dev"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-06-19T13:25:10+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-config", "mirror-repo": "Automattic/jetpack-config", "branch-alias": {"dev-trunk": "3.1.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-config/compare/v${old}...v${new}"}, "dependencies": {"test-only": ["packages/account-protection", "packages/connection", "packages/import", "packages/jitm", "packages/post-list", "packages/publicize", "packages/search", "packages/stats-admin", "packages/stats", "packages/sync", "packages/videopress", "packages/waf", "packages/yoast-promo"]}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Jetpack configuration package that initializes other packages and configures Jetpack's functionality. Can be used as a base for all variants of Jetpack package usage.", "support": {"source": "https://github.com/Automattic/jetpack-config/tree/v3.1.1"}, "install-path": "../../jetpack_vendor/automattic/jetpack-config"}, {"name": "automattic/jetpack-connection", "version": "v6.16.2", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-connection.git", "reference": "454ab919ee706a244eee9687c4f36656208cd80b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-connection/zipball/454ab919ee706a244eee9687c4f36656208cd80b", "reference": "454ab919ee706a244eee9687c4f36656208cd80b", "shasum": ""}, "require": {"automattic/jetpack-a8c-mc-stats": "^3.0.5", "automattic/jetpack-admin-ui": "^0.5.10", "automattic/jetpack-assets": "^4.3.1", "automattic/jetpack-constants": "^3.0.8", "automattic/jetpack-redirect": "^3.0.8", "automattic/jetpack-roles": "^3.0.8", "automattic/jetpack-status": "^6.0.0", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3", "brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-07-30T14:11:27+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-connection", "mirror-repo": "Automattic/jetpack-connection", "branch-alias": {"dev-trunk": "6.16.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-connection/compare/v${old}...v${new}"}, "dependencies": {"test-only": ["packages/licensing", "packages/sync"]}, "version-constants": {"::PACKAGE_VERSION": "src/class-package-version.php"}}, "installation-source": "dist", "autoload": {"files": ["actions.php"], "classmap": ["legacy", "src/", "src/webhooks", "src/identity-crisis"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Everything needed to connect to the Jetpack infrastructure", "support": {"source": "https://github.com/Automattic/jetpack-connection/tree/v6.16.2"}, "install-path": "../../jetpack_vendor/automattic/jetpack-connection"}, {"name": "automattic/jetpack-constants", "version": "v3.0.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-constants.git", "reference": "f9bf00ab48956b8326209e7c0baf247a0ed721c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-constants/zipball/f9bf00ab48956b8326209e7c0baf247a0ed721c4", "reference": "f9bf00ab48956b8326209e7c0baf247a0ed721c4", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-04-28T15:12:45+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-constants", "branch-alias": {"dev-trunk": "3.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-constants/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "A wrapper for defining constants in a more testable way.", "support": {"source": "https://github.com/Automattic/jetpack-constants/tree/v3.0.8"}, "install-path": "../../jetpack_vendor/automattic/jetpack-constants"}, {"name": "automattic/jetpack-device-detection", "version": "v3.0.9", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-device-detection.git", "reference": "9eba4f274cd858253b6c125aff90c9182431c941"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-device-detection/zipball/9eba4f274cd858253b6c125aff90c9182431c941", "reference": "9eba4f274cd858253b6c125aff90c9182431c941", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-06-27T17:11:11+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-device-detection", "branch-alias": {"dev-trunk": "3.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-device-detection/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "A way to detect device types based on User-Agent header.", "support": {"source": "https://github.com/Automattic/jetpack-device-detection/tree/v3.0.9"}, "install-path": "../../jetpack_vendor/automattic/jetpack-device-detection"}, {"name": "automattic/jetpack-explat", "version": "v0.3.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-explat.git", "reference": "14e47c3926366bc28632bfbdce2484852c666b70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-explat/zipball/14e47c3926366bc28632bfbdce2484852c666b70", "reference": "14e47c3926366bc28632bfbdce2484852c666b70", "shasum": ""}, "require": {"automattic/jetpack-connection": "^6.15.0", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-07-21T15:11:16+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-explat", "mirror-repo": "Automattic/jetpack-explat", "branch-alias": {"dev-trunk": "0.3.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-explat/compare/v${old}...v${new}"}, "version-constants": {"::PACKAGE_VERSION": "src/class-explat.php"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "A package for running A/B tests on the Experimentation Platform (ExPlat) in the plugin.", "support": {"source": "https://github.com/Automattic/jetpack-explat/tree/v0.3.4"}, "install-path": "../../jetpack_vendor/automattic/jetpack-explat"}, {"name": "automattic/jetpack-ip", "version": "v0.4.9", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-ip.git", "reference": "c15719e2c6f84cbb120424f18c490966f4875082"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-ip/zipball/c15719e2c6f84cbb120424f18c490966f4875082", "reference": "c15719e2c6f84cbb120424f18c490966f4875082", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-04-28T15:12:42+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-ip", "mirror-repo": "Automattic/jetpack-ip", "branch-alias": {"dev-trunk": "0.4.x-dev"}, "changelogger": {"link-template": "https://github.com/automattic/jetpack-ip/compare/v${old}...v${new}"}, "version-constants": {"::PACKAGE_VERSION": "src/class-utils.php"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Utilities for working with IP addresses.", "support": {"source": "https://github.com/Automattic/jetpack-ip/tree/v0.4.9"}, "install-path": "../../jetpack_vendor/automattic/jetpack-ip"}, {"name": "automattic/jetpack-jitm", "version": "v4.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-jitm.git", "reference": "8240663faa4205e3638d46c9840a70d76c3a465d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-jitm/zipball/8240663faa4205e3638d46c9840a70d76c3a465d", "reference": "8240663faa4205e3638d46c9840a70d76c3a465d", "shasum": ""}, "require": {"automattic/jetpack-a8c-mc-stats": "^3.0.5", "automattic/jetpack-assets": "^4.3.0", "automattic/jetpack-connection": "^6.16.0", "automattic/jetpack-device-detection": "^3.0.9", "automattic/jetpack-logo": "^3.0.5", "automattic/jetpack-redirect": "^3.0.8", "automattic/jetpack-status": "^6.0.0", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-07-23T16:25:37+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-jitm", "mirror-repo": "Automattic/jetpack-jitm", "branch-alias": {"dev-trunk": "4.3.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-jitm/compare/v${old}...v${new}"}, "version-constants": {"::PACKAGE_VERSION": "src/class-jitm.php"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Just in time messages for Jet<PERSON>", "support": {"source": "https://github.com/Automattic/jetpack-jitm/tree/v4.3.0"}, "install-path": "../../jetpack_vendor/automattic/jetpack-jitm"}, {"name": "automattic/jetpack-licensing", "version": "v3.0.9", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-licensing.git", "reference": "ee2c8f35c1d82469b80343072d04869e64c6df35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-licensing/zipball/ee2c8f35c1d82469b80343072d04869e64c6df35", "reference": "ee2c8f35c1d82469b80343072d04869e64c6df35", "shasum": ""}, "require": {"automattic/jetpack-connection": "^6.11.1", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-04-28T15:13:42+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-licensing", "mirror-repo": "Automattic/jetpack-licensing", "branch-alias": {"dev-trunk": "3.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-licensing/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Everything needed to manage Jetpack licenses client-side.", "support": {"source": "https://github.com/Automattic/jetpack-licensing/tree/v3.0.9"}, "install-path": "../../jetpack_vendor/automattic/jetpack-licensing"}, {"name": "automattic/jetpack-logo", "version": "v3.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-logo.git", "reference": "53b54aaabdad187fff32c4d60ee04878229542e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-logo/zipball/53b54aaabdad187fff32c4d60ee04878229542e5", "reference": "53b54aaabdad187fff32c4d60ee04878229542e5", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-04-28T15:12:43+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-logo", "branch-alias": {"dev-trunk": "3.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-logo/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "A logo for Jetpack", "support": {"source": "https://github.com/Automattic/jetpack-logo/tree/v3.0.5"}, "install-path": "../../jetpack_vendor/automattic/jetpack-logo"}, {"name": "automattic/jetpack-my-jetpack", "version": "v5.21.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-my-jetpack.git", "reference": "d30efbfb2d09ddc516668db0f3ec946ed5457cdd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-my-jetpack/zipball/d30efbfb2d09ddc516668db0f3ec946ed5457cdd", "reference": "d30efbfb2d09ddc516668db0f3ec946ed5457cdd", "shasum": ""}, "require": {"automattic/jetpack-admin-ui": "^0.5.10", "automattic/jetpack-assets": "^4.3.1", "automattic/jetpack-boost-speed-score": "^0.4.10", "automattic/jetpack-connection": "^6.16.2", "automattic/jetpack-constants": "^3.0.8", "automattic/jetpack-explat": "^0.3.4", "automattic/jetpack-jitm": "^4.3.0", "automattic/jetpack-licensing": "^3.0.9", "automattic/jetpack-plans": "^0.9.0", "automattic/jetpack-plugins-installer": "^0.5.6", "automattic/jetpack-protect-status": "^0.7.0", "automattic/jetpack-redirect": "^3.0.8", "automattic/jetpack-status": "^6.0.0", "automattic/jetpack-sync": "^4.16.0", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-07-30T14:12:01+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-my-jetpack", "mirror-repo": "Automattic/jetpack-my-jetpack", "branch-alias": {"dev-trunk": "5.21.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-my-jetpack/compare/${old}...${new}"}, "dependencies": {"test-only": ["packages/search", "packages/videopress"]}, "version-constants": {"::PACKAGE_VERSION": "src/class-initializer.php"}}, "installation-source": "dist", "autoload": {"classmap": ["src/", "src/products"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "WP Admin page with information and configuration shared among all Jetpack stand-alone plugins", "support": {"source": "https://github.com/Automattic/jetpack-my-jetpack/tree/v5.21.0"}, "install-path": "../../jetpack_vendor/automattic/jetpack-my-jetpack"}, {"name": "automattic/jetpack-password-checker", "version": "v0.4.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-password-checker.git", "reference": "53c5d39afffd8fdf8001c24e7efc2b2b7760359a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-password-checker/zipball/53c5d39afffd8fdf8001c24e7efc2b2b7760359a", "reference": "53c5d39afffd8fdf8001c24e7efc2b2b7760359a", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-04-28T15:12:49+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-password-checker", "mirror-repo": "Automattic/jetpack-password-checker", "branch-alias": {"dev-trunk": "0.4.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-password-checker/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Password Checker.", "support": {"source": "https://github.com/Automattic/jetpack-password-checker/tree/v0.4.8"}, "install-path": "../../jetpack_vendor/automattic/jetpack-password-checker"}, {"name": "automattic/jetpack-plans", "version": "v0.9.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-plans.git", "reference": "4847396301e099e334cd4e24f2782825eaded5ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-plans/zipball/4847396301e099e334cd4e24f2782825eaded5ed", "reference": "4847396301e099e334cd4e24f2782825eaded5ed", "shasum": ""}, "require": {"automattic/jetpack-connection": "^6.15.0", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-status": "^6.0.0", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-07-21T15:11:01+00:00", "type": "library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-plans", "branch-alias": {"dev-trunk": "0.9.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-plans/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Fetch information about Jetpack Plans from wpcom", "support": {"source": "https://github.com/Automattic/jetpack-plans/tree/v0.9.0"}, "install-path": "../automattic/jetpack-plans"}, {"name": "automattic/jetpack-plugins-installer", "version": "v0.5.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-plugins-installer.git", "reference": "98e55a8c362ea42ed48a9149151f4041ad3b6c42"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-plugins-installer/zipball/98e55a8c362ea42ed48a9149151f4041ad3b6c42", "reference": "98e55a8c362ea42ed48a9149151f4041ad3b6c42", "shasum": ""}, "require": {"automattic/jetpack-a8c-mc-stats": "^3.0.5", "automattic/jetpack-status": "^6.0.0", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-07-21T15:54:59+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-plugins-installer", "mirror-repo": "Automattic/jetpack-plugins-installer", "branch-alias": {"dev-trunk": "0.5.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-plugins-installer/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Handle installation of plugins from WP.org", "support": {"source": "https://github.com/Automattic/jetpack-plugins-installer/tree/v0.5.6"}, "install-path": "../../jetpack_vendor/automattic/jetpack-plugins-installer"}, {"name": "automattic/jetpack-protect-models", "version": "v0.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-protect-models.git", "reference": "22f924aec2aad3b2dabcc5d9758312d7446ae198"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-protect-models/zipball/22f924aec2aad3b2dabcc5d9758312d7446ae198", "reference": "22f924aec2aad3b2dabcc5d9758312d7446ae198", "shasum": ""}, "require": {"automattic/jetpack-redirect": "^3.0.7", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-06-05T14:23:07+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-protect-models", "mirror-repo": "Automattic/jetpack-protect-models", "branch-alias": {"dev-trunk": "0.6.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-protect-models/compare/v${old}...v${new}"}, "version-constants": {"::PACKAGE_VERSION": "src/class-protect-models.php"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "This package contains the models used in Protect. ", "support": {"source": "https://github.com/Automattic/jetpack-protect-models/tree/v0.6.0"}, "install-path": "../../jetpack_vendor/automattic/jetpack-protect-models"}, {"name": "automattic/jetpack-protect-status", "version": "v0.7.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-protect-status.git", "reference": "0abffede51cad043673f38d820a2da7af41569b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-protect-status/zipball/0abffede51cad043673f38d820a2da7af41569b5", "reference": "0abffede51cad043673f38d820a2da7af41569b5", "shasum": ""}, "require": {"automattic/jetpack-connection": "^6.16.2", "automattic/jetpack-plans": "^0.9.0", "automattic/jetpack-plugins-installer": "^0.5.6", "automattic/jetpack-protect-models": "^0.6.0", "automattic/jetpack-sync": "^4.16.0", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-07-30T14:11:42+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-protect-status", "mirror-repo": "Automattic/jetpack-protect-status", "branch-alias": {"dev-trunk": "0.7.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-protect-status/compare/v${old}...v${new}"}, "version-constants": {"::PACKAGE_VERSION": "src/class-status.php"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "This package contains the Protect Status API functionality to retrieve a site's scan status (WordPress, Themes, and Plugins threats).", "support": {"source": "https://github.com/Automattic/jetpack-protect-status/tree/v0.7.0"}, "install-path": "../../jetpack_vendor/automattic/jetpack-protect-status"}, {"name": "automattic/jetpack-redirect", "version": "v3.0.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-redirect.git", "reference": "876617673a655797178a4c35d9676000829432df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-redirect/zipball/876617673a655797178a4c35d9676000829432df", "reference": "876617673a655797178a4c35d9676000829432df", "shasum": ""}, "require": {"automattic/jetpack-status": "^6.0.0", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-07-21T15:54:58+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-redirect", "branch-alias": {"dev-trunk": "3.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-redirect/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Utilities to build URLs to the jetpack.com/redirect/ service", "support": {"source": "https://github.com/Automattic/jetpack-redirect/tree/v3.0.8"}, "install-path": "../../jetpack_vendor/automattic/jetpack-redirect"}, {"name": "automattic/jetpack-roles", "version": "v3.0.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-roles.git", "reference": "96e09fc813ccf5e691f46297875d6b85b859c669"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-roles/zipball/96e09fc813ccf5e691f46297875d6b85b859c669", "reference": "96e09fc813ccf5e691f46297875d6b85b859c669", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3", "brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-04-28T15:12:44+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-roles", "branch-alias": {"dev-trunk": "3.0.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-roles/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Utilities, related with user roles and capabilities.", "support": {"source": "https://github.com/Automattic/jetpack-roles/tree/v3.0.8"}, "install-path": "../../jetpack_vendor/automattic/jetpack-roles"}, {"name": "automattic/jetpack-status", "version": "v6.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-status.git", "reference": "64c01a04c4cf330138495e34197c2bf99fd00b66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-status/zipball/64c01a04c4cf330138495e34197c2bf99fd00b66", "reference": "64c01a04c4cf330138495e34197c2bf99fd00b66", "shasum": ""}, "require": {"automattic/jetpack-constants": "^3.0.8", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-connection": "@dev", "automattic/jetpack-ip": "^0.4.9", "automattic/jetpack-plans": "@dev", "automattic/phpunit-select-config": "^1.0.3", "brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-07-21T15:10:22+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-status", "branch-alias": {"dev-trunk": "5.4.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-status/compare/v${old}...v${new}"}, "dependencies": {"test-only": ["packages/connection", "packages/plans"]}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Used to retrieve information about the current status of Jetpack and the site overall.", "support": {"source": "https://github.com/Automattic/jetpack-status/tree/v6.0.0"}, "install-path": "../../jetpack_vendor/automattic/jetpack-status"}, {"name": "automattic/jetpack-sync", "version": "v4.16.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-sync.git", "reference": "56520945866116cd6cfd8bf23a0a27de91256329"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-sync/zipball/56520945866116cd6cfd8bf23a0a27de91256329", "reference": "56520945866116cd6cfd8bf23a0a27de91256329", "shasum": ""}, "require": {"automattic/jetpack-connection": "^6.16.1", "automattic/jetpack-constants": "^3.0.8", "automattic/jetpack-ip": "^0.4.9", "automattic/jetpack-password-checker": "^0.4.8", "automattic/jetpack-roles": "^3.0.8", "automattic/jetpack-status": "^6.0.0", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-search": "@dev", "automattic/jetpack-test-environment": "@dev", "automattic/jetpack-waf": "^0.27.1", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-07-28T19:50:34+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-sync", "mirror-repo": "Automattic/jetpack-sync", "branch-alias": {"dev-trunk": "4.16.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-sync/compare/v${old}...v${new}"}, "dependencies": {"test-only": ["packages/search", "packages/waf"]}, "version-constants": {"::PACKAGE_VERSION": "src/class-package-version.php"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Everything needed to allow syncing to the WP.com infrastructure.", "support": {"source": "https://github.com/Automattic/jetpack-sync/tree/v4.16.0"}, "install-path": "../../jetpack_vendor/automattic/jetpack-sync"}, {"name": "automattic/jetpack-transport-helper", "version": "v0.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-transport-helper.git", "reference": "f19eb1adfc795aea76a5f21f8b8744d057ac94db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-transport-helper/zipball/f19eb1adfc795aea76a5f21f8b8744d057ac94db", "reference": "f19eb1adfc795aea76a5f21f8b8744d057ac94db", "shasum": ""}, "require": {"automattic/jetpack-backup-helper-script-manager": "^0.3.7", "automattic/jetpack-connection": "^6.11.9", "php": ">=7.2"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-06-03T16:47:41+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-transport-helper", "mirror-repo": "Automattic/jetpack-transport-helper", "branch-alias": {"dev-trunk": "0.3.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-transport-helper/compare/v${old}...v${new}"}, "version-constants": {"::PACKAGE_VERSION": "src/class-package-version.php"}}, "installation-source": "dist", "autoload": {"files": ["actions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Package to help transport server communication", "support": {"source": "https://github.com/Automattic/jetpack-transport-helper/tree/v0.3.2"}, "install-path": "../../jetpack_vendor/automattic/jetpack-transport-helper"}, {"name": "automattic/jetpack-waf", "version": "v0.27.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-waf.git", "reference": "424f0ca5290810d1dc738550ca1a717218534fb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-waf/zipball/424f0ca5290810d1dc738550ca1a717218534fb7", "reference": "424f0ca5290810d1dc738550ca1a717218534fb7", "shasum": ""}, "require": {"automattic/jetpack-connection": "^6.15.0", "automattic/jetpack-constants": "^3.0.8", "automattic/jetpack-ip": "^0.4.9", "automattic/jetpack-status": "^6.0.0", "php": ">=7.2", "wikimedia/aho-corasick": "^1.0"}, "require-dev": {"automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3", "yoast/phpunit-polyfills": "^4.0.0"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2025-07-21T15:11:08+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "textdomain": "jetpack-waf", "mirror-repo": "Automattic/jetpack-waf", "branch-alias": {"dev-trunk": "0.27.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-waf/compare/v${old}...v${new}"}}, "installation-source": "dist", "autoload": {"files": ["cli.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Tools to assist with the Jetpack Web Application Firewall", "support": {"source": "https://github.com/Automattic/jetpack-waf/tree/v0.27.1"}, "install-path": "../../jetpack_vendor/automattic/jetpack-waf"}, {"name": "wikimedia/aho-corasick", "version": "v1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/wikimedia/AhoCorasick.git", "reference": "2f3a1bd765913637a66eade658d11d82f0e551be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wikimedia/AhoCorasick/zipball/2f3a1bd765913637a66eade658d11d82f0e551be", "reference": "2f3a1bd765913637a66eade658d11d82f0e551be", "shasum": ""}, "require": {"php": ">=5.5.9"}, "require-dev": {"jakub-onderka/php-console-highlighter": "0.3.2", "jakub-onderka/php-parallel-lint": "1.0.0", "mediawiki/mediawiki-codesniffer": "18.0.0", "mediawiki/minus-x": "0.3.1", "phpunit/phpunit": "4.8.36 || ^6.5"}, "time": "2018-05-01T18:13:32+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An implementation of the Aho-Corasick string matching algorithm.", "homepage": "https://gerrit.wikimedia.org/g/AhoCorasick", "keywords": ["ahocora<PERSON>ck", "matcher"], "support": {"source": "https://github.com/wikimedia/AhoCorasick/tree/v1.0.1"}, "install-path": "../wikimedia/aho-corasick"}], "dev": false, "dev-package-names": []}