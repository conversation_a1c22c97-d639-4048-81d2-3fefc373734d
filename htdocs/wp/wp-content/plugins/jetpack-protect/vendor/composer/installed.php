<?php return array(
    'root' => array(
        'name' => 'automattic/jetpack-protect',
        'pretty_version' => 'dev-trunk',
        'version' => 'dev-trunk',
        'reference' => null,
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'automattic/jetpack-a8c-mc-stats' => array(
            'pretty_version' => 'v3.0.5',
            'version' => '3.0.5.0',
            'reference' => '60401a41c714d93f7a31e36493260ad6683ca4be',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-a8c-mc-stats',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-account-protection' => array(
            'pretty_version' => 'v0.2.6',
            'version' => '0.2.6.0',
            'reference' => 'bd162ef2fa751c79d5ffe6b006c6e3be6800abf4',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-account-protection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-admin-ui' => array(
            'pretty_version' => 'v0.5.10',
            'version' => '0.5.10.0',
            'reference' => '3f4201c45ae670083fe214f821b07793c732f6e1',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-admin-ui',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-assets' => array(
            'pretty_version' => 'v4.3.1',
            'version' => '4.3.1.0',
            'reference' => 'aace1c839f0f3473ecf6d1987b3535edab0767ce',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-assets',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-autoloader' => array(
            'pretty_version' => 'v5.0.9',
            'version' => '*******',
            'reference' => 'c9e9b82cc515d9ed093fa0ff21245f277aeceb4e',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../automattic/jetpack-autoloader',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-backup-helper-script-manager' => array(
            'pretty_version' => 'v0.3.7',
            'version' => '*******',
            'reference' => '02ce3954fed40f964d6bcff7ad2aae2e85cd78f9',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-backup-helper-script-manager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-boost-core' => array(
            'pretty_version' => 'v0.3.11',
            'version' => '********',
            'reference' => '26a131e021da4024afd9ae0fafa15b302725e813',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-boost-core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-boost-speed-score' => array(
            'pretty_version' => 'v0.4.10',
            'version' => '********',
            'reference' => '9ca87099a6c876a926132360f024b50eb2da63fb',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-boost-speed-score',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-composer-plugin' => array(
            'pretty_version' => 'v4.0.5',
            'version' => '4.0.5.0',
            'reference' => 'e0a5ad3a32802ec156cb38c6280e2c12bfc63ffe',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../automattic/jetpack-composer-plugin',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-config' => array(
            'pretty_version' => 'v3.1.1',
            'version' => '3.1.1.0',
            'reference' => '2704e4122684c6553c618cca76e5d84cd524738c',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-connection' => array(
            'pretty_version' => 'v6.16.2',
            'version' => '6.16.2.0',
            'reference' => '454ab919ee706a244eee9687c4f36656208cd80b',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-connection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-constants' => array(
            'pretty_version' => 'v3.0.8',
            'version' => '3.0.8.0',
            'reference' => 'f9bf00ab48956b8326209e7c0baf247a0ed721c4',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-constants',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-device-detection' => array(
            'pretty_version' => 'v3.0.9',
            'version' => '*******',
            'reference' => '9eba4f274cd858253b6c125aff90c9182431c941',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-device-detection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-explat' => array(
            'pretty_version' => 'v0.3.4',
            'version' => '*******',
            'reference' => '14e47c3926366bc28632bfbdce2484852c666b70',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-explat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-ip' => array(
            'pretty_version' => 'v0.4.9',
            'version' => '*******',
            'reference' => 'c15719e2c6f84cbb120424f18c490966f4875082',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-ip',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-jitm' => array(
            'pretty_version' => 'v4.3.0',
            'version' => '*******',
            'reference' => '8240663faa4205e3638d46c9840a70d76c3a465d',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-jitm',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-licensing' => array(
            'pretty_version' => 'v3.0.9',
            'version' => '*******',
            'reference' => 'ee2c8f35c1d82469b80343072d04869e64c6df35',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-licensing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-logo' => array(
            'pretty_version' => 'v3.0.5',
            'version' => '3.0.5.0',
            'reference' => '53b54aaabdad187fff32c4d60ee04878229542e5',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-logo',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-my-jetpack' => array(
            'pretty_version' => 'v5.21.0',
            'version' => '5.21.0.0',
            'reference' => 'd30efbfb2d09ddc516668db0f3ec946ed5457cdd',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-my-jetpack',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-password-checker' => array(
            'pretty_version' => 'v0.4.8',
            'version' => '0.4.8.0',
            'reference' => '53c5d39afffd8fdf8001c24e7efc2b2b7760359a',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-password-checker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-plans' => array(
            'pretty_version' => 'v0.9.0',
            'version' => '0.9.0.0',
            'reference' => '4847396301e099e334cd4e24f2782825eaded5ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../automattic/jetpack-plans',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-plugins-installer' => array(
            'pretty_version' => 'v0.5.6',
            'version' => '0.5.6.0',
            'reference' => '98e55a8c362ea42ed48a9149151f4041ad3b6c42',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-plugins-installer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-protect' => array(
            'pretty_version' => 'dev-trunk',
            'version' => 'dev-trunk',
            'reference' => null,
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-protect-models' => array(
            'pretty_version' => 'v0.6.0',
            'version' => '0.6.0.0',
            'reference' => '22f924aec2aad3b2dabcc5d9758312d7446ae198',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-protect-models',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-protect-status' => array(
            'pretty_version' => 'v0.7.0',
            'version' => '0.7.0.0',
            'reference' => '0abffede51cad043673f38d820a2da7af41569b5',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-protect-status',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-redirect' => array(
            'pretty_version' => 'v3.0.8',
            'version' => '3.0.8.0',
            'reference' => '876617673a655797178a4c35d9676000829432df',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-redirect',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-roles' => array(
            'pretty_version' => 'v3.0.8',
            'version' => '3.0.8.0',
            'reference' => '96e09fc813ccf5e691f46297875d6b85b859c669',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-roles',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-status' => array(
            'pretty_version' => 'v6.0.0',
            'version' => '6.0.0.0',
            'reference' => '64c01a04c4cf330138495e34197c2bf99fd00b66',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-status',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-sync' => array(
            'pretty_version' => 'v4.16.0',
            'version' => '4.16.0.0',
            'reference' => '56520945866116cd6cfd8bf23a0a27de91256329',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-sync',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-transport-helper' => array(
            'pretty_version' => 'v0.3.2',
            'version' => '0.3.2.0',
            'reference' => 'f19eb1adfc795aea76a5f21f8b8744d057ac94db',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-transport-helper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-waf' => array(
            'pretty_version' => 'v0.27.1',
            'version' => '0.27.1.0',
            'reference' => '424f0ca5290810d1dc738550ca1a717218534fb7',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../../jetpack_vendor/automattic/jetpack-waf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wikimedia/aho-corasick' => array(
            'pretty_version' => 'v1.0.1',
            'version' => '1.0.1.0',
            'reference' => '2f3a1bd765913637a66eade658d11d82f0e551be',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wikimedia/aho-corasick',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
