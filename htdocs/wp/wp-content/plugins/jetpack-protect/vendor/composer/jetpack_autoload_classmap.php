<?php

// This file `jetpack_autoload_classmap.php` was auto generated by automattic/jetpack-autoloader.

$vendorDir = dirname(__DIR__);
$baseDir   = dirname($vendorDir);

return array(
	'AhoCorasick\\MultiStringMatcher' => array(
		'version' => '1.0.1.0',
		'path'    => $vendorDir . '/wikimedia/aho-corasick/src/MultiStringMatcher.php'
	),
	'AhoCorasick\\MultiStringReplacer' => array(
		'version' => '1.0.1.0',
		'path'    => $vendorDir . '/wikimedia/aho-corasick/src/MultiStringReplacer.php'
	),
	'Autoloader' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader.php'
	),
	'Autoloader_Handler' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader-handler.php'
	),
	'Autoloader_Locator' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader-locator.php'
	),
	'Automattic\\Jetpack\\A8c_Mc_Stats' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-a8c-mc-stats/src/class-a8c-mc-stats.php'
	),
	'Automattic\\Jetpack\\Account_Protection\\Account_Protection' => array(
		'version' => '0.2.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-account-protection/src/class-account-protection.php'
	),
	'Automattic\\Jetpack\\Account_Protection\\Config' => array(
		'version' => '0.2.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-account-protection/src/class-config.php'
	),
	'Automattic\\Jetpack\\Account_Protection\\Email_Service' => array(
		'version' => '0.2.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-account-protection/src/class-email-service.php'
	),
	'Automattic\\Jetpack\\Account_Protection\\Password_Detection' => array(
		'version' => '0.2.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-account-protection/src/class-password-detection.php'
	),
	'Automattic\\Jetpack\\Account_Protection\\Password_Manager' => array(
		'version' => '0.2.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-account-protection/src/class-password-manager.php'
	),
	'Automattic\\Jetpack\\Account_Protection\\Password_Strength_Meter' => array(
		'version' => '0.2.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-account-protection/src/class-password-strength-meter.php'
	),
	'Automattic\\Jetpack\\Account_Protection\\Settings' => array(
		'version' => '0.2.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-account-protection/src/class-settings.php'
	),
	'Automattic\\Jetpack\\Account_Protection\\Validation_Service' => array(
		'version' => '0.2.6.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-account-protection/src/class-validation-service.php'
	),
	'Automattic\\Jetpack\\Admin_UI\\Admin_Menu' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-admin-ui/src/class-admin-menu.php'
	),
	'Automattic\\Jetpack\\Assets' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-assets/src/class-assets.php'
	),
	'Automattic\\Jetpack\\Assets\\Logo' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-logo/src/class-logo.php'
	),
	'Automattic\\Jetpack\\Assets\\Script_Data' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-assets/src/class-script-data.php'
	),
	'Automattic\\Jetpack\\Assets\\Semver' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-assets/src/class-semver.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadFileWriter' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadFileWriter.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadGenerator' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadGenerator.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadProcessor' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadProcessor.php'
	),
	'Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/CustomAutoloaderPlugin.php'
	),
	'Automattic\\Jetpack\\Autoloader\\ManifestGenerator' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/ManifestGenerator.php'
	),
	'Automattic\\Jetpack\\Automatic_Install_Skin' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-plugins-installer/src/class-automatic-install-skin.php'
	),
	'Automattic\\Jetpack\\Backup\\V0005\\Helper_Script_Manager' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-backup-helper-script-manager/src/class-helper-script-manager.php'
	),
	'Automattic\\Jetpack\\Backup\\V0005\\Helper_Script_Manager_Impl' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-backup-helper-script-manager/src/class-helper-script-manager-impl.php'
	),
	'Automattic\\Jetpack\\Backup\\V0005\\Throw_On_Errors' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-backup-helper-script-manager/src/class-throw-on-errors.php'
	),
	'Automattic\\Jetpack\\Boost_Core\\Contracts\\Boost_API_Client' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/contracts/boost-api-client.php'
	),
	'Automattic\\Jetpack\\Boost_Core\\Lib\\Boost_API' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-boost-api.php'
	),
	'Automattic\\Jetpack\\Boost_Core\\Lib\\Cacheable' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-cacheable.php'
	),
	'Automattic\\Jetpack\\Boost_Core\\Lib\\Transient' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-transient.php'
	),
	'Automattic\\Jetpack\\Boost_Core\\Lib\\Url' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-url.php'
	),
	'Automattic\\Jetpack\\Boost_Core\\Lib\\Utils' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-utils.php'
	),
	'Automattic\\Jetpack\\Boost_Core\\Lib\\WPCOM_Boost_API_Client' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-core/src/lib/class-wpcom-boost-api-client.php'
	),
	'Automattic\\Jetpack\\Boost_Speed_Score\\Jetpack_Boost_Modules' => array(
		'version' => '0.4.10.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-jetpack-boost-modules.php'
	),
	'Automattic\\Jetpack\\Boost_Speed_Score\\Speed_Score' => array(
		'version' => '0.4.10.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-speed-score.php'
	),
	'Automattic\\Jetpack\\Boost_Speed_Score\\Speed_Score_Graph_History_Request' => array(
		'version' => '0.4.10.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-speed-score-graph-history-request.php'
	),
	'Automattic\\Jetpack\\Boost_Speed_Score\\Speed_Score_History' => array(
		'version' => '0.4.10.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-speed-score-history.php'
	),
	'Automattic\\Jetpack\\Boost_Speed_Score\\Speed_Score_Request' => array(
		'version' => '0.4.10.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-boost-speed-score/src/class-speed-score-request.php'
	),
	'Automattic\\Jetpack\\Composer\\Manager' => array(
		'version' => '4.0.5.0',
		'path'    => $vendorDir . '/automattic/jetpack-composer-plugin/src/class-manager.php'
	),
	'Automattic\\Jetpack\\Composer\\Plugin' => array(
		'version' => '4.0.5.0',
		'path'    => $vendorDir . '/automattic/jetpack-composer-plugin/src/class-plugin.php'
	),
	'Automattic\\Jetpack\\Config' => array(
		'version' => '3.1.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-config/src/class-config.php'
	),
	'Automattic\\Jetpack\\Connection\\Authorize_Json_Api' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-authorize-json-api.php'
	),
	'Automattic\\Jetpack\\Connection\\Client' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-client.php'
	),
	'Automattic\\Jetpack\\Connection\\Connection_Assets' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-connection-assets.php'
	),
	'Automattic\\Jetpack\\Connection\\Connection_Notice' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-connection-notice.php'
	),
	'Automattic\\Jetpack\\Connection\\Error_Handler' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-error-handler.php'
	),
	'Automattic\\Jetpack\\Connection\\Initial_State' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-initial-state.php'
	),
	'Automattic\\Jetpack\\Connection\\Manager' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-manager.php'
	),
	'Automattic\\Jetpack\\Connection\\Manager_Interface' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/interface-manager.php'
	),
	'Automattic\\Jetpack\\Connection\\Nonce_Handler' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-nonce-handler.php'
	),
	'Automattic\\Jetpack\\Connection\\Package_Version' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-package-version.php'
	),
	'Automattic\\Jetpack\\Connection\\Package_Version_Tracker' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-package-version-tracker.php'
	),
	'Automattic\\Jetpack\\Connection\\Plugin' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-plugin.php'
	),
	'Automattic\\Jetpack\\Connection\\Plugin_Storage' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-plugin-storage.php'
	),
	'Automattic\\Jetpack\\Connection\\REST_Connector' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-rest-connector.php'
	),
	'Automattic\\Jetpack\\Connection\\Rest_Authentication' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-rest-authentication.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-sso.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO\\Force_2FA' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-force-2fa.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO\\Helpers' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-helpers.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO\\Notices' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-notices.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO\\User_Admin' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/sso/class-user-admin.php'
	),
	'Automattic\\Jetpack\\Connection\\Secrets' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-secrets.php'
	),
	'Automattic\\Jetpack\\Connection\\Server_Sandbox' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-server-sandbox.php'
	),
	'Automattic\\Jetpack\\Connection\\Tokens' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-tokens.php'
	),
	'Automattic\\Jetpack\\Connection\\Tokens_Locks' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-tokens-locks.php'
	),
	'Automattic\\Jetpack\\Connection\\Traits\\WPCOM_REST_API_Proxy_Request' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/traits/trait-wpcom-rest-api-proxy-request.php'
	),
	'Automattic\\Jetpack\\Connection\\Urls' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-urls.php'
	),
	'Automattic\\Jetpack\\Connection\\User_Account_Status' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-user-account-status.php'
	),
	'Automattic\\Jetpack\\Connection\\Users_Connection_Admin' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-users-connection-admin.php'
	),
	'Automattic\\Jetpack\\Connection\\Utils' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-utils.php'
	),
	'Automattic\\Jetpack\\Connection\\Webhooks' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-webhooks.php'
	),
	'Automattic\\Jetpack\\Connection\\Webhooks\\Authorize_Redirect' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/webhooks/class-authorize-redirect.php'
	),
	'Automattic\\Jetpack\\Connection\\XMLRPC_Async_Call' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-xmlrpc-async-call.php'
	),
	'Automattic\\Jetpack\\Connection\\XMLRPC_Connector' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-xmlrpc-connector.php'
	),
	'Automattic\\Jetpack\\Constants' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-constants/src/class-constants.php'
	),
	'Automattic\\Jetpack\\CookieState' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-cookiestate.php'
	),
	'Automattic\\Jetpack\\Current_Plan' => array(
		'version' => '0.9.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-plans/src/class-current-plan.php'
	),
	'Automattic\\Jetpack\\Device_Detection' => array(
		'version' => '3.0.9.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-device-detection/src/class-device-detection.php'
	),
	'Automattic\\Jetpack\\Device_Detection\\User_Agent_Info' => array(
		'version' => '3.0.9.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-device-detection/src/class-user-agent-info.php'
	),
	'Automattic\\Jetpack\\Errors' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-errors.php'
	),
	'Automattic\\Jetpack\\ExPlat' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-explat/src/class-explat.php'
	),
	'Automattic\\Jetpack\\ExPlat\\REST_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-explat/src/class-rest-controller.php'
	),
	'Automattic\\Jetpack\\Files' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-files.php'
	),
	'Automattic\\Jetpack\\Heartbeat' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-heartbeat.php'
	),
	'Automattic\\Jetpack\\IP\\Utils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-ip/src/class-utils.php'
	),
	'Automattic\\Jetpack\\IdentityCrisis\\Exception' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-exception.php'
	),
	'Automattic\\Jetpack\\IdentityCrisis\\REST_Endpoints' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-rest-endpoints.php'
	),
	'Automattic\\Jetpack\\IdentityCrisis\\UI' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-ui.php'
	),
	'Automattic\\Jetpack\\IdentityCrisis\\URL_Secret' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-url-secret.php'
	),
	'Automattic\\Jetpack\\Identity_Crisis' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/identity-crisis/class-identity-crisis.php'
	),
	'Automattic\\Jetpack\\JITMS\\JITM' => array(
		'version' => '4.3.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-jitm/src/class-jitm.php'
	),
	'Automattic\\Jetpack\\JITMS\\Post_Connection_JITM' => array(
		'version' => '4.3.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-jitm/src/class-post-connection-jitm.php'
	),
	'Automattic\\Jetpack\\JITMS\\Pre_Connection_JITM' => array(
		'version' => '4.3.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-jitm/src/class-pre-connection-jitm.php'
	),
	'Automattic\\Jetpack\\JITMS\\Rest_Api_Endpoints' => array(
		'version' => '4.3.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-jitm/src/class-rest-api-endpoints.php'
	),
	'Automattic\\Jetpack\\Licensing' => array(
		'version' => '3.0.9.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-licensing/src/class-licensing.php'
	),
	'Automattic\\Jetpack\\Licensing\\Endpoints' => array(
		'version' => '3.0.9.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-licensing/src/class-endpoints.php'
	),
	'Automattic\\Jetpack\\Modules' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-modules.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Activitylog' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-activitylog.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Historically_Active_Modules' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-historically-active-modules.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Hybrid_Product' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-hybrid-product.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Initializer' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-initializer.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Jetpack_Manage' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-jetpack-manage.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Module_Product' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-module-product.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Product' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-product.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-products.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Anti_Spam' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-anti-spam.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Backup' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-backup.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Boost' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-boost.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Complete' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-complete.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Creator' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-creator.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Crm' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-crm.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Extras' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-extras.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Growth' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-growth.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Jetpack_Ai' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-jetpack-ai.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Newsletter' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-newsletter.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Protect' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-protect.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Related_Posts' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-related-posts.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Scan' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-scan.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Search' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-search.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Search_Stats' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-search-stats.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Security' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-security.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Site_Accelerator' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-site-accelerator.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Social' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-social.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Starter' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-starter.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Stats' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-stats.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Products\\Videopress' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/products/class-videopress.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\REST_AI' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-ai.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\REST_Products' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-products.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\REST_Purchases' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-purchases.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\REST_Recommendations_Evaluation' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-recommendations-evaluation.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\REST_Zendesk_Chat' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-rest-zendesk-chat.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Red_Bubble_Notifications' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-red-bubble-notifications.php'
	),
	'Automattic\\Jetpack\\My_Jetpack\\Wpcom_Products' => array(
		'version' => '5.21.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-my-jetpack/src/class-wpcom-products.php'
	),
	'Automattic\\Jetpack\\Partner' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-partner.php'
	),
	'Automattic\\Jetpack\\Partner_Coupon' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-partner-coupon.php'
	),
	'Automattic\\Jetpack\\Password_Checker' => array(
		'version' => '0.4.8.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-password-checker/src/class-password-checker.php'
	),
	'Automattic\\Jetpack\\Paths' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-paths.php'
	),
	'Automattic\\Jetpack\\Plans' => array(
		'version' => '0.9.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-plans/src/class-plans.php'
	),
	'Automattic\\Jetpack\\Plugins_Installer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-plugins-installer/src/class-plugins-installer.php'
	),
	'Automattic\\Jetpack\\Protect\\Credentials' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/class-credentials.php'
	),
	'Automattic\\Jetpack\\Protect\\Onboarding' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/class-onboarding.php'
	),
	'Automattic\\Jetpack\\Protect\\REST_Controller' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/class-rest-controller.php'
	),
	'Automattic\\Jetpack\\Protect\\Scan_History' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/class-scan-history.php'
	),
	'Automattic\\Jetpack\\Protect\\Site_Health' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/class-site-health.php'
	),
	'Automattic\\Jetpack\\Protect\\Threats' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/class-threats.php'
	),
	'Automattic\\Jetpack\\Protect_Models' => array(
		'version' => '0.6.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-protect-models.php'
	),
	'Automattic\\Jetpack\\Protect_Models\\Extension_Model' => array(
		'version' => '0.6.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-extension-model.php'
	),
	'Automattic\\Jetpack\\Protect_Models\\History_Model' => array(
		'version' => '0.6.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-history-model.php'
	),
	'Automattic\\Jetpack\\Protect_Models\\Status_Model' => array(
		'version' => '0.6.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-status-model.php'
	),
	'Automattic\\Jetpack\\Protect_Models\\Threat_Model' => array(
		'version' => '0.6.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-threat-model.php'
	),
	'Automattic\\Jetpack\\Protect_Models\\Vulnerability_Model' => array(
		'version' => '0.6.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-models/src/class-vulnerability-model.php'
	),
	'Automattic\\Jetpack\\Protect_Status\\Plan' => array(
		'version' => '0.7.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-plan.php'
	),
	'Automattic\\Jetpack\\Protect_Status\\Protect_Status' => array(
		'version' => '0.7.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-protect-status.php'
	),
	'Automattic\\Jetpack\\Protect_Status\\REST_Controller' => array(
		'version' => '0.7.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-rest-controller.php'
	),
	'Automattic\\Jetpack\\Protect_Status\\Scan_Status' => array(
		'version' => '0.7.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-scan-status.php'
	),
	'Automattic\\Jetpack\\Protect_Status\\Status' => array(
		'version' => '0.7.0.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-protect-status/src/class-status.php'
	),
	'Automattic\\Jetpack\\Redirect' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-redirect/src/class-redirect.php'
	),
	'Automattic\\Jetpack\\Roles' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-roles/src/class-roles.php'
	),
	'Automattic\\Jetpack\\Status' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-status.php'
	),
	'Automattic\\Jetpack\\Status\\Cache' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-cache.php'
	),
	'Automattic\\Jetpack\\Status\\Host' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-host.php'
	),
	'Automattic\\Jetpack\\Status\\Request' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-request.php'
	),
	'Automattic\\Jetpack\\Status\\Visitor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-status/src/class-visitor.php'
	),
	'Automattic\\Jetpack\\Sync\\Actions' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-actions.php'
	),
	'Automattic\\Jetpack\\Sync\\Codec_Interface' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/interface-codec.php'
	),
	'Automattic\\Jetpack\\Sync\\Data_Settings' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-data-settings.php'
	),
	'Automattic\\Jetpack\\Sync\\Dedicated_Sender' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-dedicated-sender.php'
	),
	'Automattic\\Jetpack\\Sync\\Default_Filter_Settings' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-default-filter-settings.php'
	),
	'Automattic\\Jetpack\\Sync\\Defaults' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-defaults.php'
	),
	'Automattic\\Jetpack\\Sync\\Functions' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-functions.php'
	),
	'Automattic\\Jetpack\\Sync\\Health' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-health.php'
	),
	'Automattic\\Jetpack\\Sync\\JSON_Deflate_Array_Codec' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-json-deflate-array-codec.php'
	),
	'Automattic\\Jetpack\\Sync\\Listener' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-listener.php'
	),
	'Automattic\\Jetpack\\Sync\\Lock' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-lock.php'
	),
	'Automattic\\Jetpack\\Sync\\Main' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-main.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-modules.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Attachments' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-attachments.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Callables' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-callables.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Comments' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-comments.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Constants' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-constants.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Full_Sync' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-full-sync.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Full_Sync_Immediately' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-full-sync-immediately.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Import' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-import.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Menus' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-menus.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Meta' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-meta.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Module' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-module.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Network_Options' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-network-options.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Options' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-options.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Plugins' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-plugins.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Posts' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-posts.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Protect' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-protect.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Search' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-search.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Stats' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-stats.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Term_Relationships' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-term-relationships.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Terms' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-terms.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Themes' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-themes.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Updates' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-updates.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Users' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-users.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\WP_Super_Cache' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-wp-super-cache.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\WooCommerce' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-woocommerce.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\WooCommerce_HPOS_Orders' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/modules/class-woocommerce-hpos-orders.php'
	),
	'Automattic\\Jetpack\\Sync\\Package_Version' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-package-version.php'
	),
	'Automattic\\Jetpack\\Sync\\Queue' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-queue.php'
	),
	'Automattic\\Jetpack\\Sync\\Queue\\Queue_Storage_Options' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/sync-queue/class-queue-storage-options.php'
	),
	'Automattic\\Jetpack\\Sync\\Queue\\Queue_Storage_Table' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/sync-queue/class-queue-storage-table.php'
	),
	'Automattic\\Jetpack\\Sync\\Queue_Buffer' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-queue-buffer.php'
	),
	'Automattic\\Jetpack\\Sync\\REST_Endpoints' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-rest-endpoints.php'
	),
	'Automattic\\Jetpack\\Sync\\REST_Sender' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-rest-sender.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-replicastore.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/replicastore/class-table-checksum.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum_Usermeta' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/replicastore/class-table-checksum-usermeta.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum_Users' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/replicastore/class-table-checksum-users.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore_Interface' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/interface-replicastore.php'
	),
	'Automattic\\Jetpack\\Sync\\Sender' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-sender.php'
	),
	'Automattic\\Jetpack\\Sync\\Server' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-server.php'
	),
	'Automattic\\Jetpack\\Sync\\Settings' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-settings.php'
	),
	'Automattic\\Jetpack\\Sync\\Simple_Codec' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-simple-codec.php'
	),
	'Automattic\\Jetpack\\Sync\\Users' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-users.php'
	),
	'Automattic\\Jetpack\\Sync\\Utils' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-sync/src/class-utils.php'
	),
	'Automattic\\Jetpack\\Terms_Of_Service' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-terms-of-service.php'
	),
	'Automattic\\Jetpack\\Tracking' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/src/class-tracking.php'
	),
	'Automattic\\Jetpack\\Transport_Helper\\Package_Version' => array(
		'version' => '0.3.2.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-transport-helper/src/class-package-version.php'
	),
	'Automattic\\Jetpack\\Transport_Helper\\V0001\\Package_Version' => array(
		'version' => '0.3.2.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-transport-helper/src/class-package-version-compat.php'
	),
	'Automattic\\Jetpack\\Transport_Helper\\V0005\\REST_Controller' => array(
		'version' => '0.3.2.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-transport-helper/src/class-rest-controller.php'
	),
	'Automattic\\Jetpack\\Waf\\Blocked_Login_Page' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/abstract-blocked-login-page.php'
	),
	'Automattic\\Jetpack\\Waf\\Brute_Force_Protection\\Brute_Force_Protection' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-brute-force-protection.php'
	),
	'Automattic\\Jetpack\\Waf\\Brute_Force_Protection\\Brute_Force_Protection_Blocked_Login_Page' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/brute-force-protection/class-brute-force-protection-blocked-login-page.php'
	),
	'Automattic\\Jetpack\\Waf\\Brute_Force_Protection\\Brute_Force_Protection_Math_Authenticate' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/brute-force-protection/class-math-fallback.php'
	),
	'Automattic\\Jetpack\\Waf\\Brute_Force_Protection\\Brute_Force_Protection_Shared_Functions' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/brute-force-protection/class-shared-functions.php'
	),
	'Automattic\\Jetpack\\Waf\\Brute_Force_Protection\\Brute_Force_Protection_Transient_Cleanup' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/brute-force-protection/class-transient-cleanup.php'
	),
	'Automattic\\Jetpack\\Waf\\CLI' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-waf-cli.php'
	),
	'Automattic\\Jetpack\\Waf\\File_System_Exception' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/exceptions/class-file-system-exception.php'
	),
	'Automattic\\Jetpack\\Waf\\REST_Controller' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-rest-controller.php'
	),
	'Automattic\\Jetpack\\Waf\\Rules_API_Exception' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/exceptions/class-rules-api-exception.php'
	),
	'Automattic\\Jetpack\\Waf\\Waf_Blocked_Login_Page' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-waf-blocked-login-page.php'
	),
	'Automattic\\Jetpack\\Waf\\Waf_Blocklog_Manager' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-waf-blocklog-manager.php'
	),
	'Automattic\\Jetpack\\Waf\\Waf_Compatibility' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-compatibility.php'
	),
	'Automattic\\Jetpack\\Waf\\Waf_Constants' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-waf-constants.php'
	),
	'Automattic\\Jetpack\\Waf\\Waf_Exception' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/exceptions/class-waf-exception.php'
	),
	'Automattic\\Jetpack\\Waf\\Waf_Initializer' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-waf-initializer.php'
	),
	'Automattic\\Jetpack\\Waf\\Waf_Operators' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-waf-operators.php'
	),
	'Automattic\\Jetpack\\Waf\\Waf_Request' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-waf-request.php'
	),
	'Automattic\\Jetpack\\Waf\\Waf_Rules_Manager' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-waf-rules-manager.php'
	),
	'Automattic\\Jetpack\\Waf\\Waf_Runner' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-waf-runner.php'
	),
	'Automattic\\Jetpack\\Waf\\Waf_Runtime' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-waf-runtime.php'
	),
	'Automattic\\Jetpack\\Waf\\Waf_Standalone_Bootstrap' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-waf-standalone-bootstrap.php'
	),
	'Automattic\\Jetpack\\Waf\\Waf_Stats' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-waf-stats.php'
	),
	'Automattic\\Jetpack\\Waf\\Waf_Transforms' => array(
		'version' => '0.27.1.0',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-waf/src/class-waf-transforms.php'
	),
	'Container' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-container.php'
	),
	'Hook_Manager' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-hook-manager.php'
	),
	'Jetpack_IXR_Client' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-ixr-client.php'
	),
	'Jetpack_IXR_ClientMulticall' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-ixr-clientmulticall.php'
	),
	'Jetpack_Options' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-options.php'
	),
	'Jetpack_Protect' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/class-jetpack-protect.php'
	),
	'Jetpack_Signature' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-signature.php'
	),
	'Jetpack_Tracks_Client' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-tracks-client.php'
	),
	'Jetpack_Tracks_Event' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-tracks-event.php'
	),
	'Jetpack_XMLRPC_Server' => array(
		'version' => '********',
		'path'    => $baseDir . '/jetpack_vendor/automattic/jetpack-connection/legacy/class-jetpack-xmlrpc-server.php'
	),
	'Latest_Autoloader_Guard' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-latest-autoloader-guard.php'
	),
	'Manifest_Reader' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-manifest-reader.php'
	),
	'PHP_Autoloader' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-php-autoloader.php'
	),
	'Path_Processor' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-path-processor.php'
	),
	'Plugin_Locator' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-plugin-locator.php'
	),
	'Plugins_Handler' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-plugins-handler.php'
	),
	'Shutdown_Handler' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-shutdown-handler.php'
	),
	'Version_Loader' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-version-loader.php'
	),
	'Version_Selector' => array(
		'version' => '5.0.9',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-version-selector.php'
	),
);
