{"name": "automattic/jetpack-boost-speed-score", "description": "A package that handles the API to generate the speed score.", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require-dev": {"yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-changelogger": "^6.0.5", "brain/monkey": "^2.6", "automattic/phpunit-select-config": "^1.0.3"}, "autoload-dev": {"psr-4": {"Automattic\\Jetpack\\Boost_Speed_Score\\Tests\\": "./tests/php"}}, "require": {"php": ">=7.2", "automattic/jetpack-boost-core": "^0.3.11"}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": ["php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\""], "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"mirror-repo": "Automattic/jetpack-boost-speed-score", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-boost-speed-score/compare/v${old}...v${new}"}, "autotagger": true, "branch-alias": {"dev-trunk": "0.4.x-dev"}, "textdomain": "jetpack-boost-speed-score", "version-constants": {"::PACKAGE_VERSION": "src/class-speed-score.php"}}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}}