{"name": "automattic/jetpack-connection", "description": "Everything needed to connect to the Jetpack infrastructure", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"php": ">=7.2", "automattic/jetpack-a8c-mc-stats": "^3.0.5", "automattic/jetpack-admin-ui": "^0.5.10", "automattic/jetpack-assets": "^4.3.1", "automattic/jetpack-constants": "^3.0.8", "automattic/jetpack-roles": "^3.0.8", "automattic/jetpack-status": "^6.0.0", "automattic/jetpack-redirect": "^3.0.8"}, "require-dev": {"automattic/jetpack-test-environment": "@dev", "yoast/phpunit-polyfills": "^4.0.0", "brain/monkey": "^2.6.2", "automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"files": ["actions.php"], "classmap": ["legacy", "src/", "src/webhooks", "src/identity-crisis"]}, "scripts": {"build-production": ["pnpm run build-production"], "build-development": ["pnpm run build"], "phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": ["php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\""], "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-connection", "textdomain": "jetpack-connection", "version-constants": {"::PACKAGE_VERSION": "src/class-package-version.php"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-connection/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "6.16.x-dev"}, "dependencies": {"test-only": ["packages/licensing", "packages/sync"]}}, "config": {"allow-plugins": {"roots/wordpress-core-installer": true}}}