!function(e,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.JetpackConnection=n():e.JetpackConnection=n()}(globalThis,(()=>(()=>{var e={6854:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o={error:"TcCZnGE6mad8Dvz9pCZi",button:"_mn6o2Dtm5pfFWc8_A1K"}},4659:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o={button:"zI5tJ_qhWE6Oe6Lk75GY","is-icon-button":"tuBt2DLqimiImoqVzPqo",small:"Na39I683LAaSA99REg14",normal:"ipS7tKy9GntCS4R3vekF",icon:"paGLQwtPEaJmtArCcmyK",regular:"lZAo6_oGfclXOO9CC6Rd","full-width":"xJDOiJxTt0R_wSl8Ipz_",loading:"q_tVWqMjl39RcY6WtQA6","external-icon":"CDuBjJp_8jxzx5j6Nept"}},2990:()=>{},7773:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o={placeholder:"NisihrgiIKl_knpYJtfg",pulse:"R2i0K45dEF157drbVRPI"}},5404:()=>{},2144:()=>{},4099:()=>{},3370:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o={reset:"WQVtrU6q0L1Igcj7wCrQ","headline-medium":"UujoBFTnQNY2cWU2SIsH","headline-small":"TeGO5V_thHw5lDAm1_2M","headline-small-regular":"WolQzb2MsSgiNmLtc7_j","title-medium":"hUB0JT8p1T2Hw28N6qC8","title-medium-semi-bold":"gKZWDv5chz3_O3Syp74H","title-small":"zY2No8Ga4b8shbOQGhnv",body:"tIj0D1t8Cc892ikmgFPZ","body-small":"KdcN0BnOaVeVhyLRKqhS","body-extra-small":"dso3Rh3tl3Xv1GumBktz","body-extra-small-bold":"mQ1UlbN9u4Mg9byO8m7v",label:"PItlW5vRExLnTj4a8eLE","m-0":"TwRpPlktzxhmFVeua7P5","mx-0":"zVfqx7gyb3o9mxfGynn1","my-0":"iSHVzNiB9iVleGljaQxy","mt-0":"xqDIp6cNVr_E6RXaiPyD","mr-0":"S8EwaXk1kyPizt6x4WH2","mb-0":"ODX5Vr1TARoLFkDDFooD","ml-0":"cphJ8dCpfimnky7P2FHg","m-1":"PFgIhNxIyiSuNvQjAIYj","mx-1":"M2jKmUzDxvJjjVEPU3zn","my-1":"io15gAh8tMTNbSEfwJKk","mt-1":"rcTN5uw9xIEeMEGL3Xi_","mr-1":"CQSkybjq2TcRM1Xo9COV","mb-1":"hfqOWgq6_MEGdFE82eOY","ml-1":"I8MxZQYTbuu595yfesWA","m-2":"kQkc6rmdpvLKPkyoJtVQ","mx-2":"j6vFPxWuu4Jan2ldoxpp","my-2":"hqr39dC4H_AbactPAkCG","mt-2":"c3dQnMi16C6J6Ecy4283","mr-2":"YNZmHOuRo6hU7zzKfPdP","mb-2":"Db8lbak1_wunpPk8NwKU","ml-2":"ftsYE5J9hLzquQ0tA5dY","m-3":"Det4MHzLUW7EeDnafPzq","mx-3":"h_8EEAztC29Vve1datb5","my-3":"YXIXJ0h1k47u6hzK8KcM","mt-3":"soADBBkcIKCBXzCTuV9_","mr-3":"zSX59ziEaEWGjnpZa4uV","mb-3":"yrVTnq_WBMbejg89c2ZQ","ml-3":"UKtHPJnI2cXBWtPDm5hM","m-4":"guexok_Tqd5Tf52hRlbT","mx-4":"oS1E2KfTBZkJ3F0tN7T6","my-4":"DN1OhhXi6AoBgEdDSbGd","mt-4":"ot2kkMcYHv53hLZ4LSn0","mr-4":"A1krOZZhlQ6Sp8Cy4bly","mb-4":"pkDbXXXL32237M0hokEh","ml-4":"XXv4kDTGvEnQeuGKOPU3","m-5":"yGqHk1a57gaISwkXwXe6","mx-5":"X8cghM358X3DkXLc9aNK","my-5":"GdfSmGwHlFnN2S6xBn1f","mt-5":"yqeuzwyGQ7zG0avrGqi_","mr-5":"g9emeCkuHvYhveiJbfXO","mb-5":"Lvk3dqcyHbZ07QCRlrUQ","ml-5":"r3yQECDQ9qX0XZzXlVAg","m-6":"aQhlPwht2Cz1X_63Miw0","mx-6":"JyHb0vK3wJgpblL9s5j8","my-6":"cY2gULL1lAv6WPNIRuf3","mt-6":"NBWQ9Lwhh_fnry3lg_p7","mr-6":"yIOniNe5E40C8fWvBm5V","mb-6":"t30usboNSyqfQWIwHvT3","ml-6":"Nm_TyFkYCMhOoghoToKJ","m-7":"C4qJKoBXpgKtpmrqtEKB","mx-7":"S93Srbu6NQ_PBr7DmTiD","my-7":"fJj8k6gGJDks3crUZxOS","mt-7":"cW6D6djs7Ppm7fD7TeoV","mr-7":"DuCnqNfcxcP3Z__Yo5Ro","mb-7":"im8407m2fw5vOg7O2zsw","ml-7":"G0fbeBgvz2sh3uTP9gNl","m-8":"kvW3sBCxRxUqz1jrVMJl","mx-8":"tOjEqjLONQdkiYx_XRnw","my-8":"op5hFSx318zgxsoZZNLN","mt-8":"c9WfNHP6TFKWIfLxv52J","mr-8":"sBA75QqcqRwwYSHJh2wc","mb-8":"GpL6idrXmSOM6jB8Ohsf","ml-8":"HbtWJoQwpgGycz8dGzeT","p-0":"uxX3khU88VQ_Ah49Ejsa","px-0":"KX0FhpBKwKzs9fOUdbNz","py-0":"PfK8vKDyN32dnimlzYjz","pt-0":"emxLHRjQuJsImnPbQIzE","pr-0":"kJ8WzlpTVgdViXt8ukP9","pb-0":"tg_UIUI11VBzrTAn2AzJ","pl-0":"uczvl8kaz84oPQJ2DB2R","p-1":"o7UHPcdVK3lt7q3lqV4o","px-1":"IDqEOxvDoYrFYxELPmtX","py-1":"DdywPW2qSYlu2pt8tpO2","pt-1":"npy3hw4A5QSkDicb2CJJ","pr-1":"LgbptTApNY5NwLQvEFAt","pb-1":"WZQy2SZuZso59bUsXXyl","pl-1":"o331apInxNunbYB3SfPE","p-2":"fMPIyD9Vqki1Lrc_yJnG","px-2":"i2pMcTcdrr10IQoiSm_L","py-2":"eA702gn32kwptiI1obXH","pt-2":"o9bGieUKcYc8o0Ij9oZX","pr-2":"SwZcFez1RDqWsOFjB5iG","pb-2":"eHpLc_idmuEqeqCTvqkN","pl-2":"vU39i2B4P1fUTMB2l6Vo","p-3":"JHWNzBnE29awhdu5BEh1","px-3":"X72lGbb56L3KFzC2xQ9N","py-3":"BzfNhRG8wXdCEB5ocQ6e","pt-3":"srV0KSDC83a2fiimSMMQ","pr-3":"lUWfkmbQjCskhcNwkyCm","pb-3":"Ts0dIlc3aTSL7V4cIHis","pl-3":"CzlqQXXhX6MvorArFZ8B","p-4":"TqMPkQtR_DdZuKb5vBoV","px-4":"a7UrjhI69Vetlcj9ZVzz","py-4":"StEhBzGs2Gi5dDEkjhAv","pt-4":"FGneZfZyvYrt1dG0zcnm","pr-4":"APEH216rpdlJWgD2fHc8","pb-4":"oGwXC3ohCic9XnAj6x69","pl-4":"U6gnT9y42ViPNOcNzBwb","p-5":"IpdRLBwnHqbqFrixgbYC","px-5":"HgNeXvkBa9o3bQ5fvFZm","py-5":"tJtFZM3XfPG9v9TSDfN1","pt-5":"PdifHW45QeXYfK568uD8","pr-5":"mbLkWTTZ0Za_BBbFZ5b2","pb-5":"vVWpZpLlWrkTt0hMk8XU","pl-5":"RxfaJj5a1Nt6IavEo5Zl","p-6":"SppJULDGdnOGcjZNCYBy","px-6":"palY2nLwdoyooPUm9Hhk","py-6":"WYw1JvZC0ppLdvSAPhr_","pt-6":"YEEJ9b90ueQaPfiU8aeN","pr-6":"QE0ssnsKvWJMqlhPbY5u","pb-6":"n8yA3jHlMRyLd5UIfoND","pl-6":"tXHmxYnHzbwtfxEaG51n","p-7":"kBTsPKkO_3g_tLkj77Um","px-7":"RyhrFx6Y1FGDrGAAyaxm","py-7":"CBwRpB0bDN3iEdQPPMJO","pt-7":"vQVSq6SvWKbOMu6r4H6b","pr-7":"oBy5__aEADMsH46mrgFX","pb-7":"KVEXoJqf1s92j0JMdNmN","pl-7":"ZMXGNrNaKW3k_3TLz0Fq","p-8":"tuiR9PhkHXhGyEgzRZRI","px-8":"U7454qyWkQNa2iaSJziu","py-8":"VLYIv2GVocjuN93e8HC8","pt-8":"X1rm9DQ1zLGLfogja5Gn","pr-8":"JS7G6kAuqJo5GIuF8S5t","pb-8":"Y8F9ga1TDCMbM1lj4gUz","pl-8":"AJuyNGrI63BOWql719H8"}},7627:()=>{},1133:()=>{},1590:()=>{},9910:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o={heading:"urouayitSUT8zW0V3p_0",notice:"iXXJlk08gFDeCvsTTlNQ",button:"MWqRqr7q6fgvLxitcWYk",primary:"qExIAscWqEKaVy2cSTqb",secondary:"fh6gO3o64bHDWoktxV1A",actions:"ix1awakl4n7EjEZdShcd",error:"e6hHy8BZ7ZKPSXbIC0UG",message:"jXz8LnXNzMDdtHqkG0sZ"}},177:()=>{},3524:()=>{},7556:()=>{},4317:()=>{},4046:()=>{},7202:()=>{},7750:(e,n,t)=>{"use strict";t.d(n,{A:()=>s});var o=t(6087);const s=(0,o.forwardRef)((function({icon:e,size:n=24,...t},s){return(0,o.cloneElement)(e,{width:n,height:n,...t,ref:s})}))},1386:(e,n,t)=>{"use strict";t.d(n,{A:()=>c});var o=t(5573),s=t(790);const c=(0,s.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(o.Path,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"})})},8391:(e,n,t)=>{"use strict";t.d(n,{A:()=>c});var o=t(5573),s=t(790);const c=(0,s.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(o.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})})},4804:(e,n,t)=>{n.formatArgs=function(n){if(n[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+n[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const t="color: "+this.color;n.splice(1,0,t,"color: inherit");let o=0,s=0;n[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(o++,"%c"===e&&(s=o))})),n.splice(s,0,t)},n.save=function(e){try{e?n.storage.setItem("debug",e):n.storage.removeItem("debug")}catch(e){}},n.load=function(){let e;try{e=n.storage.getItem("debug")||n.storage.getItem("DEBUG")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},n.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},n.storage=function(){try{return localStorage}catch(e){}}(),n.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),n.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],n.log=console.debug||console.log||(()=>{}),e.exports=t(5067)(n);const{formatters:o}=e.exports;o.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},5067:(e,n,t)=>{e.exports=function(e){function n(e){let t,s,c,i=null;function r(...e){if(!r.enabled)return;const o=r,s=Number(new Date),c=s-(t||s);o.diff=c,o.prev=t,o.curr=s,t=s,e[0]=n.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((t,s)=>{if("%%"===t)return"%";i++;const c=n.formatters[s];if("function"==typeof c){const n=e[i];t=c.call(o,n),e.splice(i,1),i--}return t})),n.formatArgs.call(o,e);(o.log||n.log).apply(o,e)}return r.namespace=e,r.useColors=n.useColors(),r.color=n.selectColor(e),r.extend=o,r.destroy=n.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(s!==n.namespaces&&(s=n.namespaces,c=n.enabled(e)),c),set:e=>{i=e}}),"function"==typeof n.init&&n.init(r),r}function o(e,t){const o=n(this.namespace+(void 0===t?":":t)+e);return o.log=this.log,o}function s(e,n){let t=0,o=0,s=-1,c=0;for(;t<e.length;)if(o<n.length&&(n[o]===e[t]||"*"===n[o]))"*"===n[o]?(s=o,c=t,o++):(t++,o++);else{if(-1===s)return!1;o=s+1,c++,t=c}for(;o<n.length&&"*"===n[o];)o++;return o===n.length}return n.debug=n,n.default=n,n.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},n.disable=function(){const e=[...n.names,...n.skips.map((e=>"-"+e))].join(",");return n.enable(""),e},n.enable=function(e){n.save(e),n.namespaces=e,n.names=[],n.skips=[];const t=("string"==typeof e?e:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(const e of t)"-"===e[0]?n.skips.push(e.slice(1)):n.names.push(e)},n.enabled=function(e){for(const t of n.skips)if(s(e,t))return!1;for(const t of n.names)if(s(e,t))return!0;return!1},n.humanize=t(3594),n.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((t=>{n[t]=e[t]})),n.names=[],n.skips=[],n.formatters={},n.selectColor=function(e){let t=0;for(let n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return n.colors[Math.abs(t)%n.colors.length]},n.enable(n.load()),n}},3594:e=>{var n=1e3,t=60*n,o=60*t,s=24*o,c=7*s,i=365.25*s;function r(e,n,t,o){var s=n>=1.5*t;return Math.round(e/t)+" "+o+(s?"s":"")}e.exports=function(e,a){a=a||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var r=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!r)return;var a=parseFloat(r[1]);switch((r[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return a*i;case"weeks":case"week":case"w":return a*c;case"days":case"day":case"d":return a*s;case"hours":case"hour":case"hrs":case"hr":case"h":return a*o;case"minutes":case"minute":case"mins":case"min":case"m":return a*t;case"seconds":case"second":case"secs":case"sec":case"s":return a*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}(e);if("number"===l&&isFinite(e))return a.long?function(e){var c=Math.abs(e);if(c>=s)return r(e,c,s,"day");if(c>=o)return r(e,c,o,"hour");if(c>=t)return r(e,c,t,"minute");if(c>=n)return r(e,c,n,"second");return e+" ms"}(e):function(e){var c=Math.abs(e);if(c>=s)return Math.round(e/s)+"d";if(c>=o)return Math.round(e/o)+"h";if(c>=t)return Math.round(e/t)+"m";if(c>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},1583:(e,n,t)=>{"use strict";var o=t(1752);function s(){}function c(){}c.resetWarningCache=s,e.exports=function(){function e(e,n,t,s,c,i){if(i!==o){var r=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw r.name="Invariant Violation",r}}function n(){return e}e.isRequired=e;var t={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:n,element:e,elementType:e,instanceOf:n,node:e,objectOf:n,oneOf:n,oneOfType:n,shape:n,exact:n,checkPropTypes:c,resetWarningCache:s};return t.PropTypes=t,t}},3619:(e,n,t)=>{e.exports=t(1583)()},1752:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},372:(e,n,t)=>{"use strict";t.d(n,{A:()=>a});var o=t(4804);const s=t.n(o)()("dops:analytics");let c,i;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const r={initialize:function(e,n,t){r.setUser(e,n),r.setSuperProps(t),r.identifyUser()},setGoogleAnalyticsEnabled:function(e,n=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=n},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,n){i={ID:e,username:n}},setSuperProps:function(e){c=e},assignSuperProps:function(e){c=Object.assign(c||{},e)},mc:{bumpStat:function(e,n){const t=function(e,n){let t="";if("object"==typeof e){for(const n in e)t+="&x_"+encodeURIComponent(n)+"="+encodeURIComponent(e[n]);s("Bumping stats %o",e)}else t="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(n),s('Bumping stat "%s" in group "%s"',n,e);return t}(e,n);r.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+t+"&t="+Math.random())},bumpStatWithPageView:function(e,n){const t=function(e,n){let t="";if("object"==typeof e){for(const n in e)t+="&"+encodeURIComponent(n)+"="+encodeURIComponent(e[n]);s("Built stats %o",e)}else t="&"+encodeURIComponent(e)+"="+encodeURIComponent(n),s('Built stat "%s" in group "%s"',n,e);return t}(e,n);r.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+t+"&t="+Math.random())}},pageView:{record:function(e,n){r.tracks.recordPageView(e),r.ga.recordPageView(e,n)}},purchase:{record:function(e,n,t,o,s,c,i){r.ga.recordPurchase(e,n,t,o,s,c,i)}},tracks:{recordEvent:function(e,n){n=n||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(c&&(s("- Super Props: %o",c),n=Object.assign(n,c)),s('Record event "%s" called with props %s',e,JSON.stringify(n)),window._tkq.push(["recordEvent",e,n])):s('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const n="object"==typeof e?e:{target:e};r.tracks.recordEvent("jetpack_wpa_click",n)},recordPageView:function(e){r.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){s("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};r.ga.initialized||(i&&(e={userId:"u-"+i.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),r.ga.initialized=!0)},recordPageView:function(e,n){r.ga.initialize(),s("Recording Page View ~ [URL: "+e+"] [Title: "+n+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:n}))},recordEvent:function(e,n,t,o){r.ga.initialize();let c="Recording Event ~ [Category: "+e+"] [Action: "+n+"]";void 0!==t&&(c+=" [Option Label: "+t+"]"),void 0!==o&&(c+=" [Option Value: "+o+"]"),s(c),this.googleAnalyticsEnabled&&window.ga("send","event",e,n,t,o)},recordPurchase:function(e,n,t,o,s,c,i){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:o,currency:i}),window.ga("ecommerce:addItem",{id:e,name:n,sku:t,price:s,quantity:c}),window.ga("ecommerce:send")}},identifyUser:function(){i&&window._tkq.push(["identifyUser",i.ID,i.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},a=r},5932:(e,n,t)=>{"use strict";t.d(n,{Ay:()=>p});var o=t(6439),s=t(3832);function c(e){class n extends Error{constructor(...n){super(...n),this.name=e}}return n}const i=c("JsonParseError"),r=c("JsonParseAfterRedirectError"),a=c("Api404Error"),l=c("Api404AfterRedirectError"),d=c("FetchNetworkError");const p=new function(e,n){let t=e,c=e,i={"X-WP-Nonce":n},r={credentials:"same-origin",headers:i},a={method:"post",credentials:"same-origin",headers:Object.assign({},i,{"Content-type":"application/json"})},l=function(e){const n=e.split("?"),t=n.length>1?n[1]:"",o=t.length?t.split("&"):[];return o.push("_cacheBuster="+(new Date).getTime()),n[0]+"?"+o.join("&")};const d={setApiRoot(e){t=e},setWpcomOriginApiUrl(e){c=e},setApiNonce(e){i={"X-WP-Nonce":e},r={credentials:"same-origin",headers:i},a={method:"post",credentials:"same-origin",headers:Object.assign({},i,{"Content-type":"application/json"})}},setCacheBusterCallback:e=>{l=e},registerSite:(e,n,s)=>{const c={};return(0,o.jetpackConfigHas)("consumer_slug")&&(c.plugin_slug=(0,o.jetpackConfigGet)("consumer_slug")),null!==n&&(c.redirect_uri=n),s&&(c.from=s),g(`${t}jetpack/v4/connection/register`,a,{body:JSON.stringify(c)}).then(u).then(m)},fetchAuthorizationUrl:e=>p((0,s.addQueryArgs)(`${t}jetpack/v4/connection/authorize_url`,{no_iframe:"1",redirect_uri:e}),r).then(u).then(m),fetchSiteConnectionData:()=>p(`${t}jetpack/v4/connection/data`,r).then(m),fetchSiteConnectionStatus:()=>p(`${t}jetpack/v4/connection`,r).then(m),fetchSiteConnectionTest:()=>p(`${t}jetpack/v4/connection/test`,r).then(u).then(m),fetchUserConnectionData:()=>p(`${t}jetpack/v4/connection/data`,r).then(m),fetchUserTrackingSettings:()=>p(`${t}jetpack/v4/tracking/settings`,r).then(u).then(m),updateUserTrackingSettings:e=>g(`${t}jetpack/v4/tracking/settings`,a,{body:JSON.stringify(e)}).then(u).then(m),disconnectSite:()=>g(`${t}jetpack/v4/connection`,a,{body:JSON.stringify({isActive:!1})}).then(u).then(m),fetchConnectUrl:()=>p(`${t}jetpack/v4/connection/url`,r).then(u).then(m),unlinkUser:(e=!1,n={})=>{const o={linked:!1,force:!!e};return n.disconnectAllUsers&&(o["disconnect-all-users"]=!0),g(`${t}jetpack/v4/connection/user`,a,{body:JSON.stringify(o)}).then(u).then(m)},reconnect:()=>g(`${t}jetpack/v4/connection/reconnect`,a).then(u).then(m),fetchConnectedPlugins:()=>p(`${t}jetpack/v4/connection/plugins`,r).then(u).then(m),setHasSeenWCConnectionModal:()=>g(`${t}jetpack/v4/seen-wc-connection-modal`,a).then(u).then(m),fetchModules:()=>p(`${t}jetpack/v4/module/all`,r).then(u).then(m),fetchModule:e=>p(`${t}jetpack/v4/module/${e}`,r).then(u).then(m),activateModule:e=>g(`${t}jetpack/v4/module/${e}/active`,a,{body:JSON.stringify({active:!0})}).then(u).then(m),deactivateModule:e=>g(`${t}jetpack/v4/module/${e}/active`,a,{body:JSON.stringify({active:!1})}),updateModuleOptions:(e,n)=>g(`${t}jetpack/v4/module/${e}`,a,{body:JSON.stringify(n)}).then(u).then(m),updateSettings:e=>g(`${t}jetpack/v4/settings`,a,{body:JSON.stringify(e)}).then(u).then(m),getProtectCount:()=>p(`${t}jetpack/v4/module/protect/data`,r).then(u).then(m),resetOptions:e=>g(`${t}jetpack/v4/options/${e}`,a,{body:JSON.stringify({reset:!0})}).then(u).then(m),activateVaultPress:()=>g(`${t}jetpack/v4/plugins`,a,{body:JSON.stringify({slug:"vaultpress",status:"active"})}).then(u).then(m),getVaultPressData:()=>p(`${t}jetpack/v4/module/vaultpress/data`,r).then(u).then(m),installPlugin:(e,n)=>{const o={slug:e,status:"active"};return n&&(o.source=n),g(`${t}jetpack/v4/plugins`,a,{body:JSON.stringify(o)}).then(u).then(m)},activateAkismet:()=>g(`${t}jetpack/v4/plugins`,a,{body:JSON.stringify({slug:"akismet",status:"active"})}).then(u).then(m),getAkismetData:()=>p(`${t}jetpack/v4/module/akismet/data`,r).then(u).then(m),checkAkismetKey:()=>p(`${t}jetpack/v4/module/akismet/key/check`,r).then(u).then(m),checkAkismetKeyTyped:e=>g(`${t}jetpack/v4/module/akismet/key/check`,a,{body:JSON.stringify({api_key:e})}).then(u).then(m),getFeatureTypeStatus:e=>p(`${t}jetpack/v4/feature/${e}`,r).then(u).then(m),fetchStatsData:e=>p(function(e){let n=`${t}jetpack/v4/module/stats/data`;-1!==n.indexOf("?")?n+=`&range=${encodeURIComponent(e)}`:n+=`?range=${encodeURIComponent(e)}`;return n}(e),r).then(u).then(m).then(_),getPluginUpdates:()=>p(`${t}jetpack/v4/updates/plugins`,r).then(u).then(m),getPlans:()=>p(`${t}jetpack/v4/plans`,r).then(u).then(m),fetchSettings:()=>p(`${t}jetpack/v4/settings`,r).then(u).then(m),updateSetting:e=>g(`${t}jetpack/v4/settings`,a,{body:JSON.stringify(e)}).then(u).then(m),fetchSiteData:()=>p(`${t}jetpack/v4/site`,r).then(u).then(m).then((e=>JSON.parse(e.data))),fetchSiteFeatures:()=>p(`${t}jetpack/v4/site/features`,r).then(u).then(m).then((e=>JSON.parse(e.data))),fetchSiteProducts:()=>p(`${t}jetpack/v4/site/products`,r).then(u).then(m),fetchSitePurchases:()=>p(`${t}jetpack/v4/site/purchases`,r).then(u).then(m).then((e=>JSON.parse(e.data))),fetchSiteBenefits:()=>p(`${t}jetpack/v4/site/benefits`,r).then(u).then(m).then((e=>JSON.parse(e.data))),fetchSiteDiscount:()=>p(`${t}jetpack/v4/site/discount`,r).then(u).then(m).then((e=>e.data)),fetchSetupQuestionnaire:()=>p(`${t}jetpack/v4/setup/questionnaire`,r).then(u).then(m),fetchRecommendationsData:()=>p(`${t}jetpack/v4/recommendations/data`,r).then(u).then(m),fetchRecommendationsProductSuggestions:()=>p(`${t}jetpack/v4/recommendations/product-suggestions`,r).then(u).then(m),fetchRecommendationsUpsell:()=>p(`${t}jetpack/v4/recommendations/upsell`,r).then(u).then(m),fetchRecommendationsConditional:()=>p(`${t}jetpack/v4/recommendations/conditional`,r).then(u).then(m),saveRecommendationsData:e=>g(`${t}jetpack/v4/recommendations/data`,a,{body:JSON.stringify({data:e})}).then(u),fetchProducts:()=>p(`${t}jetpack/v4/products`,r).then(u).then(m),fetchRewindStatus:()=>p(`${t}jetpack/v4/rewind`,r).then(u).then(m).then((e=>JSON.parse(e.data))),fetchScanStatus:()=>p(`${t}jetpack/v4/scan`,r).then(u).then(m).then((e=>JSON.parse(e.data))),dismissJetpackNotice:e=>g(`${t}jetpack/v4/notice/${e}`,a,{body:JSON.stringify({dismissed:!0})}).then(u).then(m),fetchPluginsData:()=>p(`${t}jetpack/v4/plugins`,r).then(u).then(m),fetchIntroOffers:()=>p(`${t}jetpack/v4/intro-offers`,r).then(u).then(m),fetchVerifySiteGoogleStatus:e=>p(null!==e?`${t}jetpack/v4/verify-site/google/${e}`:`${t}jetpack/v4/verify-site/google`,r).then(u).then(m),verifySiteGoogle:e=>g(`${t}jetpack/v4/verify-site/google`,a,{body:JSON.stringify({keyring_id:e})}).then(u).then(m),submitSurvey:e=>g(`${t}jetpack/v4/marketing/survey`,a,{body:JSON.stringify(e)}).then(u).then(m),saveSetupQuestionnaire:e=>g(`${t}jetpack/v4/setup/questionnaire`,a,{body:JSON.stringify(e)}).then(u).then(m),updateLicensingError:e=>g(`${t}jetpack/v4/licensing/error`,a,{body:JSON.stringify(e)}).then(u).then(m),updateLicenseKey:e=>g(`${t}jetpack/v4/licensing/set-license`,a,{body:JSON.stringify({license:e})}).then(u).then(m),getUserLicensesCounts:()=>p(`${t}jetpack/v4/licensing/user/counts`,r).then(u).then(m),getUserLicenses:()=>p(`${t}jetpack/v4/licensing/user/licenses`,r).then(u).then(m),updateLicensingActivationNoticeDismiss:e=>g(`${t}jetpack/v4/licensing/user/activation-notice-dismiss`,a,{body:JSON.stringify({last_detached_count:e})}).then(u).then(m),updateRecommendationsStep:e=>g(`${t}jetpack/v4/recommendations/step`,a,{body:JSON.stringify({step:e})}).then(u),confirmIDCSafeMode:()=>g(`${t}jetpack/v4/identity-crisis/confirm-safe-mode`,a).then(u),startIDCFresh:e=>g(`${t}jetpack/v4/identity-crisis/start-fresh`,a,{body:JSON.stringify({redirect_uri:e})}).then(u).then(m),migrateIDC:()=>g(`${t}jetpack/v4/identity-crisis/migrate`,a).then(u),attachLicenses:e=>g(`${t}jetpack/v4/licensing/attach-licenses`,a,{body:JSON.stringify({licenses:e})}).then(u).then(m),fetchSearchPlanInfo:()=>p(`${c}jetpack/v4/search/plan`,r).then(u).then(m),fetchSearchSettings:()=>p(`${c}jetpack/v4/search/settings`,r).then(u).then(m),updateSearchSettings:e=>g(`${c}jetpack/v4/search/settings`,a,{body:JSON.stringify(e)}).then(u).then(m),fetchSearchStats:()=>p(`${c}jetpack/v4/search/stats`,r).then(u).then(m),fetchWafSettings:()=>p(`${t}jetpack/v4/waf`,r).then(u).then(m),updateWafSettings:e=>g(`${t}jetpack/v4/waf`,a,{body:JSON.stringify(e)}).then(u).then(m),fetchWordAdsSettings:()=>p(`${t}jetpack/v4/wordads/settings`,r).then(u).then(m),updateWordAdsSettings:e=>g(`${t}jetpack/v4/wordads/settings`,a,{body:JSON.stringify(e)}),fetchSearchPricing:()=>p(`${c}jetpack/v4/search/pricing`,r).then(u).then(m),fetchMigrationStatus:()=>p(`${t}jetpack/v4/migration/status`,r).then(u).then(m),fetchBackupUndoEvent:()=>p(`${t}jetpack/v4/site/backup/undo-event`,r).then(u).then(m),fetchBackupPreflightStatus:()=>p(`${t}jetpack/v4/site/backup/preflight`,r).then(u).then(m)};function p(e,n){return fetch(l(e),n)}function g(e,n,t){return fetch(e,Object.assign({},n,t)).catch(h)}function _(e){return e.general&&void 0===e.general.response||e.week&&void 0===e.week.response||e.month&&void 0===e.month.response?e:{}}Object.assign(this,d)};function u(e){return e.status>=200&&e.status<300?e:404===e.status?new Promise((()=>{throw e.redirected?new l(e.redirected):new a})):e.json().catch((e=>g(e))).then((n=>{const t=new Error(`${n.message} (Status ${e.status})`);throw t.response=n,t.name="ApiError",t}))}function m(e){return e.json().catch((n=>g(n,e.redirected,e.url)))}function g(e,n,t){throw n?new r(t):new i}function h(){throw new d}},8089:(e,n,t)=>{"use strict";t.d(n,{A:()=>u});var o=t(6427),s=t(7723),c=t(2231),i=t(3619),r=t.n(i),a=t(1112),l=t(6854),d=t(790);const __=s.__,p=e=>{const{label:n,onClick:t,isLoading:s=!1,loadingText:i,isDisabled:r,displayError:p=!1,errorMessage:u=__("An error occurred. Please try again.","jetpack-connection"),variant:m="primary",isExternalLink:g=!1,customClass:h}=e,_=i||(0,d.jsx)(o.Spinner,{});return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(a.A,{className:(0,c.A)(l.A.button,"jp-action-button--button",h),label:n,onClick:t,variant:g?"link":m,isExternalLink:g,disabled:s||r,children:s?_:n}),p&&(0,d.jsx)("p",{className:(0,c.A)(l.A.error,"jp-action-button__error"),children:u})]})};p.propTypes={label:r().string.isRequired,onClick:r().func,isLoading:r().bool,isDisabled:r().bool,displayError:r().bool,errorMessage:r().oneOfType([r().string,r().element]),variant:r().oneOf(["primary","secondary","link"]),isExternalLink:r().bool,customClass:r().string,loadingText:r().oneOfType([r().string,r().element])};const u=p},1112:(e,n,t)=>{"use strict";t.d(n,{A:()=>u});var o=t(6427),s=t(7723),c=t(7750),i=t(8391),r=t(2231),a=t(1609),l=t(4659),d=t(790);const __=s.__,p=(0,a.forwardRef)(((e,n)=>{const{children:t,variant:s="primary",size:a="normal",weight:p="bold",icon:u,iconSize:m,disabled:g,isDestructive:h,isLoading:_,isExternalLink:f,className:y,text:b,fullWidth:j,...k}=e,v=(0,r.A)(l.A.button,y,{[l.A.normal]:"normal"===a,[l.A.small]:"small"===a,[l.A.icon]:Boolean(u),[l.A.loading]:_,[l.A.regular]:"regular"===p,[l.A["full-width"]]:j,[l.A["is-icon-button"]]:Boolean(u)&&!t});k.ref=n;const C="normal"===a?20:16,x=f&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(c.A,{size:C,icon:i.A,className:l.A["external-icon"]}),(0,d.jsx)(o.VisuallyHidden,{as:"span",children:/* translators: accessibility text */
__("(opens in a new tab)","jetpack-connection")})]}),w=f?"_blank":void 0,A=t?.[0]&&null!==t[0]&&"components-tooltip"!==t?.[0]?.props?.className;return(0,d.jsxs)(o.Button,{target:w,variant:s,className:(0,r.A)(v,{"has-text":!!u&&A}),icon:f?void 0:u,iconSize:m,disabled:g,"aria-disabled":g,isDestructive:h,text:b,...k,children:[_&&(0,d.jsx)(o.Spinner,{}),(0,d.jsx)("span",{children:t}),x]})}));p.displayName="Button";const u=p},9121:(e,n,t)=>{"use strict";t.d(n,{A:()=>s});t(2990);var o=t(790);const s=({format:e="horizontal",icon:n,imageUrl:t})=>(0,o.jsxs)("div",{className:"jp-components__decorative-card "+(e?"jp-components__decorative-card--"+e:""),children:[(0,o.jsx)("div",{className:"jp-components__decorative-card__image",style:{backgroundImage:t?`url( ${t} )`:""}}),(0,o.jsx)("div",{className:"jp-components__decorative-card__content",children:(0,o.jsx)("div",{className:"jp-components__decorative-card__lines"})}),n?(0,o.jsx)("div",{className:"jp-components__decorative-card__icon-container",children:(0,o.jsx)("span",{className:"jp-components__decorative-card__icon jp-components__decorative-card__icon--"+n})}):null]})},7142:(e,n,t)=>{"use strict";t.d(n,{A:()=>i});var o=t(7723),s=t(2231),c=t(790);const __=o.__,i=({logoColor:e="#069e08",showText:n=!0,className:t,height:o=32,...i})=>{const r=n?"0 0 118 32":"0 0 32 32";return(0,c.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:r,className:(0,s.A)("jetpack-logo",t),"aria-labelledby":"jetpack-logo-title",height:o,...i,role:"img",children:[(0,c.jsx)("title",{id:"jetpack-logo-title",children:__("Jetpack Logo","jetpack-connection")}),(0,c.jsx)("path",{fill:e,d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z M15,19H7l8-16V19z M17,29V13h8L17,29z"}),n&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("path",{d:"M41.3,26.6c-0.5-0.7-0.9-1.4-1.3-2.1c2.3-1.4,3-2.5,3-4.6V8h-3V6h6v13.4C46,22.8,45,24.8,41.3,26.6z"}),(0,c.jsx)("path",{d:"M65,18.4c0,1.1,0.8,1.3,1.4,1.3c0.5,0,2-0.2,2.6-0.4v2.1c-0.9,0.3-2.5,0.5-3.7,0.5c-1.5,0-3.2-0.5-3.2-3.1V12H60v-2h2.1V7.1 H65V10h4v2h-4V18.4z"}),(0,c.jsx)("path",{d:"M71,10h3v1.3c1.1-0.8,1.9-1.3,3.3-1.3c2.5,0,4.5,1.8,4.5,5.6s-2.2,6.3-5.8,6.3c-0.9,0-1.3-0.1-2-0.3V28h-3V10z M76.5,12.3 c-0.8,0-1.6,0.4-2.5,1.2v5.9c0.6,0.1,0.9,0.2,1.8,0.2c2,0,3.2-1.3,3.2-3.9C79,13.4,78.1,12.3,76.5,12.3z"}),(0,c.jsx)("path",{d:"M93,22h-3v-1.5c-0.9,0.7-1.9,1.5-3.5,1.5c-1.5,0-3.1-1.1-3.1-3.2c0-2.9,2.5-3.4,4.2-3.7l2.4-0.3v-0.3c0-1.5-0.5-2.3-2-2.3 c-0.7,0-2.3,0.5-3.7,1.1L84,11c1.2-0.4,3-1,4.4-1c2.7,0,4.6,1.4,4.6,4.7L93,22z M90,16.4l-2.2,0.4c-0.7,0.1-1.4,0.5-1.4,1.6 c0,0.9,0.5,1.4,1.3,1.4s1.5-0.5,2.3-1V16.4z"}),(0,c.jsx)("path",{d:"M104.5,21.3c-1.1,0.4-2.2,0.6-3.5,0.6c-4.2,0-5.9-2.4-5.9-5.9c0-3.7,2.3-6,6.1-6c1.4,0,2.3,0.2,3.2,0.5V13 c-0.8-0.3-2-0.6-3.2-0.6c-1.7,0-3.2,0.9-3.2,3.6c0,2.9,1.5,3.8,3.3,3.8c0.9,0,1.9-0.2,3.2-0.7V21.3z"}),(0,c.jsx)("path",{d:"M110,15.2c0.2-0.3,0.2-0.8,3.8-5.2h3.7l-4.6,5.7l5,6.3h-3.7l-4.2-5.8V22h-3V6h3V15.2z"}),(0,c.jsx)("path",{d:"M58.5,21.3c-1.5,0.5-2.7,0.6-4.2,0.6c-3.6,0-5.8-1.8-5.8-6c0-3.1,1.9-5.9,5.5-5.9s4.9,2.5,4.9,4.9c0,0.8,0,1.5-0.1,2h-7.3 c0.1,2.5,1.5,2.8,3.6,2.8c1.1,0,2.2-0.3,3.4-0.7C58.5,19,58.5,21.3,58.5,21.3z M56,15c0-1.4-0.5-2.9-2-2.9c-1.4,0-2.3,1.3-2.4,2.9 C51.6,15,56,15,56,15z"})]})]})}},1876:(e,n,t)=>{"use strict";t.d(n,{A:()=>i});var o=t(2231),s=t(7773),c=t(790);const i=({children:e=null,width:n=null,height:t=null,className:i=""})=>(0,c.jsx)("div",{className:(0,o.A)(s.A.placeholder,i),style:{width:n,height:t},children:e})},9957:(e,n,t)=>{"use strict";t.d(n,{A:()=>d});var o=t(4268),s=t(6427),c=t(7723),i=t(1876),r=t(5879),a=(t(5404),t(790));const __=c.__,l=e=>-1===e.fraction.indexOf("00"),d=({currencyCode:e="USD",priceDetails:n=__("/month, paid yearly","jetpack-connection"),...t})=>{const d=(0,o.vA)(t.priceBefore,e),p=(0,o.vA)(t.priceAfter,e);return(0,a.jsxs)("div",{className:"jp-components__pricing-card",children:[t.icon&&(0,a.jsx)("div",{className:"jp-components__pricing-card__icon",children:"string"==typeof t.icon?(0,a.jsx)("img",{src:t.icon,alt:(0,c.sprintf)(/* translators: placeholder is a product name */
__("Icon for the product %s","jetpack-connection"),t.title)}):t.icon}),(0,a.jsx)("h1",{className:"jp-components__pricing-card__title",children:t.title}),(0,a.jsxs)("div",{className:"jp-components__pricing-card__pricing",children:[0===t.priceAfter&&(0,a.jsx)(i.A,{width:"100%",height:48}),t.priceBefore!==t.priceAfter&&t.priceAfter>0&&(0,a.jsxs)("div",{className:"jp-components__pricing-card__price-before",children:[(0,a.jsx)("span",{className:"jp-components__pricing-card__currency",children:d.symbol}),(0,a.jsx)("span",{className:"jp-components__pricing-card__price",children:d.integer}),l(d)&&(0,a.jsxs)("span",{className:"jp-components__pricing-card__price-decimal",children:[" ",d.fraction]}),(0,a.jsx)("div",{className:"jp-components__pricing-card__price-strikethrough"})]}),t.priceAfter>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"jp-components__pricing-card__price-after",children:[(0,a.jsx)("span",{className:"jp-components__pricing-card__currency",children:p.symbol}),(0,a.jsx)("span",{className:"jp-components__pricing-card__price",children:p.integer}),l(p)&&(0,a.jsx)("span",{className:"jp-components__pricing-card__price-decimal",children:p.fraction})]}),(0,a.jsx)("span",{className:"jp-components__pricing-card__price-details",children:n})]})]}),t.children&&(0,a.jsx)("div",{className:"jp-components__pricing-card__extra-content-wrapper",children:t.children}),t.tosText&&(0,a.jsx)("div",{className:"jp-components__pricing-card__tos",children:t.tosText}),t.ctaText&&(0,a.jsxs)(a.Fragment,{children:[!t.tosText&&(0,a.jsx)("div",{className:"jp-components__pricing-card__tos",children:(0,a.jsx)(r.A,{agreeButtonLabel:t.ctaText})}),(0,a.jsx)("div",{className:"jp-components__pricing-card__cta",children:(0,a.jsx)(s.Button,{className:"jp-components__pricing-card__button",label:t.ctaText,onClick:t.onCtaClick,children:t.ctaText})})]}),t.infoText&&(0,a.jsx)("div",{className:"jp-components__pricing-card__info",children:t.infoText})]})}},6461:(e,n,t)=>{"use strict";t.d(n,{A:()=>r});var o=t(3619),s=t.n(o),c=(t(2144),t(790));const i=({color:e="#FFFFFF",className:n="",size:t=20})=>{const o=n+" jp-components-spinner",s={width:t,height:t,fontSize:t,borderTopColor:e},i={borderTopColor:e,borderRightColor:e};return(0,c.jsx)("div",{className:o,children:(0,c.jsx)("div",{className:"jp-components-spinner__outer",style:s,children:(0,c.jsx)("div",{className:"jp-components-spinner__inner",style:i})})})};i.propTypes={color:s().string,className:s().string,size:s().number};const r=i},5879:(e,n,t)=>{"use strict";t.d(n,{A:()=>g});var o=t(6427),s=t(6087),c=t(7723),i=t(2231),r=t(3924),a=t(7425),l=(t(4099),t(790));const __=c.__,d=({multipleButtonsLabels:e})=>Array.isArray(e)&&e.length>1?(0,s.createInterpolateElement)((0,c.sprintf)(/* translators: %1$s is button label 1 and %2$s is button label 2 */
__("By clicking <strong>%1$s</strong> or <strong>%2$s</strong>, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-connection"),e[0],e[1]),{strong:(0,l.jsx)("strong",{}),tosLink:(0,l.jsx)(m,{slug:"wpcom-tos"}),shareDetailsLink:(0,l.jsx)(m,{slug:"jetpack-support-what-data-does-jetpack-sync"})}):(0,s.createInterpolateElement)(__("By clicking the buttons above, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-connection"),{tosLink:(0,l.jsx)(m,{slug:"wpcom-tos"}),shareDetailsLink:(0,l.jsx)(m,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),p=({agreeButtonLabel:e})=>(0,s.createInterpolateElement)((0,c.sprintf)(/* translators: %s is a button label */
__("By clicking <strong>%s</strong>, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-connection"),e),{strong:(0,l.jsx)("strong",{}),tosLink:(0,l.jsx)(m,{slug:"wpcom-tos"}),shareDetailsLink:(0,l.jsx)(m,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),u=()=>(0,s.createInterpolateElement)(__("By continuing you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site’s data</shareDetailsLink> with us. We’ll check if that email is linked to an existing WordPress.com account or create a new one instantly.","jetpack-connection"),{tosLink:(0,l.jsx)(m,{slug:"wpcom-tos"}),shareDetailsLink:(0,l.jsx)(m,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),m=({slug:e,children:n})=>(0,l.jsx)(o.ExternalLink,{className:"terms-of-service__link",href:(0,r.A)(e),children:n}),g=({className:e,multipleButtons:n,agreeButtonLabel:t,isTextOnly:o,...s})=>(0,l.jsx)(a.Ay,{className:(0,i.A)(e,"terms-of-service"),...s,children:o?(0,l.jsx)(u,{}):n?(0,l.jsx)(d,{multipleButtonsLabels:n}):(0,l.jsx)(p,{agreeButtonLabel:t})})},110:(e,n,t)=>{"use strict";t.d(n,{Q:()=>o,Z:()=>s});const o={"headline-medium":"h1","headline-small":"h2","headline-small-regular":"h2","title-medium":"h3","title-medium-semi-bold":"h3","title-small":"h4",body:"p","body-small":"p","body-extra-small":"p","body-extra-small-bold":"p",label:"p"},s=["mt","mr","mb","ml","mx","my","m","pt","pr","pb","pl","px","py","p"]},7425:(e,n,t)=>{"use strict";t.d(n,{Ay:()=>l});var o=t(2231),s=t(1609),c=t(110),i=t(3370),r=t(790);const a=(0,s.forwardRef)((({variant:e="body",children:n,component:t,className:a,...l},d)=>{const p=t||c.Q[e]||"span",u=(0,s.useMemo)((()=>c.Z.reduce(((e,n)=>(void 0!==l[n]&&(e+=i.A[`${n}-${l[n]}`]+" ",delete l[n]),e)),"")),[l]);return(0,r.jsx)(p,{className:(0,o.A)(i.A.reset,i.A[e],a,u),...l,ref:d,children:n})}));a.displayName="Text";const l=a},3924:(e,n,t)=>{"use strict";function o(e,n={}){const t={};let o;if("undefined"!=typeof window&&(o=window?.JP_CONNECTION_INITIAL_STATE?.calypsoEnv),0===e.search("https://")){const n=new URL(e);e=`https://${n.host}${n.pathname}`,t.url=encodeURIComponent(e)}else t.source=encodeURIComponent(e);for(const e in n)t[e]=encodeURIComponent(n[e]);!Object.keys(t).includes("site")&&"undefined"!=typeof jetpack_redirects&&Object.hasOwn(jetpack_redirects,"currentSiteRawUrl")&&(t.site=jetpack_redirects.currentBlogID??jetpack_redirects.currentSiteRawUrl),o&&(t.calypso_env=o);return"https://jetpack.com/redirect/?"+Object.keys(t).map((e=>e+"="+t[e])).join("&")}t.d(n,{A:()=>o})},6439:(e,n,t)=>{let o={};try{o=t(9074)}catch{console.error("jetpackConfig is missing in your webpack config file. See @automattic/jetpack-config"),o={missingConfig:!0}}const s=e=>Object.hasOwn(o,e);e.exports={jetpackConfigHas:s,jetpackConfigGet:e=>{if(!s(e))throw'This app requires the "'+e+'" Jetpack Config to be defined in your webpack configuration file. See details in @automattic/jetpack-config package docs.';return o[e]}}},8421:(e,n,t)=>{"use strict";t.d(n,{A:()=>d});var o=t(8089),s=t(7723),c=t(3619),i=t.n(c),r=t(9660),a=t(790);const __=s.__,l=e=>{const{apiRoot:n,apiNonce:t,connectLabel:s=__("Connect","jetpack-connection"),registrationNonce:c,redirectUri:i=null,from:l,autoTrigger:d=!1}=e,{handleRegisterSite:p,isRegistered:u,isUserConnected:m,siteIsRegistering:g,userIsConnecting:h,registrationError:_}=(0,r.A)({registrationNonce:c,redirectUri:i,apiRoot:n,apiNonce:t,autoTrigger:d,from:l});return(0,a.jsx)(a.Fragment,{children:(!u||!m)&&(0,a.jsx)(o.A,{label:s,onClick:p,displayError:!!_,isLoading:g||h})})};l.propTypes={connectLabel:i().string,apiRoot:i().string.isRequired,apiNonce:i().string.isRequired,from:i().string,redirectUri:i().string.isRequired,registrationNonce:i().string.isRequired,autoTrigger:i().bool};const d=l},6212:(e,n,t)=>{"use strict";t.d(n,{A:()=>r});var o=t(7723),s=t(9660),c=t(5582),i=t(790);const __=o.__,r=({title:e,buttonLabel:n,loadingLabel:t,apiRoot:o,apiNonce:r,registrationNonce:a,from:l,redirectUri:d,images:p,children:u,assetBaseUrl:m,autoTrigger:g,footer:h,skipUserConnection:_,skipPricingPage:f,logo:y})=>{const{handleRegisterSite:b,siteIsRegistering:j,userIsConnecting:k,registrationError:v,isOfflineMode:C}=(0,s.A)({registrationNonce:a,redirectUri:d,apiRoot:o,apiNonce:r,autoTrigger:g,from:l,skipUserConnection:_,skipPricingPage:f}),x=Boolean(v),w=j||k,A=v?.response?.code;return(0,i.jsx)(c.A,{title:e||__("Over 5 million WordPress sites are faster and more secure","jetpack-connection"),images:p||[],assetBaseUrl:m,buttonLabel:n||__("Set up Jetpack","jetpack-connection"),loadingLabel:t,handleButtonClick:b,displayButtonError:x,errorCode:A,buttonIsLoading:w,footer:h,isOfflineMode:C,logo:y,children:u})}},5582:(e,n,t)=>{"use strict";t.d(n,{A:()=>p});var o=t(3924),s=t(5879),c=t(8089),i=t(6087),r=t(7723),a=t(2668),l=(t(7627),t(790));const __=r.__,d=(e,n)=>{switch(e){case"fail_domain_forbidden":case"fail_ip_forbidden":case"fail_domain_tld":case"fail_subdomain_wpcom":case"siteurl_private_ip":return __("Your site host is on a private network. Jetpack can only connect to public sites.","jetpack-connection");case"connection_disabled":return __("This site has been suspended.","jetpack-connection")}if(n)return(0,i.createInterpolateElement)(__("Unavailable in <a>Offline Mode</a>","jetpack-connection"),{a:(0,l.jsx)("a",{href:(0,o.A)("jetpack-support-development-mode"),target:"_blank",rel:"noopener noreferrer"})})},p=({title:e,images:n,children:t,assetBaseUrl:o,isLoading:i,buttonLabel:r,handleButtonClick:p,displayButtonError:u,errorCode:m,buttonIsLoading:g,loadingLabel:h,footer:_,isOfflineMode:f,logo:y})=>(0,l.jsx)(a.A,{title:e,assetBaseUrl:o,images:n,className:"jp-connection__connect-screen"+(i?" jp-connection__connect-screen__loading":""),logo:y,children:(0,l.jsxs)("div",{className:"jp-connection__connect-screen__content",children:[t,(0,l.jsx)("div",{className:"jp-connection__connect-screen__tos",children:(0,l.jsx)(s.A,{agreeButtonLabel:r})}),(0,l.jsx)(c.A,{label:r,onClick:p,displayError:u||f,errorMessage:d(m,f),isLoading:g,isDisabled:f}),(0,l.jsx)("span",{className:"jp-connection__connect-screen__loading-message",role:"status",children:g?h||__("Loading","jetpack-connection"):""}),_&&(0,l.jsx)("div",{className:"jp-connection__connect-screen__footer",children:_})]})})},5745:(e,n,t)=>{"use strict";t.d(n,{A:()=>c});var o=t(1609),s=t(790);const c=({images:e,assetBaseUrl:n=""})=>{if(!e?.length)return null;const t=e.map(((e,t)=>(0,s.jsx)(o.Fragment,{children:(0,s.jsx)("img",{src:n+e,alt:""})},t)));return(0,s.jsx)("div",{className:"jp-connection__connect-screen__image-slider",children:t})}},2668:(e,n,t)=>{"use strict";t.d(n,{A:()=>r});var o=t(7142),s=t(2231),c=t(5745),i=(t(1133),t(790));const r=({title:e,children:n,className:t,assetBaseUrl:r,images:a,logo:l,rna:d=!1})=>{const p=a?.length;return(0,i.jsxs)("div",{className:(0,s.A)("jp-connection__connect-screen-layout",p?"jp-connection__connect-screen-layout__two-columns":"",t?" "+t:""),children:[d&&(0,i.jsxs)("div",{className:"jp-connection__connect-screen-layout__color-blobs",children:[(0,i.jsx)("div",{className:"jp-connection__connect-screen-layout__color-blobs__green"}),(0,i.jsx)("div",{className:"jp-connection__connect-screen-layout__color-blobs__yellow"}),(0,i.jsx)("div",{className:"jp-connection__connect-screen-layout__color-blobs__blue"})]}),(0,i.jsxs)("div",{className:"jp-connection__connect-screen-layout__left",children:[l||(0,i.jsx)(o.A,{}),(0,i.jsx)("h2",{children:e}),n]}),p?(0,i.jsx)("div",{className:"jp-connection__connect-screen-layout__right",children:(0,i.jsx)(c.A,{images:a,assetBaseUrl:r})}):null]})}},7945:(e,n,t)=>{"use strict";t.d(n,{A:()=>p});var o=t(7723),s=t(3619),c=t.n(s),i=t(2558),r=t(9660),a=t(401),l=t(790);const __=o.__,d=e=>{const{title:n=__("Over 5 million WordPress sites are faster and more secure","jetpack-connection"),autoTrigger:t=!1,buttonLabel:o=__("Set up Jetpack","jetpack-connection"),apiRoot:s,apiNonce:c,registrationNonce:d,from:p,redirectUri:u,children:m,priceBefore:g,priceAfter:h,pricingIcon:_,pricingTitle:f,pricingCurrencyCode:y="USD",wpcomProductSlug:b,siteProductAvailabilityHandler:j,logo:k,rna:v=!1}=e,{handleRegisterSite:C,siteIsRegistering:x,userIsConnecting:w,registrationError:A,isOfflineMode:N}=(0,r.A)({registrationNonce:d,redirectUri:u,apiRoot:s,apiNonce:c,autoTrigger:t,from:p}),S=b||"",{run:E,hasCheckoutStarted:T}=(0,i.A)({productSlug:S,redirectUrl:u,siteProductAvailabilityHandler:j,from:p}),R=Boolean(A),O=x||w||T,L=S?E:C;return(0,l.jsx)(a.A,{title:n,buttonLabel:o,priceBefore:g,priceAfter:h,pricingIcon:_,pricingTitle:f,pricingCurrencyCode:y,handleButtonClick:L,displayButtonError:R,buttonIsLoading:O,logo:k,isOfflineMode:N,rna:v,children:m})};d.propTypes={title:c().string,buttonLabel:c().string,apiRoot:c().string.isRequired,apiNonce:c().string.isRequired,registrationNonce:c().string.isRequired,from:c().string,redirectUri:c().string.isRequired,autoTrigger:c().bool,pricingTitle:c().string.isRequired,pricingIcon:c().oneOfType([c().string,c().element]),priceBefore:c().number.isRequired,priceAfter:c().number.isRequired,pricingCurrencyCode:c().string,wpcomProductSlug:c().string,checkSiteHasWpcomProduct:c().func,logo:c().element};const p=d},401:(e,n,t)=>{"use strict";t.d(n,{A:()=>y});var o=t(8089),s=t(3924),c=t(9957),i=t(5879),r=t(6087),a=t(7723),l=t(2231),d=t(4804),p=t.n(d),u=t(3619),m=t.n(u),g=t(2668),h=(t(1590),t(790));const __=a.__,_=p()("jetpack:connection:ConnectScreenRequiredPlanVisual"),f=e=>{const{title:n,buttonLabel:t,children:a,priceBefore:d,priceAfter:p,pricingIcon:u,pricingTitle:m,pricingCurrencyCode:f="USD",isLoading:y=!1,handleButtonClick:b=()=>{},displayButtonError:j=!1,buttonIsLoading:k=!1,logo:v,isOfflineMode:C,rna:x=!1}=e;_("props are %o",e);const w=(0,r.createInterpolateElement)(__("Already have a subscription? <connectButton/>","jetpack-connection"),{connectButton:(0,h.jsx)(o.A,{label:__("Log in to get started","jetpack-connection"),onClick:b,isLoading:k})}),A=C?(0,r.createInterpolateElement)(__("Unavailable in <a>Offline Mode</a>","jetpack-connection"),{a:(0,h.jsx)("a",{href:(0,s.A)("jetpack-support-development-mode"),target:"_blank",rel:"noopener noreferrer"})}):void 0;return(0,h.jsx)(g.A,{title:n,className:(0,l.A)("jp-connection__connect-screen-required-plan",y?"jp-connection__connect-screen-required-plan__loading":"",x?"rna":""),logo:v,rna:x,children:(0,h.jsxs)("div",{className:"jp-connection__connect-screen-required-plan__content",children:[a,(0,h.jsx)("div",{className:"jp-connection__connect-screen-required-plan__pricing-card",children:(0,h.jsxs)(c.A,{title:m,icon:u,priceBefore:d,currencyCode:f,priceAfter:p,children:[(0,h.jsx)(i.A,{agreeButtonLabel:t}),(0,h.jsx)(o.A,{label:t,onClick:b,displayError:j||C,errorMessage:A,isLoading:k,isDisabled:C})]})}),!C&&(0,h.jsx)("div",{className:"jp-connection__connect-screen-required-plan__with-subscription",children:w})]})})};f.propTypes={pricingTitle:m().string.isRequired,priceBefore:m().number.isRequired,priceAfter:m().number.isRequired,pricingCurrencyCode:m().string,title:m().string,buttonLabel:m().string,pricingIcon:m().oneOfType([m().string,m().element]),isLoading:m().bool,handleButtonClick:m().func,displayButtonError:m().bool,buttonIsLoading:m().bool,logo:m().element,isOfflineMode:m().bool};const y=f},7840:(e,n,t)=>{"use strict";t.d(n,{A:()=>a});var o=t(5932),s=t(3619),c=t.n(s),i=t(1609);const r=e=>{const{redirectFunc:n=e=>window.location.assign(e),connectUrl:t,redirectUri:s=null,from:c}=e,[r,a]=(0,i.useState)(null);return t&&t!==r&&a(t),(0,i.useEffect)((()=>{r||o.Ay.fetchAuthorizationUrl(s).then((e=>a(e.authorizeUrl))).catch((e=>{throw e}))}),[]),r?(n(r+(c?(r.includes("?")?"&":"?")+"from="+encodeURIComponent(c):"")),null):null};r.propTypes={connectUrl:c().string,redirectUri:c().string.isRequired,from:c().string,redirectFunc:c().func};const a=r},648:(e,n,t)=>{"use strict";t.d(n,{A:()=>d});var o=t(7723),s=t(3619),c=t.n(s),i=t(1609),r=t(7499),a=t(790);const __=o.__,l=e=>{const{connectedPlugins:n,disconnectingPlugin:t}=e,o=(0,i.useMemo)((()=>{if(n){return Object.keys(n).map((e=>Object.assign({slug:e},n[e]))).filter((e=>t!==e.slug))}return[]}),[n,t]);return n&&o.length>0?(0,a.jsxs)(i.Fragment,{children:[(0,a.jsx)("div",{className:"jp-connection__disconnect-dialog__step-copy",children:(0,a.jsx)("p",{className:"jp-connection__disconnect-dialog__large-text",children:__("Jetpack is powering other plugins on your site. If you disconnect, these plugins will no longer work.","jetpack-connection")})}),(0,a.jsx)("div",{className:"jp-connection__disconnect-card__group",children:o.map((e=>(0,a.jsx)(r.A,{title:e.name},e.slug)))})]}):null};l.propTypes={connectedPlugins:c().array,disconnectingPlugin:c().string};const d=l},7088:(e,n,t)=>{"use strict";t.d(n,{A:()=>p});var o=t(6461),s=t(6427),c=t(7723),i=t(3619),r=t.n(i),a=t(9910),l=t(790);const __=c.__,d=e=>{const{message:n,isRestoringConnection:t,restoreConnectionCallback:i,restoreConnectionError:r,actions:d=[]}=e,p=a.A.notice,u=(0,l.jsx)(s.Icon,{icon:(0,l.jsxs)(s.SVG,{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,l.jsx)(s.Path,{d:"M11.7815 4.93772C11.8767 4.76626 12.1233 4.76626 12.2185 4.93772L20.519 19.8786C20.6116 20.0452 20.4911 20.25 20.3005 20.25H3.69951C3.50889 20.25 3.3884 20.0452 3.48098 19.8786L11.7815 4.93772Z",stroke:"#D63638",strokeWidth:"1.5"}),(0,l.jsx)(s.Path,{d:"M13 10H11V15H13V10Z",fill:"#D63638"}),(0,l.jsx)(s.Path,{d:"M13 16H11V18H13V16Z",fill:"#D63638"})]})});if(!n)return null;if(t)return(0,l.jsx)(s.Notice,{status:"error",isDismissible:!1,className:p,children:(0,l.jsxs)("div",{className:a.A.message,children:[(0,l.jsx)(o.A,{color:"#B32D2E",size:24}),__("Reconnecting Jetpack","jetpack-connection")]})});const m=r?(0,l.jsx)(s.Notice,{status:"error",isDismissible:!1,className:p+" "+a.A.error,children:(0,l.jsxs)("div",{className:a.A.message,children:[u,(0,c.sprintf)(/* translators: placeholder is the error. */
__("There was an error reconnecting Jetpack. Error: %s","jetpack-connection"),r)]})}):null;let g=[];return d.length>0?g=d.map(((e,n)=>{let t=a.A.button;return"primary"===e.variant?t+=" "+a.A.primary:"secondary"===e.variant&&(t+=" "+a.A.secondary),(0,l.jsx)("button",{type:"button",onClick:e.onClick,onKeyDown:e.onClick,className:t,disabled:e.isLoading,children:e.isLoading?e.loadingText||__("Loading…","jetpack-connection"):e.label},n)})):i&&(g=[(0,l.jsx)("button",{type:"button",onClick:i,onKeyDown:i,className:a.A.button,children:__("Restore Connection","jetpack-connection")},"restore")]),(0,l.jsxs)(l.Fragment,{children:[m,(0,l.jsxs)(s.Notice,{status:"error",isDismissible:!1,className:p,children:[(0,l.jsxs)("div",{className:a.A.message,children:[u,n]}),g.length>0&&(0,l.jsx)("div",{className:a.A.actions,children:g})]})]})};d.propTypes={message:r().oneOfType([r().string,r().element]).isRequired,restoreConnectionCallback:r().func,isRestoringConnection:r().bool,restoreConnectionError:r().string,actions:r().arrayOf(r().shape({label:r().string.isRequired,onClick:r().func.isRequired,isLoading:r().bool,loadingText:r().string,variant:r().oneOf(["primary","secondary"])}))};const p=d},7499:(e,n,t)=>{"use strict";t.d(n,{A:()=>r});var o=t(3619),s=t.n(o),c=(t(177),t(790));const i=e=>{const{title:n,value:t,description:o}=e;return(0,c.jsx)("div",{className:"jp-connection__disconnect-card card",children:(0,c.jsxs)("div",{className:"jp-connection__disconnect-card__card-content",children:[(0,c.jsx)("p",{className:"jp-connection__disconnect-card__card-headline",children:n}),(t||o)&&(0,c.jsxs)("div",{className:"jp-connection__disconnect-card__card-stat-block",children:[(0,c.jsx)("span",{className:"jp-connection__disconnect-card__card-stat",children:t}),(0,c.jsx)("div",{className:"jp-connection__disconnect-card__card-description",children:o})]})]})})};i.propTypes={title:s().string,value:s().oneOfType([s().string,s().number]),description:s().string};const r=i},3269:(e,n,t)=>{"use strict";t.d(n,{A:()=>f});var o=t(372),s=t(5932),c=t(6439),i=t(6427),r=t(7723),a=t(3619),l=t.n(a),d=t(1609),p=(t(3524),t(4472)),u=t(8503),m=t(412),g=t(8090),h=t(790);const __=r.__,_=e=>{const[n,t]=(0,d.useState)(!1),[r,a]=(0,d.useState)(!1),[l,_]=(0,d.useState)(!1),[f,y]=(0,d.useState)(!1),[b,j]=(0,d.useState)(!1),[k,v]=(0,d.useState)(!1),{apiRoot:C,apiNonce:x,connectedPlugins:w,title:A=__("Are you sure you want to disconnect?","jetpack-connection"),pluginScreenDisconnectCallback:N,onDisconnected:S,onError:E,disconnectStepComponent:T,context:R="jetpack-dashboard",connectedUser:O={},connectedSiteId:L,isOpen:I,onClose:P}=e;let D="";(0,c.jetpackConfigHas)("consumer_slug")&&(D=(0,c.jetpackConfigGet)("consumer_slug"));const U=(0,d.useMemo)((()=>({context:R,plugin:D})),[R,D]);(0,d.useEffect)((()=>{s.Ay.setApiRoot(C),s.Ay.setApiNonce(x)}),[C,x]),(0,d.useEffect)((()=>{O&&O.ID&&O.login&&o.A.initialize(O.ID,O.login)}),[O,O.ID,O.login]),(0,d.useEffect)((()=>{I&&o.A.tracks.recordEvent("jetpack_disconnect_dialog_open",U)}),[I,U]),(0,d.useEffect)((()=>{I&&(r?!r||f||b?f&&!b?o.A.tracks.recordEvent("jetpack_disconnect_dialog_step",Object.assign({},{step:"survey"},U)):b&&o.A.tracks.recordEvent("jetpack_disconnect_dialog_step",Object.assign({},{step:"thank_you"},U)):o.A.tracks.recordEvent("jetpack_disconnect_dialog_step",Object.assign({},{step:"disconnect_confirm"},U)):o.A.tracks.recordEvent("jetpack_disconnect_dialog_step",Object.assign({},{step:"disconnect"},U)))}),[I,r,f,b,U]);const F=(0,d.useCallback)((()=>{s.Ay.disconnectSite().then((()=>{t(!1),a(!0)})).catch((e=>{t(!1),_(e),E&&E(e)}))}),[t,a,_,E]),B=(0,d.useCallback)(((e,n)=>{v(!0),fetch("https://public-api.wordpress.com/wpcom/v2/marketing/feedback-survey",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(e)}).then((e=>e.json())).then((e=>{if(!0!==e.success)throw new Error("Survey endpoint returned error code "+e.code);o.A.tracks.recordEvent("jetpack_disconnect_survey_submit",n),j(!0),v(!1)})).catch((e=>{o.A.tracks.recordEvent("jetpack_disconnect_survey_error",Object.assign({},{error:e.message},n)),j(!0),v(!1)}))}),[v,j]),M=(0,d.useCallback)((e=>{e&&e.preventDefault(),_(!1),t(!0),"plugins"!==R?F():N&&N(e)}),[_,t,N,R,F]),$=(0,d.useCallback)((e=>o.A.tracks.recordEvent(e,U)),[U]),J=(0,d.useCallback)((()=>!(!O.ID||!L)),[O,L]),z=(0,d.useCallback)(((e,n,t)=>{if(t&&t.preventDefault(),!J())return void j(!0);const o={site_id:L,user_id:O.ID,survey_id:"jetpack-plugin-disconnect",survey_responses:{"why-cancel":{response:e,text:n||null}}},s=Object.assign({},U,{disconnect_reason:e});B(o,s)}),[B,j,J,L,O,U]),q=(0,d.useCallback)((e=>{e&&e.preventDefault(),S&&S(),P()}),[S,P]),G=(0,d.useCallback)((e=>{e&&e.preventDefault(),y(!0)}),[y]);return(0,h.jsx)(h.Fragment,{children:I&&(0,h.jsx)(i.Modal,{title:"",contentLabel:A,aria:{labelledby:"jp-connection__disconnect-dialog__heading"},onRequestClose:P,shouldCloseOnClickOutside:!1,shouldCloseOnEsc:!1,isDismissible:!1,className:"jp-connection__disconnect-dialog"+(r?" jp-connection__disconnect-dialog__success":""),children:r?!r||f||b?f&&!b?(0,h.jsx)(m.A,{isSubmittingFeedback:k,onFeedBackProvided:z,onExit:q}):b?(0,h.jsx)(g.A,{onExit:q}):void 0:(0,h.jsx)(u.A,{canProvideFeedback:J(),onProvideFeedback:G,onExit:q}):(0,h.jsx)(p.A,{title:A,connectedPlugins:w,disconnectStepComponent:T,isDisconnecting:n,closeModal:P,onDisconnect:M,disconnectError:l,context:R,disconnectingPlugin:D,trackModalClick:$})})})};_.propTypes={apiRoot:l().string.isRequired,apiNonce:l().string.isRequired,title:l().string,onDisconnected:l().func,onError:l().func,context:l().string,connectedPlugins:l().oneOfType([l().array,l().object]),pluginScreenDisconnectCallback:l().func,disconnectStepComponent:l().element,connectedUser:l().object,connectedSiteId:l().number,isOpen:l().bool,onClose:l().func};const f=_},8503:(e,n,t)=>{"use strict";t.d(n,{A:()=>u});var o=t(9121),s=t(6427),c=t(6087),i=t(7723),r=t(3619),a=t.n(r),l=t(2365),d=t(790);const __=i.__,p=e=>{const{onExit:n,canProvideFeedback:t,onProvideFeedback:i}=e;return(0,d.jsxs)("div",{className:"jp-connection__disconnect-dialog__content",children:[(0,d.jsx)(o.A,{icon:"unlink",imageUrl:l}),(0,d.jsxs)("div",{className:"jp-connection__disconnect-dialog__step-copy jp-connection__disconnect-dialog__step-copy--narrow",children:[(0,d.jsx)("h1",{children:(0,c.createInterpolateElement)(__("Jetpack has been <br/>successfully disconnected.","jetpack-connection"),{br:(0,d.jsx)("br",{})})}),t&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("p",{children:__("We’re sorry to see you go. Here at Jetpack, we’re always striving to provide the best experience for our customers. Please take our short survey (2 minutes, promise).","jetpack-connection")}),(0,d.jsx)("p",{children:(0,d.jsx)(s.Button,{variant:"primary",onClick:i,className:"jp-connection__disconnect-dialog__btn-back-to-wp",children:__("Help us improve","jetpack-connection")})}),(0,d.jsx)("a",{className:"jp-connection__disconnect-dialog__link jp-connection__disconnect-dialog__link--bold",href:"#",onClick:n,children:__("No thank you","jetpack-connection")})]}),!t&&(0,d.jsx)(d.Fragment,{children:(0,d.jsx)("p",{children:(0,d.jsx)(s.Button,{variant:"primary",onClick:n,className:"jp-connection__disconnect-dialog__btn-back-to-wp",children:__("Back to my website","jetpack-connection")})})})]})]})};p.propTypes={onExit:a().func,onProvideFeedback:a().func,canProvideFeedback:a().bool};const u=p},4472:(e,n,t)=>{"use strict";t.d(n,{A:()=>m});var o=t(3924),s=t(6427),c=t(6087),i=t(7723),r=t(3619),a=t.n(r),l=t(1609),d=t(648),p=t(790);const __=i.__,u=e=>{const{title:n,isDisconnecting:t,onDisconnect:i,disconnectError:r,disconnectStepComponent:a,connectedPlugins:u,disconnectingPlugin:m,closeModal:g,context:h,trackModalClick:_}=e,f=(0,l.useCallback)((()=>_("jetpack_disconnect_dialog_click_learn_about")),[_]),y=(0,l.useCallback)((()=>_("jetpack_disconnect_dialog_click_support")),[_]),b=(0,l.useCallback)((()=>{_("jetpack_disconnect_dialog_click_stay_connected"),g()}),[_,g]),j=(0,l.useCallback)((e=>{_("jetpack_disconnect_dialog_click_disconnect"),i(e)}),[_,i]),k=(0,l.useCallback)((e=>{"Escape"!==e.key||t||b()}),[b,t]);(0,l.useEffect)((()=>(document.addEventListener("keydown",k,!1),()=>{document.removeEventListener("keydown",k,!1)})),[]);return(0,p.jsxs)(l.Fragment,{children:[(0,p.jsxs)("div",{className:"jp-connection__disconnect-dialog__content",children:[(0,p.jsx)("h1",{id:"jp-connection__disconnect-dialog__heading",children:n}),(0,p.jsx)(d.A,{connectedPlugins:u,disconnectingPlugin:m}),a,(()=>{if(!(u&&Object.keys(u).filter((e=>e!==m)).length)&&!a)return(0,p.jsx)("div",{className:"jp-connection__disconnect-dialog__step-copy",children:(0,p.jsxs)("p",{className:"jp-connection__disconnect-dialog__large-text",children:[__("Jetpack is currently powering multiple products on your site.","jetpack-connection"),(0,p.jsx)("br",{}),__("Once you disconnect Jetpack, these will no longer work.","jetpack-connection")]})})})()]}),(0,p.jsxs)("div",{className:"jp-connection__disconnect-dialog__actions",children:[(0,p.jsxs)("div",{className:"jp-row",children:[(0,p.jsx)("div",{className:"lg-col-span-8 md-col-span-9 sm-col-span-4",children:(0,p.jsx)("p",{children:(0,c.createInterpolateElement)(__("<strong>Need help?</strong> Learn more about the <jpConnectionInfoLink>Jetpack connection</jpConnectionInfoLink> or <jpSupportLink>contact Jetpack support</jpSupportLink>.","jetpack-connection"),{strong:(0,p.jsx)("strong",{}),jpConnectionInfoLink:(0,p.jsx)(s.ExternalLink,{href:(0,o.A)("why-the-wordpress-com-connection-is-important-for-jetpack"),className:"jp-connection__disconnect-dialog__link",onClick:f}),jpSupportLink:(0,p.jsx)(s.ExternalLink,{href:(0,o.A)("jetpack-support"),className:"jp-connection__disconnect-dialog__link",onClick:y})})})}),(0,p.jsxs)("div",{className:"jp-connection__disconnect-dialog__button-wrap lg-col-span-4 md-col-span-7 sm-col-span-4",children:[(0,p.jsx)(s.Button,{variant:"primary",disabled:t,onClick:b,className:"jp-connection__disconnect-dialog__btn-dismiss",children:"plugins"===h?__("Cancel","jetpack-connection"):__("Stay connected","jetpack-connection",0)}),(()=>{let e=__("Disconnect","jetpack-connection");return t?e=__("Disconnecting…","jetpack-connection"):"plugins"===h&&(e=__("Deactivate","jetpack-connection")),(0,p.jsx)(s.Button,{variant:"primary",disabled:t,onClick:j,className:"jp-connection__disconnect-dialog__btn-disconnect",children:e})})()]})]}),r&&(0,p.jsx)("p",{className:"jp-connection__disconnect-dialog__error",children:r})]})]})};u.propTypes={title:a().string,isDisconnecting:a().bool,onDisconnect:a().func,disconnectError:a().bool,disconnectStepComponent:a().element,connectedPlugins:a().array,disconnectingPlugin:a().string,closeModal:a().func,context:a().string,trackModalClick:a().func};const m=u},412:(e,n,t)=>{"use strict";t.d(n,{A:()=>l});var o=t(7723),s=t(3619),c=t.n(s),i=(t(7556),t(2951)),r=t(790);const __=o.__,a=e=>{const{onExit:n,onFeedBackProvided:t,isSubmittingFeedback:o}=e;return(0,r.jsxs)("div",{className:"jp-connection__disconnect-dialog__content",children:[(0,r.jsx)("h1",{children:__("Before you go, help us improve Jetpack","jetpack-connection")}),(0,r.jsx)("p",{className:"jp-connection__disconnect-dialog__large-text",children:__("Let us know what didn‘t work for you","jetpack-connection")}),(0,r.jsx)(i.A,{onSubmit:t,isSubmittingFeedback:o}),(0,r.jsx)("a",{className:"jp-connection__disconnect-dialog__link jp-connection__disconnect-dialog__link--bold",href:"#",onClick:n,children:__("Skip for now","jetpack-connection")})]})};a.PropTypes={onExit:c().func,onFeedBackProvided:c().func,isSubmittingFeedback:c().bool};const l=a},8090:(e,n,t)=>{"use strict";t.d(n,{A:()=>u});var o=t(9121),s=t(6427),c=t(6087),i=t(7723),r=t(3619),a=t.n(r),l=t(9362),d=t(790);const __=i.__,p=e=>{const{onExit:n}=e;return(0,d.jsxs)("div",{className:"jp-connection__disconnect-dialog__content",children:[(0,d.jsx)(o.A,{format:"vertical",imageUrl:l}),(0,d.jsxs)("div",{className:"jp-connection__disconnect-dialog__copy",children:[(0,d.jsx)("h1",{children:__("Thank you!","jetpack-connection")}),(0,d.jsx)("p",{className:"jp-connection__disconnect-dialog__large-text",children:(0,c.createInterpolateElement)(__("Your answer has been submitted. <br/>Thanks for your input on how we can improve Jetpack.","jetpack-connection"),{br:(0,d.jsx)("br",{})})}),(0,d.jsx)(s.Button,{variant:"primary",onClick:n,className:"jp-connection__disconnect-dialog__btn-back-to-wp",children:__("Back to my website","jetpack-connection")})]})]})};p.PropTypes={onExit:a().func,assetBaseUrl:a().string};const u=p},2951:(e,n,t)=>{"use strict";t.d(n,{A:()=>p});var o=t(6427),s=t(7723),c=t(3619),i=t.n(c),r=t(1609),a=t(8233),l=t(790);const __=s.__,d=e=>{const{onSubmit:n,isSubmittingFeedback:t}=e,[s,c]=(0,r.useState)(),[i,d]=(0,r.useState)(),p=[{id:"troubleshooting",answerText:__("Troubleshooting - I'll be reconnecting afterwards.","jetpack-connection")},{id:"not-working",answerText:__("I can't get it to work.","jetpack-connection")},{id:"slowed-down-site",answerText:__("It slowed down my site.","jetpack-connection")},{id:"buggy",answerText:__("It's buggy.","jetpack-connection")},{id:"what-does-it-do",answerText:__("I don't know what it does.","jetpack-connection")}],u="another-reason",m=(0,r.useCallback)((()=>{n(s,s===u?i:"")}),[n,u,i,s]),g=(0,r.useCallback)((e=>{const n=e.target.value;e.stopPropagation(),d(n)}),[d]),h=e=>e===s?"jp-connect__disconnect-survey-card--selected":"",_=(0,r.useCallback)(((e,n)=>{switch(n.key){case"Enter":case"Space":case"Spacebar":case" ":c(e)}}),[c]);return(0,l.jsxs)(r.Fragment,{children:[(0,l.jsxs)("div",{className:"jp-connection__disconnect-dialog__survey",children:[p.map((e=>(0,l.jsx)(a.A,{id:e.id,onClick:c,onKeyDown:_,className:"card jp-connect__disconnect-survey-card "+h(e.id),children:(0,l.jsx)("p",{className:"jp-connect__disconnect-survey-card__answer",children:e.answerText})},e.id))),(0,l.jsx)(a.A,{id:u,onClick:c,onKeyDown:_,className:"card jp-connect__disconnect-survey-card "+h(u),children:(0,l.jsxs)("p",{className:"jp-connect__disconnect-survey-card__answer",children:[__("Other:","jetpack-connection")," ",(0,l.jsx)("input",{placeholder:__("share your experience","jetpack-connection"),className:"jp-connect__disconnect-survey-card__input",type:"text",value:i,onChange:g,maxLength:1e3})]})},u)]}),(0,l.jsx)("p",{children:(0,l.jsx)(o.Button,{disabled:!s||t,variant:"primary",onClick:m,className:"jp-connection__disconnect-dialog__btn-back-to-wp",children:t?__("Submitting…","jetpack-connection"):__("Submit Feedback","jetpack-connection",0)})})]})};d.PropTypes={onSubmit:i().func,isSubmittingFeedback:i().bool};const p=d},8233:(e,n,t)=>{"use strict";t.d(n,{A:()=>c});var o=t(1609),s=(t(7556),t(790));const c=e=>{const{id:n,onClick:t,onKeyDown:c,children:i,className:r}=e,a=(0,o.useCallback)((()=>{t(n)}),[n,t]),l=(0,o.useCallback)((e=>{c(n,e)}),[n,c]);return(0,s.jsx)("div",{tabIndex:"0",role:"button",onClick:a,onKeyDown:l,className:"card jp-connect__disconnect-survey-card "+r,children:i})}},7018:(e,n,t)=>{"use strict";t.d(n,{A:()=>l});var o=t(7723),s=t(3619),c=t.n(s),i=t(1609),r=(t(4317),t(790));const __=o.__,a=e=>{const{title:n,isLoading:t=!1,width:o="100%",displayTOS:s,scrollToIframe:c=!1,connectUrl:a,onComplete:l,onThirdPartyCookiesBlocked:d,location:p}=e;let{height:u="300"}=e;const m=(0,i.useRef)(void 0),g=(0,i.useRef)(void 0),h=e=>{if(g.current&&e.source===g.current.contentWindow)switch(e.data){case"close":window.removeEventListener("message",h),l&&l();break;case"wpcom_nocookie":d&&d()}};(0,i.useEffect)((()=>{c&&window.scrollTo(0,m.current.offsetTop-10),window.addEventListener("message",h)}));let _=a.replace("authorize/","authorize_iframe/");return _.includes("?")||(_+="?"),s&&(_+="&display-tos",u=(parseInt(u)+50).toString()),_+="&iframe_height="+parseInt(u),p&&(_+="&iframe_source="+p),(0,r.jsxs)("div",{className:"dops-card fade-in jp-iframe-wrap",ref:m,children:[(0,r.jsx)("h1",{children:n}),t?(0,r.jsx)("p",{children:__("Loading…","jetpack-connection")}):(0,r.jsx)("iframe",{title:n,width:o,height:u,src:_,ref:g})]})};a.propTypes={title:c().string.isRequired,isLoading:c().bool,width:c().string,height:c().string,connectUrl:c().string.isRequired,displayTOS:c().bool.isRequired,scrollToIframe:c().bool,onComplete:c().func,onThirdPartyCookiesBlocked:c().func,location:c().string};const l=a},4981:(e,n,t)=>{"use strict";t.d(n,{A:()=>A});var o=t(372),s=t(5932),c=t(7425),i=t(3924),r=t(1112),a=t(7999),l=t(6427),d=t(6087),p=t(7723),u=t(7750),m=t(8391),g=t(1386),h=t(2231),_=t(3619),f=t.n(_),y=t(1609),b=t(7088),j=t(3269),k=t(3735),v=(t(4046),t(790));const __=p.__,C=e=>{const{title:n=__("Manage your Jetpack connection","jetpack-connection"),apiRoot:t,apiNonce:r,connectedPlugins:d,onDisconnected:p,onUnlinked:u,context:m="jetpack-dashboard",connectedUser:g={},connectedSiteId:h,isOpen:_=!1,onClose:f}=e,[C,A]=(0,y.useState)(!1),[N,S]=(0,y.useState)(!1),[E,T]=(0,y.useState)(""),[R,O]=(0,y.useState)(!1);(0,y.useEffect)((()=>{s.Ay.setApiRoot(t),s.Ay.setApiNonce(r)}),[t,r]);const L=(0,y.useCallback)((e=>{e&&e.preventDefault(),A(!0)}),[A]),I=(0,y.useCallback)((e=>{e&&e.preventDefault(),A(!1)}),[A]),P=(0,y.useMemo)((()=>!!g.currentUser?.permissions?.manage_options),[g.currentUser]),D=(0,y.useCallback)((()=>{g.currentUser?.isConnected&&(S(!0),T(""),s.Ay.unlinkUser(P).then((()=>{S(!1),f(),u()})).catch((()=>{let e=__("There was some trouble disconnecting your user account, your Jetpack plugin(s) may be outdated. Please visit your plugins page and make sure all Jetpack plugins are updated.","jetpack-connection");P||(e=__("There was some trouble disconnecting your user account, your Jetpack plugin(s) may be outdated. Please ask a site admin to update Jetpack","jetpack-connection")),T(e),S(!1)})))}),[S,T,P,u,f,g]),U=(0,y.useCallback)((e=>{e&&e.preventDefault(),g.currentUser?.isMaster?O(!0):(o.A.tracks.recordEvent("jetpack_manage_connection_dialog_disconnect_user_click",{context:m}),D())}),[D,m,g]),F=(0,y.useMemo)((()=>N),[N]),B=__("Disconnecting…","jetpack-connection"),M=(0,y.useCallback)((()=>{O(!1)}),[O]);return(0,v.jsx)(v.Fragment,{children:_&&(0,v.jsxs)(v.Fragment,{children:[(0,v.jsxs)(l.Modal,{title:"",contentLabel:n,aria:{labelledby:"jp-connection__manage-dialog__heading"},shouldCloseOnClickOutside:!1,shouldCloseOnEsc:!1,isDismissible:!1,className:"jp-connection__manage-dialog",children:[(0,v.jsxs)("div",{className:"jp-connection__manage-dialog__content",children:[(0,v.jsx)("h1",{id:"jp-connection__manage-dialog__heading",children:n}),(0,v.jsx)(c.Ay,{className:"jp-connection__manage-dialog__large-text",children:__("At least one user must be connected for your Jetpack products to work properly.","jetpack-connection")}),P&&g.currentUser?.isConnected&&g.currentUser?.isMaster&&(0,v.jsx)(x,{title:__("Transfer ownership to another admin","jetpack-connection"),link:(0,i.A)("calypso-settings-manage-connection",{site:window?.myJetpackInitialState?.siteSuffix}),isExternal:!0,action:"transfer",disabled:F},"transfer"),g.currentUser?.isConnected&&(0,v.jsxs)(v.Fragment,{children:[""!==E&&(0,v.jsx)(b.A,{message:E}),(0,v.jsx)(x,{title:N?B:__("Disconnect my user account","jetpack-connection"),onClick:U,action:"unlink",disabled:F},"unlink")]}),P&&!(0,a.isWoASite)()&&(0,v.jsx)(x,{title:__("Disconnect Jetpack","jetpack-connection"),onClick:L,action:"disconnect",disabled:F},"disconnect")]}),(0,v.jsx)(w,{onClose:f,disabled:F})]}),(0,v.jsx)(j.A,{apiRoot:t,apiNonce:r,onDisconnected:p,connectedPlugins:d,connectedSiteId:h,connectedUser:g,isOpen:C,onClose:I,context:m}),(0,v.jsx)(k.A,{isOpen:R,onClose:M,apiRoot:t,apiNonce:r,onDisconnected:p,onUnlinked:u})]})})},x=({title:e,onClick:n=()=>null,isExternal:t=!1,link:o="#",action:s,disabled:c=!1})=>{const i=(0,y.useCallback)((e=>e.preventDefault()),[]);return(0,v.jsx)("div",{className:"jp-connection__manage-dialog__action-card card"+(c?" disabled":""),children:(0,v.jsx)("div",{className:"jp-connection__manage-dialog__action-card__card-content",children:(0,v.jsxs)("a",{href:o,className:(0,h.A)("jp-connection__manage-dialog__action-card__card-headline",s),onClick:c?i:n,target:t?"_blank":"_self",rel:"noopener noreferrer",children:[e,(0,v.jsx)(u.A,{icon:t?m.A:g.A,className:"jp-connection__manage-dialog__action-card__icon"})]})})})},w=({onClose:e,disabled:n})=>(0,v.jsxs)("div",{className:"jp-row jp-connection__manage-dialog__actions",children:[(0,v.jsx)("div",{className:"jp-connection__manage-dialog__text-wrap lg-col-span-9 md-col-span-7 sm-col-span-3",children:(0,v.jsx)(c.Ay,{children:(0,d.createInterpolateElement)(__("<strong>Need help?</strong> Learn more about the <connectionInfoLink>Jetpack connection</connectionInfoLink> or <supportLink>contact Jetpack support</supportLink>","jetpack-connection"),{strong:(0,v.jsx)("strong",{}),connectionInfoLink:(0,v.jsx)(l.ExternalLink,{href:(0,i.A)("why-the-wordpress-com-connection-is-important-for-jetpack"),className:"jp-connection__manage-dialog__link"}),supportLink:(0,v.jsx)(l.ExternalLink,{href:(0,i.A)("jetpack-support"),className:"jp-connection__manage-dialog__link"})})})}),(0,v.jsx)("div",{className:"jp-connection__manage-dialog__button-wrap lg-col-span-3 md-col-span-1 sm-col-span-1",children:(0,v.jsx)(r.A,{weight:"regular",variant:"secondary",onClick:e,className:"jp-connection__manage-dialog__btn-dismiss",disabled:n,children:__("Cancel","jetpack-connection")})})]});C.propTypes={title:f().string,apiRoot:f().string.isRequired,apiNonce:f().string.isRequired,connectedPlugins:f().oneOfType([f().array,f().object]),onDisconnected:f().func,onUnlinked:f().func,context:f().string,connectedUser:f().object,connectedSiteId:f().number,isOpen:f().bool,onClose:f().func};const A=C},3735:(e,n,t)=>{"use strict";t.d(n,{A:()=>b});var o=t(372),s=t(5932),c=t(3924),i=t(6427),r=t(6087),a=t(7723),l=t(7750),d=t(8391),p=t(1386),u=t(2231),m=t(3619),g=t.n(m),h=t(1609),_=(t(7202),t(790));const __=a.__,f=({isOpen:e,onClose:n,apiRoot:t,apiNonce:a,onDisconnected:l,onUnlinked:d})=>{const[p,u]=(0,h.useState)(!1),[m,g]=(0,h.useState)(""),f=__("Disconnecting…","jetpack-connection"),b=__("Disconnect","jetpack-connection");(0,h.useEffect)((()=>{s.Ay.setApiRoot(t),s.Ay.setApiNonce(a)}),[t,a]);const j=(0,h.useCallback)((()=>{n()}),[n]),k=(0,h.useCallback)((()=>{o.A.tracks.recordEvent("jetpack_manage_connection_dialog_owner_disconnect_click"),u(!0),g(""),s.Ay.unlinkUser(!0,{disconnectAllUsers:!0}).then((()=>{o.A.tracks.recordEvent("jetpack_manage_connection_dialog_owner_disconnect_success"),l&&l(),d&&d()})).catch((()=>{o.A.tracks.recordEvent("jetpack_manage_connection_dialog_owner_disconnect_error"),g(__("There was a problem disconnecting your account. Please try again.","jetpack-connection")),u(!1)}))}),[l,d]);return e&&(0,_.jsxs)(i.Modal,{title:"",contentLabel:__("Disconnect Owner Account","jetpack-connection"),aria:{labelledby:"jp-connection__disconnect-dialog__heading"},onRequestClose:j,className:"jp-connection__disconnect-dialog",children:[(0,_.jsxs)("div",{className:"jp-connection__disconnect-dialog__content",children:[(0,_.jsx)("h1",{id:"jp-connection__disconnect-dialog__heading",children:__("Disconnect Owner Account","jetpack-connection")}),(0,_.jsx)("p",{className:"jp-connection__disconnect-dialog__large-text",children:__("Disconnecting the owner account will remove the Jetpack connection for all users on this site. The site will remain connected.","jetpack-connection")}),(0,_.jsx)(y,{title:__("Transfer ownership to another admin","jetpack-connection"),link:(0,c.A)("calypso-settings-manage-connection",{site:window?.myJetpackInitialState?.siteSuffix}),isExternal:!0,action:"transfer"}),(0,_.jsx)(y,{title:__("View other connected accounts","jetpack-connection"),link:"users.php",action:"check-users"})]}),(0,_.jsxs)("div",{className:"jp-connection__disconnect-dialog__actions",children:[(0,_.jsxs)("div",{className:"jp-row",children:[(0,_.jsx)("div",{className:"lg-col-span-8 md-col-span-9 sm-col-span-4",children:(0,_.jsx)("p",{children:(0,r.createInterpolateElement)(__("<strong>Need help?</strong> Learn more about the <connectionInfoLink>Jetpack connection</connectionInfoLink> or <supportLink>contact Jetpack support</supportLink>","jetpack-connection"),{strong:(0,_.jsx)("strong",{}),connectionInfoLink:(0,_.jsx)(i.ExternalLink,{href:(0,c.A)("why-the-wordpress-com-connection-is-important-for-jetpack"),className:"jp-connection__disconnect-dialog__link"}),supportLink:(0,_.jsx)(i.ExternalLink,{href:(0,c.A)("jetpack-support"),className:"jp-connection__disconnect-dialog__link"})})})}),(0,_.jsxs)("div",{className:"jp-connection__disconnect-dialog__button-wrap lg-col-span-4 md-col-span-7 sm-col-span-4",children:[(0,_.jsx)(i.Button,{variant:"primary",onClick:j,className:"jp-connection__disconnect-dialog__btn-dismiss",children:__("Stay Connected","jetpack-connection")}),(0,_.jsx)(i.Button,{variant:"primary",onClick:k,className:"jp-connection__disconnect-dialog__btn-disconnect",isDestructive:!0,disabled:p,children:p?f:b})]})]}),m&&(0,_.jsx)("p",{className:"jp-connection__disconnect-dialog__error",children:m})]})]})};f.propTypes={isOpen:g().bool,onClose:g().func,apiRoot:g().string.isRequired,apiNonce:g().string.isRequired,onDisconnected:g().func,onUnlinked:g().func};const y=({title:e,onClick:n=()=>null,isExternal:t=!1,link:o="#",action:s,disabled:c=!1})=>{const i=(0,h.useCallback)((e=>e.preventDefault()),[]);return(0,_.jsx)("div",{className:"jp-connection__manage-dialog__action-card card"+(c?" disabled":""),children:(0,_.jsx)("div",{className:"jp-connection__manage-dialog__action-card__card-content",children:(0,_.jsxs)("a",{href:o,className:(0,u.A)("jp-connection__manage-dialog__action-card__card-headline",s),onClick:c?i:n,target:t?"_blank":"_self",rel:"noopener noreferrer",children:[e,(0,_.jsx)(l.A,{icon:t?d.A:p.A,className:"jp-connection__manage-dialog__action-card__icon"})]})})})},b=f},9660:(e,n,t)=>{"use strict";t.d(n,{A:()=>l});var o=t(5932),s=t(7999),c=t(7143),i=t(1609),r=t(4293);const a=window?.JP_CONNECTION_INITIAL_STATE||(0,s.getScriptData)()?.connection||{};function l({registrationNonce:e=a.registrationNonce,apiRoot:n=a.apiRoot,apiNonce:t=a.apiNonce,redirectUri:s,autoTrigger:l,from:d,skipUserConnection:p,skipPricingPage:u}={}){const{registerSite:m,connectUser:g,refreshConnectedPlugins:h}=(0,c.useDispatch)(r.a),_=(0,c.useSelect)((e=>e(r.a).getRegistrationError())),{siteIsRegistering:f,userIsConnecting:y,userConnectionData:b,connectedPlugins:j,connectionErrors:k,isRegistered:v,isUserConnected:C,hasConnectedOwner:x,isOfflineMode:w}=(0,c.useSelect)((e=>({siteIsRegistering:e(r.a).getSiteIsRegistering(),userIsConnecting:e(r.a).getUserIsConnecting(),userConnectionData:e(r.a).getUserConnectionData(),connectedPlugins:e(r.a).getConnectedPlugins(),connectionErrors:e(r.a).getConnectionErrors(),isOfflineMode:e(r.a).getIsOfflineMode(),...e(r.a).getConnectionStatus()}))),A=()=>p?s?(window.location=s,Promise.resolve(s)):Promise.resolve():g({from:d,redirectUri:s,skipPricingPage:u}),N=n=>(n&&n.preventDefault(),v?A():m({registrationNonce:e,redirectUri:s,from:d}).then((()=>A())));return(0,i.useEffect)((()=>{o.Ay.setApiRoot(n),o.Ay.setApiNonce(t)}),[n,t]),(0,i.useEffect)((()=>{!l||f||y||N()}),[]),{handleRegisterSite:N,handleConnectUser:A,refreshConnectedPlugins:h,isRegistered:v,isUserConnected:C,siteIsRegistering:f,userIsConnecting:y,registrationError:_,userConnectionData:b,hasConnectedOwner:x,connectedPlugins:j,connectionErrors:k,isOfflineMode:w}}},3765:(e,n,t)=>{"use strict";t.d(n,{A:()=>s});var o=t(7999);function s(){const e=("undefined"!=typeof window&&window?.JP_CONNECTION_INITIAL_STATE||(0,o.getScriptData)()?.connection)?.calypsoEnv;switch(e){case"development":return"http://calypso.localhost:3000/";case"wpcalypso":return"https://wpcalypso.wordpress.com/";case"horizon":return"https://horizon.wordpress.com/";default:return"https://wordpress.com/"}}},4617:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o=e=>{window.location.replace(e)}},9628:(e,n,t)=>{"use strict";t.d(n,{A:()=>a,R:()=>l});var o=t(7723),s=t(7088),c=t(9660),i=t(1713),r=t(790);const __=o.__;function a(){const{connectionErrors:e}=(0,c.A)({}),n=Object.values(e).shift(),t=n&&Object.values(n).length&&Object.values(n).shift(),o=t&&t.error_message;return{hasConnectionError:Boolean(o),connectionErrorMessage:o,connectionError:t,connectionErrors:e}}const l=({actionHandlers:e={},trackingCallback:n=null,customActions:t=null}={})=>{const{hasConnectionError:o,connectionErrorMessage:c,connectionError:l}=a(),{restoreConnection:d,isRestoringConnection:p,restoreConnectionError:u}=(0,i.A)();if(!o)return null;let m=[];if(t)try{m=t(l,{restoreConnection:d,isRestoringConnection:p})}catch{m=[]}else{const t=l?.error_data||{},o=t.action,s=e[o];if(o&&s){const e=t.action_label||__("Take Action","jetpack-connection"),o=t.action_variant||"primary",c=t.tracking_event;m=[{label:e,onClick:()=>{try{n&&c&&n(c,{}),s(l)}catch{}},variant:o}]}else if(t.action_url&&t.action_label){const e=t.action_label,o=t.action_variant||"primary",s=t.tracking_event;m=[{label:e,onClick:()=>{try{n&&s&&n(s,{}),window.location.href=t.action_url}catch{}},variant:o}]}else m=[{label:__("Restore Connection","jetpack-connection"),onClick:()=>{try{n&&n("jetpack_connection_error_notice_reconnect_cta_click",{}),d()}catch{}},isLoading:p,loadingText:__("Reconnecting Jetpack…","jetpack-connection")}];if(m.length>0&&(o||t.action_url)){const o=t.secondary_action,s=e[o],c=t.secondary_action_url,i=t.secondary_action_label;if(o&&s&&i){const e=t.secondary_action_variant||"secondary",o=t.secondary_tracking_event;m.push({label:i,onClick:()=>{try{n&&o&&n(o,{}),s(l)}catch{}},variant:e})}else if(c&&i){const e=t.secondary_action_variant||"secondary",o=t.secondary_tracking_event;m.push({label:i,onClick:()=>{try{n&&o&&n(o,{}),window.location.href=c}catch{}},variant:e})}}}return 0!==m.length||t?(0,r.jsx)(s.A,{isRestoringConnection:p,restoreConnectionError:u,restoreConnectionCallback:0===m.length?d:null,message:c,actions:m}):null}},2558:(e,n,t)=>{"use strict";t.d(n,{A:()=>y});var o=t(5932),s=t(7999),c=t(7143),i=t(4804),r=t.n(i),a=t(1609),l=t(3765),d=t(9660),p=t(4293);const u=r()("jetpack:connection:useProductCheckoutWorkflow"),{registrationNonce:m,apiRoot:g,apiNonce:h,siteSuffix:_}=window?.JP_CONNECTION_INITIAL_STATE||(0,s.getScriptData)()?.connection||{},f=()=>"undefined"!=typeof window?window?.myJetpackInitialState?.adminUrl:null;function y({productSlug:e,redirectUrl:n,siteSuffix:t=_,adminUrl:s=f(),connectAfterCheckout:i=!1,siteProductAvailabilityHandler:r=null,quantity:y=null,from:b,useBlogIdSuffix:j=!1}={}){u("productSlug is %s",e),u("redirectUrl is %s",n),u("siteSuffix is %s",t),u("from is %s",b);const[k,v]=(0,a.useState)(!1),{registerSite:C}=(0,c.useDispatch)(p.a),x=(0,c.useSelect)((e=>e(p.a).getBlogId()),[]);u("blogID is %s",x??"undefined"),j=j&&!!x;const{isUserConnected:w,isRegistered:A,handleConnectUser:N}=(0,d.A)({redirectUri:n,from:b}),S=(0,a.useMemo)((()=>{const o=(0,l.A)(),c=(!A||!w)&&i,r=c?"checkout/jetpack/":`checkout/${j?x.toString():t}/`,a=new URL(`${o}${r}${e}${null!=y?`:-q-${y}`:""}`);return c?(a.searchParams.set("connect_after_checkout",!0),a.searchParams.set("admin_url",s),a.searchParams.set("from_site_slug",t)):a.searchParams.set("site",t),a.searchParams.set("source",b),a.searchParams.set("redirect_to",n),w||a.searchParams.set("unlinked","1"),a}),[A,w,i,t,y,e,b,n,s,j,x]);u("isRegistered is %s",A),u("isUserConnected is %s",w),u("connectAfterCheckout is %s",i),u("checkoutUrl is %s",S);const E=(e=null)=>Promise.resolve(r&&r()).then((n=>{if(e&&S.searchParams.set("redirect_to",e),n)return u("handleAfterRegistration: Site has a product associated"),N();u("handleAfterRegistration: Site does not have a product associated. Redirecting to checkout %s",S),window.location.href=S}));return(0,a.useEffect)((()=>{o.Ay.setApiRoot(g),o.Ay.setApiNonce(h)}),[]),{run:(e,t=null)=>(e&&e.preventDefault(),v(!0),i?((e=null)=>{e&&S.searchParams.set("redirect_to",e),u("Redirecting to connectAfterCheckout flow: %s",S),window.location.href=S})(t):A?E(t):void C({registrationNonce:m,redirectUri:n}).then((()=>E(t)))),isRegistered:A,hasCheckoutStarted:k}}},1713:(e,n,t)=>{"use strict";t.d(n,{A:()=>d});var o=t(5932),s=t(7999),c=t(7143),i=t(1609),r=t(4293);const{apiRoot:a,apiNonce:l}=window?.JP_CONNECTION_INITIAL_STATE||(0,s.getScriptData)()?.connection||{};function d(){const[e,n]=(0,i.useState)(!1),[t,s]=(0,i.useState)(null),{disconnectUserSuccess:d,setConnectionErrors:p}=(0,c.useDispatch)(r.a);return(0,i.useEffect)((()=>{o.Ay.setApiRoot(a),o.Ay.setApiNonce(l)}),[]),{restoreConnection:(e=!0)=>(n(!0),s(null),o.Ay.reconnect().then((n=>("in_progress"===n.status?(d(),p({}),e&&(window.location.href="/wp-admin/admin.php?page=my-jetpack#/connection")):window.location.reload(),n))).catch((e=>{throw s(e),n(!1),e}))),isRestoringConnection:e,restoreConnectionError:t}}},8980:(e,n,t)=>{"use strict";t.d(n,{AY:()=>g.A,F0:()=>o.A,Hx:()=>f.a,JC:()=>l.A,Jl:()=>s.A,Ni:()=>u.A,Ob:()=>b.A,Rc:()=>d.R,Sx:()=>d.A,ag:()=>_.A,bo:()=>p.A,cS:()=>y.A,d1:()=>h.A,mX:()=>a.A,nM:()=>c.A,pK:()=>i.A,w5:()=>m.A,xW:()=>r.A});var o=t(6212),s=t(2668),c=t(7945),i=t(8421),r=t(7018),a=t(7840),l=t(7088),d=t(9628),p=t(3269),u=t(7499),m=t(9660),g=t(4981),h=t(4617),_=t(3765),f=t(4293),y=t(2558),b=t(1713)},3935:(e,n,t)=>{"use strict";t.d(n,{A1:()=>a,Ay:()=>v,DO:()=>r,Ij:()=>i,Kl:()=>m,LW:()=>l,MU:()=>g,XY:()=>d,ZO:()=>c,dz:()=>p,gH:()=>u,v_:()=>s});var o=t(5932);const s="SET_CONNECTION_STATUS",c="SET_CONNECTION_STATUS_IS_FETCHING",i="SET_SITE_IS_REGISTERING",r="SET_USER_IS_CONNECTING",a="SET_REGISTRATION_ERROR",l="CLEAR_REGISTRATION_ERROR",d="SET_AUTHORIZATION_URL",p="DISCONNECT_USER_SUCCESS",u="SET_CONNECTED_PLUGINS",m="SET_CONNECTION_ERRORS",g="SET_IS_OFFLINE_MODE",h=e=>({type:s,connectionStatus:e}),_=e=>({type:i,isRegistering:e}),f=e=>({type:r,isConnecting:e}),y=e=>({type:a,registrationError:e}),b=()=>({type:l}),j=e=>({type:d,authorizationUrl:e}),k=e=>({type:u,connectedPlugins:e});const v={setConnectionStatus:h,setConnectionStatusIsFetching:e=>({type:c,isFetching:e}),fetchConnectionStatus:()=>({type:"FETCH_CONNECTION_STATUS"}),fetchAuthorizationUrl:e=>({type:"FETCH_AUTHORIZATION_URL",redirectUri:e}),setSiteIsRegistering:_,setUserIsConnecting:f,setRegistrationError:y,clearRegistrationError:b,setAuthorizationUrl:j,registerSite:function*({registrationNonce:e,redirectUri:n,from:t=""}){yield b(),yield _(!0);try{const o=yield{type:"REGISTER_SITE",registrationNonce:e,redirectUri:n,from:t};return yield h({isRegistered:!0}),yield j(o.authorizeUrl),yield _(!1),Promise.resolve(o)}catch(e){return yield y(e),yield _(!1),Promise.reject(e)}},connectUser:function*({from:e,redirectFunc:n,redirectUri:t,skipPricingPage:o}={}){yield f(!0),yield{type:"CONNECT_USER",from:e,redirectFunc:n,redirectUri:t,skipPricingPage:o}},disconnectUserSuccess:()=>({type:p}),setConnectedPlugins:k,refreshConnectedPlugins:()=>async({dispatch:e})=>await new Promise((n=>o.Ay.fetchConnectedPlugins().then((t=>{e(k(t)),n(t)})))),setConnectionErrors:e=>({type:m,connectionErrors:e}),setIsOfflineMode:e=>({type:g,isOfflineMode:e})}},7938:(e,n,t)=>{"use strict";function o(e){return window.location.assign(e)}t.d(n,{d:()=>o})},2494:(e,n,t)=>{"use strict";t.d(n,{A:()=>r});var o=t(5932),s=t(7143),c=t(7938),i=t(2279);const r={FETCH_AUTHORIZATION_URL:({redirectUri:e})=>o.Ay.fetchAuthorizationUrl(e),REGISTER_SITE:({redirectUri:e,from:n})=>o.Ay.registerSite(null,e,n),CONNECT_USER:(0,s.createRegistryControl)((({resolveSelect:e})=>({from:n,redirectFunc:t,redirectUri:o,skipPricingPage:s}={})=>new Promise(((r,a)=>{e(i.A).getAuthorizationUrl(o).then((e=>{const o=t||(e=>(0,c.d)(e)),i=new URL(e);s&&i.searchParams.set("skip_pricing","true"),n&&i.searchParams.set("from",encodeURIComponent(n));const a=i.toString();o(a),r(a)})).catch((e=>{a(e)}))}))))}},5051:(e,n,t)=>{"use strict";t.d(n,{A:()=>c});var o=t(7143),s=t(3935);const c=(0,o.combineReducers)({connectionStatus:(e={},n)=>{switch(n.type){case s.v_:return{...e,...n.connectionStatus};case s.dz:return{...e,isUserConnected:!1}}return e},connectionStatusIsFetching:(e=!1,n)=>n.type===s.ZO?n.isFetching:e,siteIsRegistering:(e=!1,n)=>n.type===s.Ij?n.isRegistering:e,userIsConnecting:(e=!1,n)=>n.type===s.DO?n.isConnecting:e,registrationError:(e,n)=>{switch(n.type){case s.LW:return!1;case s.A1:return n.registrationError;default:return e}},authorizationUrl:(e,n)=>n.type===s.XY?n.authorizationUrl:e,userConnectionData:(e,n)=>(n.type,e),connectedPlugins:(e={},n)=>n.type===s.gH?n.connectedPlugins:e,connectionErrors:(e={},n)=>n.type===s.Kl?n.connectionErrors:e,isOfflineMode:(e=!1,n)=>n.type===s.MU?n.isConnecting:e})},8019:(e,n,t)=>{"use strict";t.d(n,{A:()=>i});var o=t(7143),s=t(3935),c=t(2279);const i={...{getAuthorizationUrl:{isFulfilled:(e,...n)=>{const t=Boolean(e.authorizationUrl),s=(0,o.select)(c.A).hasFinishedResolution("getAuthorizationUrl",n);return t&&!s&&(0,o.dispatch)(c.A).finishResolution("getAuthorizationUrl",n),t},*fulfill(e){const n=yield s.Ay.fetchAuthorizationUrl(e);yield s.Ay.setAuthorizationUrl(n.authorizeUrl)}}}}},2676:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o={...{getConnectionStatus:e=>e.connectionStatus||{},getConnectionStatusIsFetching:()=>!1,getSiteIsRegistering:e=>e.siteIsRegistering||!1,getUserIsConnecting:e=>e.userIsConnecting||!1,getRegistrationError:e=>e.registrationError||!1,getAuthorizationUrl:e=>e.authorizationUrl||!1,getUserConnectionData:e=>e.userConnectionData||!1,getConnectedPlugins:e=>e.connectedPlugins||[],getConnectionErrors:e=>e.connectionErrors||[],getIsOfflineMode:e=>e.isOfflineMode||!1,getWpcomUser:e=>e?.userConnectionData?.currentUser?.wpcomUser,getBlogId:e=>e?.userConnectionData?.currentUser?.blogId}}},8734:(e,n,t)=>{"use strict";t.d(n,{A:()=>c});var o=t(7143);class s{static store=null;static mayBeInit(e,n){null===s.store&&(s.store=(0,o.createReduxStore)(e,n),(0,o.register)(s.store))}}const c=s},2279:(e,n,t)=>{"use strict";t.d(n,{A:()=>o});const o="jetpack-connection"},4293:(e,n,t)=>{"use strict";t.d(n,{a:()=>d.A});var o=t(7999),s=t(3935),c=t(2494),i=t(5051),r=t(8019),a=t(2676),l=t(8734),d=t(2279);const p=window.JP_CONNECTION_INITIAL_STATE||(0,o.getScriptData)()?.connection;p||console.error("Jetpack Connection package: Initial state is missing. Check documentation to see how to use the Connection composer package to set up the initial state."),l.A.mayBeInit(d.A,{__experimentalUseThunks:!0,reducer:i.A,actions:s.Ay,selectors:a.A,resolvers:r.A,controls:c.A,initialState:p||{}})},3673:(e,n,t)=>{"use strict";t.d(n,{$:()=>s,M:()=>o});const o="en",s="USD"},1452:(e,n,t)=>{"use strict";t.d(n,{A:()=>r});var o=t(8443),s=t(3673),c=t(9980),i=t(1167);const r=function(){let e,n;const t=()=>{const{l10n:{locale:n}}=(0,o.getSettings)();return(e??(n||window?.window?.navigator?.language)??s.M).split("_")[0]};return{setLocale:n=>{e=n},setGeoLocation:e=>{n=e},formatNumber:(e,{decimals:n=0,forceLatin:o=!0,numberFormatOptions:s={}}={})=>{try{return(0,i.j)({browserSafeLocale:t(),decimals:n,forceLatin:o,numberFormatOptions:s}).format(e)}catch{return String(e)}},formatNumberCompact:(e,{decimals:n=0,forceLatin:o=!0,numberFormatOptions:s={}}={})=>{try{return(0,i.c)({browserSafeLocale:t(),decimals:n,forceLatin:o,numberFormatOptions:s}).format(e)}catch{return String(e)}},formatCurrency:(e,o,{stripZeros:s=!1,isSmallestUnit:i=!1,signForPositive:r=!1,forceLatin:a=!0}={})=>(0,c.u)({number:e,currency:o,browserSafeLocale:t(),stripZeros:s,isSmallestUnit:i,signForPositive:r,geoLocation:n,forceLatin:a}),getCurrencyObject:(e,o,{stripZeros:s=!1,isSmallestUnit:i=!1,signForPositive:r=!1,forceLatin:a=!0}={})=>(0,c.v)({number:e,currency:o,browserSafeLocale:t(),stripZeros:s,isSmallestUnit:i,signForPositive:r,geoLocation:n,forceLatin:a})}}},3328:(e,n,t)=>{"use strict";t.d(n,{J:()=>a});var o=t(4804),s=t.n(o),c=t(3673);const i=s()("number-formatters:get-cached-formatter"),r=new Map;function a({locale:e,fallbackLocale:n=c.M,options:t,retries:o=1}){const s=JSON.stringify([e,t]);try{return r.get(s)??r.set(s,new Intl.NumberFormat(e,t)).get(s)}catch(s){if(i(`Intl.NumberFormat was called with a non-existent locale "${e}"; falling back to ${n}`),o)return a({locale:n,options:t,retries:o-1});throw s}}},4268:(e,n,t)=>{"use strict";t.d(n,{vA:()=>l});const o=(0,t(1452).A)(),{setLocale:s,setGeoLocation:c,formatNumber:i,formatNumberCompact:r,formatCurrency:a,getCurrencyObject:l}=o},6673:(e,n,t)=>{"use strict";t.d(n,{a:()=>o});const o={AED:{symbol:"د.إ.‏"},AFN:{symbol:"؋"},ALL:{symbol:"Lek"},AMD:{symbol:"֏"},ANG:{symbol:"ƒ"},AOA:{symbol:"Kz"},ARS:{symbol:"$"},AUD:{symbol:"A$"},AWG:{symbol:"ƒ"},AZN:{symbol:"₼"},BAM:{symbol:"КМ"},BBD:{symbol:"Bds$"},BDT:{symbol:"৳"},BGN:{symbol:"лв."},BHD:{symbol:"د.ب.‏"},BIF:{symbol:"FBu"},BMD:{symbol:"$"},BND:{symbol:"$"},BOB:{symbol:"Bs"},BRL:{symbol:"R$"},BSD:{symbol:"$"},BTC:{symbol:"Ƀ"},BTN:{symbol:"Nu."},BWP:{symbol:"P"},BYR:{symbol:"р."},BZD:{symbol:"BZ$"},CAD:{symbol:"C$"},CDF:{symbol:"FC"},CHF:{symbol:"CHF"},CLP:{symbol:"$"},CNY:{symbol:"¥"},COP:{symbol:"$"},CRC:{symbol:"₡"},CUC:{symbol:"CUC"},CUP:{symbol:"$MN"},CVE:{symbol:"$"},CZK:{symbol:"Kč"},DJF:{symbol:"Fdj"},DKK:{symbol:"kr."},DOP:{symbol:"RD$"},DZD:{symbol:"د.ج.‏"},EGP:{symbol:"ج.م.‏"},ERN:{symbol:"Nfk"},ETB:{symbol:"ETB"},EUR:{symbol:"€"},FJD:{symbol:"FJ$"},FKP:{symbol:"£"},GBP:{symbol:"£"},GEL:{symbol:"Lari"},GHS:{symbol:"₵"},GIP:{symbol:"£"},GMD:{symbol:"D"},GNF:{symbol:"FG"},GTQ:{symbol:"Q"},GYD:{symbol:"G$"},HKD:{symbol:"HK$"},HNL:{symbol:"L."},HRK:{symbol:"kn"},HTG:{symbol:"G"},HUF:{symbol:"Ft"},IDR:{symbol:"Rp"},ILS:{symbol:"₪"},INR:{symbol:"₹"},IQD:{symbol:"د.ع.‏"},IRR:{symbol:"﷼"},ISK:{symbol:"kr."},JMD:{symbol:"J$"},JOD:{symbol:"د.ا.‏"},JPY:{symbol:"¥"},KES:{symbol:"S"},KGS:{symbol:"сом"},KHR:{symbol:"៛"},KMF:{symbol:"CF"},KPW:{symbol:"₩"},KRW:{symbol:"₩"},KWD:{symbol:"د.ك.‏"},KYD:{symbol:"$"},KZT:{symbol:"₸"},LAK:{symbol:"₭"},LBP:{symbol:"ل.ل.‏"},LKR:{symbol:"₨"},LRD:{symbol:"L$"},LSL:{symbol:"M"},LYD:{symbol:"د.ل.‏"},MAD:{symbol:"د.م.‏"},MDL:{symbol:"lei"},MGA:{symbol:"Ar"},MKD:{symbol:"ден."},MMK:{symbol:"K"},MNT:{symbol:"₮"},MOP:{symbol:"MOP$"},MRO:{symbol:"UM"},MTL:{symbol:"₤"},MUR:{symbol:"₨"},MVR:{symbol:"MVR"},MWK:{symbol:"MK"},MXN:{symbol:"MX$"},MYR:{symbol:"RM"},MZN:{symbol:"MT"},NAD:{symbol:"N$"},NGN:{symbol:"₦"},NIO:{symbol:"C$"},NOK:{symbol:"kr"},NPR:{symbol:"₨"},NZD:{symbol:"NZ$"},OMR:{symbol:"﷼"},PAB:{symbol:"B/."},PEN:{symbol:"S/."},PGK:{symbol:"K"},PHP:{symbol:"₱"},PKR:{symbol:"₨"},PLN:{symbol:"zł"},PYG:{symbol:"₲"},QAR:{symbol:"﷼"},RON:{symbol:"lei"},RSD:{symbol:"Дин."},RUB:{symbol:"₽"},RWF:{symbol:"RWF"},SAR:{symbol:"﷼"},SBD:{symbol:"S$"},SCR:{symbol:"₨"},SDD:{symbol:"LSd"},SDG:{symbol:"£‏"},SEK:{symbol:"kr"},SGD:{symbol:"S$"},SHP:{symbol:"£"},SLL:{symbol:"Le"},SOS:{symbol:"S"},SRD:{symbol:"$"},STD:{symbol:"Db"},SVC:{symbol:"₡"},SYP:{symbol:"£"},SZL:{symbol:"E"},THB:{symbol:"฿"},TJS:{symbol:"TJS"},TMT:{symbol:"m"},TND:{symbol:"د.ت.‏"},TOP:{symbol:"T$"},TRY:{symbol:"TL"},TTD:{symbol:"TT$"},TVD:{symbol:"$T"},TWD:{symbol:"NT$"},TZS:{symbol:"TSh"},UAH:{symbol:"₴"},UGX:{symbol:"USh"},USD:{},UYU:{symbol:"$U"},UZS:{symbol:"сўм"},VEB:{symbol:"Bs."},VEF:{symbol:"Bs. F."},VND:{symbol:"₫"},VUV:{symbol:"VT"},WST:{symbol:"WS$"},XAF:{symbol:"F"},XCD:{symbol:"$"},XOF:{symbol:"F"},XPF:{symbol:"F"},YER:{symbol:"﷼"},ZAR:{symbol:"R"},ZMW:{symbol:"ZK"},WON:{symbol:"₩"}}},9980:(e,n,t)=>{"use strict";t.d(n,{u:()=>h,v:()=>_});var o=t(4804),s=t.n(o),c=t(3673),i=t(3328),r=t(6673);const a=s()("number-formatters:number-format-currency");function l(e,n){return"USD"===e&&n&&""!==n&&"US"!==n?{symbol:"US$"}:r.a[e]}function d(e,n){return l(e,n)?e:(a(`getValidCurrency was called with a non-existent currency "${e}"; falling back to ${c.$}`),c.$)}function p({number:e,currency:n,browserSafeLocale:t,forceLatin:o=!0,stripZeros:s,signForPositive:c}){const r=`${t}${o?"-u-nu-latn":""}`,a={style:"currency",currency:n,...s&&Number.isInteger(e)&&{maximumFractionDigits:0,minimumFractionDigits:0},...c&&{signDisplay:"exceptZero"}};return(0,i.J)({locale:r,options:a})}function u(e,n,t){return p({number:0,currency:n,browserSafeLocale:e,forceLatin:t}).resolvedOptions().maximumFractionDigits}function m(e,n){const t=Math.pow(10,n);return Math.round(e*t)/t}function g(e,n,t){if(isNaN(e))return a("formatCurrency was called with NaN"),0;if(t){Number.isInteger(e)||a("formatCurrency was called with isSmallestUnit and a float which will be rounded",e);const t=10**n;return m(Math.round(e)/t,n)}return m(e,n)}const h=({number:e,browserSafeLocale:n,currency:t,stripZeros:o,isSmallestUnit:s,signForPositive:c,geoLocation:i,forceLatin:r})=>{const a=d(t,i),m=l(a,i),h=u(n,a,r);if(s&&void 0===h)throw new Error(`Could not determine currency precision for ${a} in ${n}`);const _=g(e,h??0,s);return p({number:_,currency:a,browserSafeLocale:n,forceLatin:r,stripZeros:o,signForPositive:c}).formatToParts(_).reduce(((e,n)=>"currency"===n.type&&m?.symbol?e+m.symbol:e+n.value),"")},_=({number:e,browserSafeLocale:n,currency:t,stripZeros:o,isSmallestUnit:s,signForPositive:c,geoLocation:i,forceLatin:r})=>{const a=d(t,i),m=l(a,i),h=g(e,u(n,a,r)??0,s),_=p({number:h,currency:a,browserSafeLocale:n,forceLatin:r,stripZeros:o,signForPositive:c}).formatToParts(h);let f="",y="$",b="before",j=!1,k=!1,v="",C="";_.forEach((e=>{switch(e.type){case"currency":return y=m?.symbol??e.value,void(j&&(b="after"));case"group":case"integer":return v+=e.value,void(j=!0);case"decimal":case"fraction":return C+=e.value,j=!0,void(k=!0);case"minusSign":return void(f="-");case"plusSign":f="+"}}));const x=!Number.isInteger(h)&&k;return{sign:f,symbol:y,symbolPosition:b,integer:v,fraction:C,hasNonZeroFraction:x}}},1167:(e,n,t)=>{"use strict";t.d(n,{c:()=>c,j:()=>s});var o=t(3328);const s=({browserSafeLocale:e,decimals:n=0,forceLatin:t=!0,numberFormatOptions:s={}})=>{const c=`${e}${t?"-u-nu-latn":""}`,i={minimumFractionDigits:n,maximumFractionDigits:n,...s};return(0,o.J)({locale:c,options:i})},c=({numberFormatOptions:e={},...n})=>s({...n,numberFormatOptions:{notation:"compact",maximumFractionDigits:1,...e}})},2365:(e,n,t)=>{"use strict";e.exports=t.p+"images/disconnect-confirm-dc9fe8f5c68cfd1320e0.jpg"},9362:(e,n,t)=>{"use strict";e.exports=t.p+"images/disconnect-thanks-5873bfac56a9bd7322cd.jpg"},9074:e=>{"use strict";e.exports={consumer_slug:"connection_package"}},7999:e=>{"use strict";e.exports=window.JetpackScriptDataModule},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},6427:e=>{"use strict";e.exports=window.wp.components},7143:e=>{"use strict";e.exports=window.wp.data},8443:e=>{"use strict";e.exports=window.wp.date},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},5573:e=>{"use strict";e.exports=window.wp.primitives},3832:e=>{"use strict";e.exports=window.wp.url},2231:(e,n,t)=>{"use strict";function o(e){var n,t,s="";if("string"==typeof e||"number"==typeof e)s+=e;else if("object"==typeof e)if(Array.isArray(e)){var c=e.length;for(n=0;n<c;n++)e[n]&&(t=o(e[n]))&&(s&&(s+=" "),s+=t)}else for(t in e)e[t]&&(s&&(s+=" "),s+=t);return s}t.d(n,{A:()=>s});const s=function(){for(var e,n,t=0,s="",c=arguments.length;t<c;t++)(e=arguments[t])&&(n=o(e))&&(s&&(s+=" "),s+=n);return s}}},n={};function t(o){var s=n[o];if(void 0!==s)return s.exports;var c=n[o]={exports:{}};return e[o](c,c.exports,t),c.exports}t.n=e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return t.d(n,{a:n}),n},t.d=(e,n)=>{for(var o in n)t.o(n,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:n[o]})},t.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),t.o=(e,n)=>Object.prototype.hasOwnProperty.call(e,n),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;t.g.importScripts&&(e=t.g.location+"");var n=t.g.document;if(!e&&n&&(n.currentScript&&"SCRIPT"===n.currentScript.tagName.toUpperCase()&&(e=n.currentScript.src),!e)){var o=n.getElementsByTagName("script");if(o.length)for(var s=o.length-1;s>-1&&(!e||!/^http(s?):/.test(e));)e=o[s--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),t.p=e})();var o={};return(()=>{"use strict";t.r(o),t.d(o,{CONNECTION_STORE_ID:()=>e.Hx,ConnectButton:()=>e.pK,ConnectScreen:()=>e.F0,ConnectScreenLayout:()=>e.Jl,ConnectScreenRequiredPlan:()=>e.nM,ConnectUser:()=>e.mX,ConnectionError:()=>e.Rc,ConnectionErrorNotice:()=>e.JC,DisconnectCard:()=>e.Ni,DisconnectDialog:()=>e.bo,InPlaceConnection:()=>e.xW,ManageConnectionDialog:()=>e.AY,getCalypsoOrigin:()=>e.ag,thirdPartyCookiesFallbackHelper:()=>e.d1,useConnection:()=>e.w5,useConnectionErrorNotice:()=>e.Sx,useProductCheckoutWorkflow:()=>e.cS,useRestoreConnection:()=>e.Ob});var e=t(8980)})(),o})()));