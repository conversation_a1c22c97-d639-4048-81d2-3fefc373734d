{"name": "automattic/jetpack-waf", "description": "Tools to assist with the Jetpack Web Application Firewall", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"php": ">=7.2", "automattic/jetpack-connection": "^6.15.0", "automattic/jetpack-constants": "^3.0.8", "automattic/jetpack-ip": "^0.4.9", "automattic/jetpack-status": "^6.0.0", "wikimedia/aho-corasick": "^1.0"}, "require-dev": {"yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"files": ["cli.php"], "classmap": ["src/"]}, "scripts": {"phpunit": ["phpunit-select-config tests/php/integration/phpunit.#.xml.dist --colors=always", "phpunit-select-config tests/php/unit/phpunit.#.xml.dist --colors=always"], "test-coverage": "tests/action-test-coverage.sh", "test-coverage-html": ["php -dpcov.directory=. ./vendor/bin/phpunit-select-config tests/php/integration/phpunit.#.xml.dist --coverage-html ./coverage", "php -dpcov.directory=. ./vendor/bin/phpunit-select-config tests/php/unit/phpunit.#.xml.dist --coverage-html ./coverage"], "test-php": ["@test-php-integration", "@test-php-unit"], "test-php-unit": ["phpunit-select-config tests/php/unit/phpunit.#.xml.dist --colors=always"], "test-php-integration": ["phpunit-select-config tests/php/integration/phpunit.#.xml.dist --colors=always"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-waf", "textdomain": "jetpack-waf", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-waf/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "0.27.x-dev"}}, "config": {"allow-plugins": {"roots/wordpress-core-installer": true}}}