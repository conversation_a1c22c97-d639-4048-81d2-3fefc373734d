{"name": "automattic/jetpack-account-protection", "description": "Account protection", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"php": ">=7.2", "automattic/jetpack-connection": "^6.16.1", "automattic/jetpack-logo": "^3.0.5", "automattic/jetpack-constants": "^3.0.8", "automattic/jetpack-status": "^6.0.0"}, "require-dev": {"yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-test-environment": "@dev", "automattic/phpunit-select-config": "^1.0.3"}, "autoload": {"classmap": ["src/"]}, "scripts": {"build-development": "echo 'Add your build step to composer.j<PERSON>, please!'", "build-production": "echo 'Add your build step to composer.j<PERSON>, please!'", "phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": ["php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\""], "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "branch-alias": {"dev-trunk": "0.2.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-account-protection/compare/v${old}...v${new}"}, "mirror-repo": "Automattic/jetpack-account-protection", "textdomain": "jetpack-account-protection", "version-constants": {"::PACKAGE_VERSION": "src/class-account-protection.php"}}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}}