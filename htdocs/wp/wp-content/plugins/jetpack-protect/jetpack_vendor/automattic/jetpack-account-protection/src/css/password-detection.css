.password-detection-wrapper {
	background-color: #f0f0f1;
	min-width: 0;
	margin: 30px;
	color: #3c434a;
	font-family: -apple-system, system-ui, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 13px;
	line-height: 1.4;
}

.password-detection-content {
	background: #fff;
	max-width: 420px;
	margin: auto;
	padding: 26px 24px;
	font-weight: 400;
	overflow: hidden;
	border: 1px solid #c3c4c7;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);

	#jetpack-logo__icon {
		height: 30px;
	}
}

.password-detection-title {
	font-size: 24px;
	font-weight: 500;
}

.actions {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.action {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 36px;
	cursor: pointer;
	width: 100%;
	border-radius: 2px;
	font-size: 13px;
	text-decoration: none;
}

.action-input {
	height: 36px;
	cursor: pointer;
	width: 100%;
	box-sizing: border-box;
	text-indent: 8px;

	&::placeholder {
		font-size: 13px;
	}
}

.action-verify,
.action-update-password {
	margin-top: 10px;
	background-color: #00e;
	border: 2px solid #00e;
	color: #fff;
}

.action-proceed {
	background-color: #fff;
	border: 2px solid #00e;
	color: #00e;
}

a.risks-link,
a.resend-email-link {
	color: #00e;
}

.email-status {
	text-align: center;
}

.notice {
	width: 100%;
	display: flex;
	align-items: center;
	font-weight: 500;
	box-sizing: border-box;
	justify-content: center;
}

.notice-message {
	text-align: center;
	margin: 7px 0;
}

.error {
	background: #facfd2;
	border: 2px solid #e65054;
	color: #e65054;
}

.success {
	background: #d0e6b8;
	border: 2px solid #069e08;
	color: #069e08;
}

