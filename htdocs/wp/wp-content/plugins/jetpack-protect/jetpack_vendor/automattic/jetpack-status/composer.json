{"name": "automattic/jetpack-status", "description": "Used to retrieve information about the current status of Jetpack and the site overall.", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"php": ">=7.2", "automattic/jetpack-constants": "^3.0.8"}, "require-dev": {"brain/monkey": "^2.6.2", "yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-changelogger": "^6.0.5", "automattic/jetpack-connection": "@dev", "automattic/jetpack-plans": "@dev", "automattic/jetpack-ip": "^0.4.9", "automattic/phpunit-select-config": "^1.0.3"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": ["php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\""], "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-status", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-status/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "5.4.x-dev"}, "dependencies": {"test-only": ["packages/connection", "packages/plans"]}}}