{"name": "automattic/jetpack-plugins-installer", "description": "Handle installation of plugins from WP.org", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"php": ">=7.2", "automattic/jetpack-a8c-mc-stats": "^3.0.5", "automattic/jetpack-status": "^6.0.0"}, "require-dev": {"yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": ["php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\""], "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"branch-alias": {"dev-trunk": "0.5.x-dev"}, "mirror-repo": "Automattic/jetpack-plugins-installer", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-plugins-installer/compare/v${old}...v${new}"}, "autotagger": true, "textdomain": "jetpack-plugins-installer"}}