{"name": "automattic/jetpack-explat", "description": "A package for running A/B tests on the Experimentation Platform (ExPlat) in the plugin.", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"php": ">=7.2", "automattic/jetpack-connection": "^6.15.0"}, "require-dev": {"yoast/phpunit-polyfills": "^4.0.0", "automattic/jetpack-changelogger": "^6.0.5", "automattic/phpunit-select-config": "^1.0.3"}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["phpunit-select-config phpunit.#.xml.dist --colors=always"], "test-coverage": "pnpm concurrently --names php,js 'php -dpcov.directory=. ./vendor/bin/phpunit-select-config phpunit.#.xml.dist --coverage-php \"$COVERAGE_DIR/php.cov\"' 'pnpm:test-coverage'", "test-php": ["@composer phpunit"], "test-js": ["pnpm run test"], "test-js-watch": ["Composer\\Config::disableProcessTimeout", "pnpm run test --watch"], "build-development": ["pnpm run build"], "build-production": ["NODE_ENV=production pnpm run build"], "watch": ["Composer\\Config::disableProcessTimeout", "pnpm run watch"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "branch-alias": {"dev-trunk": "0.3.x-dev"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-explat/compare/v${old}...v${new}"}, "mirror-repo": "Automattic/jetpack-explat", "textdomain": "jetpack-explat", "version-constants": {"::PACKAGE_VERSION": "src/class-explat.php"}}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}}