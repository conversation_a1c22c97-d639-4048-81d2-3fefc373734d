<?php
// i18n-map.php @generated by automattic/jetpack-composer-plugin
return array(
  'domain' => 'jetpack-protect',
  'type' => 'plugins',
  'packages' => array(
    'jetpack-account-protection' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-account-protection',
      'ver' => '0.2.6',
    ),
    'jetpack-admin-ui' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-admin-ui',
      'ver' => '0.5.10',
    ),
    'jetpack-assets' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-assets',
      'ver' => '4.3.1',
    ),
    'jetpack-boost-core' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-boost-core',
      'ver' => '0.3.11',
    ),
    'jetpack-boost-speed-score' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-boost-speed-score',
      'ver' => '0.4.10',
    ),
    'jetpack-config' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-config',
      'ver' => '3.1.1',
    ),
    'jetpack-connection' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-connection',
      'ver' => '6.16.2',
    ),
    'jetpack-explat' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-explat',
      'ver' => '0.3.4',
    ),
    'jetpack-ip' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-ip',
      'ver' => '0.4.9',
    ),
    'jetpack-jitm' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-jitm',
      'ver' => '4.3.0',
    ),
    'jetpack-licensing' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-licensing',
      'ver' => '3.0.9',
    ),
    'jetpack-my-jetpack' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-my-jetpack',
      'ver' => '5.21.0',
    ),
    'jetpack-password-checker' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-password-checker',
      'ver' => '0.4.8',
    ),
    'jetpack-plugins-installer' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-plugins-installer',
      'ver' => '0.5.6',
    ),
    'jetpack-protect-models' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-protect-models',
      'ver' => '0.6.0',
    ),
    'jetpack-protect-status' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-protect-status',
      'ver' => '0.7.0',
    ),
    'jetpack-sync' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-sync',
      'ver' => '4.16.0',
    ),
    'jetpack-transport-helper' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-transport-helper',
      'ver' => '0.3.2',
    ),
    'jetpack-waf' => array(
      'path' => 'jetpack_vendor/automattic/jetpack-waf',
      'ver' => '0.27.1',
    ),
  ),
);
