This is a continuation of issue 6-************* . I was promised a refund on 25 USD there (for the old Google Play account), and nothing happened, after 6 months of talking. My emails in that thread are absurdly no longer answered.

Details:

- I'm a private person living in Poland (not a company).

- I had a Google Play developer account (id 8315026166336791468,  <EMAIL> ). Unfortunately, in 2025, Google decided to tell me "Because you earn money on Google Play, your full legal address will be shown publicly." (although the apps there were all free and without in-app purchases).

- In the support ticket 7-************* we established that the solution is for me to create a new developer account on Google Play, transfer there all apps, and I will be refunded for the old developer account. We did it. The new and active Google Play developer account id is 7891434499544220905 , <NAME_EMAIL> .

- Everything seems OK, but despite promises in issue 6-*************, I never received the refund of 25 USD. There are clear statements in that thread confirming that refund will be made (in 7-10 business days from March 17) but then nothing.

- The only answer I got was to look in https://pay.google.com/payments -- which shows that the payment from 2013 is *not* refunded by Google. I send 2 screenshots to the issue in 6-************* .

- As I emphasized in that thread, the original payment (from 2013) was done using a credit card that is expired now. I'm OK to get the refund in any way you like, even by just <NAME_EMAIL> . Or to my new credit card, which you have on file (since I used it to pay for new developer account).